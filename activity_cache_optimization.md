# 活动缓存优化方案

## 问题分析

在 `HomeSpecialAuth::special` 方法的执行过程中，发现存在大量重复的活动查询：

1. **限时购活动查询**：相同的 `limit_discount_id` 被重复查询多次
2. **N件折扣活动查询**：相同的 `n_discount_id` 被重复查询多次  
3. **满减活动查询**：相同的 `full_discount_id` 被重复查询多次
4. **活动商品关联查询**：相同的活动ID和商品ID组合被重复查询

从SQL日志可以看到，第49-70行中相同的折扣查询被执行了22次，严重影响性能。

## 优化方案

### 1. 添加活动信息缓存机制

在 `NetGoods` 类中添加了两个缓存方法：

#### `getActivityInfoWithCache()` - 活动信息缓存
```php
private function getActivityInfoWithCache($type, $activity_id, $membership, $owner, $model)
{
    $cache_key = "{$type}_info_{$activity_id}_{$membership}_{$owner}";
    
    // 内存缓存 + Redis缓存双重机制
    if (isset(self::$activityCache[$cache_key])) {
        return self::$activityCache[$cache_key];
    }
    
    $activity_info = redis($cache_key);
    if (empty($activity_info)) {
        // 只有缓存未命中时才执行数据库查询
        $_l_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
        $activity_info = $model->where(['id' => $activity_id])->where($_l_where)->find();
        if ($activity_info) {
            redis($cache_key, $activity_info, 1800); // 缓存30分钟
        }
    }
    
    self::$activityCache[$cache_key] = $activity_info;
    return $activity_info;
}
```

#### `getActivityGoodsInfoWithCache()` - 活动商品信息缓存
```php
private function getActivityGoodsInfoWithCache($type, $activity_id, $commodity_id, $model)
{
    $cache_key = "{$type}_goods_{$activity_id}_{$commodity_id}";
    
    // 内存缓存 + Redis缓存双重机制
    if (isset(self::$activityCache[$cache_key])) {
        return self::$activityCache[$cache_key];
    }
    
    $goods_info = redis($cache_key);
    if (empty($goods_info)) {
        $where_field = $type === 'limit_discount' ? 'limit_discount_id' : $type . '_id';
        $goods_info = $model->getOne(['where' => [$where_field => $activity_id, 'commodity_id' => $commodity_id]]);
        if ($goods_info) {
            redis($cache_key, $goods_info, 1800); // 缓存30分钟
        }
    }
    
    self::$activityCache[$cache_key] = $goods_info;
    return $goods_info;
}
```

### 2. 替换原有查询逻辑

#### 限时购活动优化
```php
// 优化前
$limit_info = $limit_model->where(['id' => $cheap_dis_id])->where($_l_where)->find();

// 优化后  
$limit_info = $this->getActivityInfoWithCache('limit_discount', $cheap_dis_id, $membership, $owner, $limit_model);
```

#### N件折扣活动优化
```php
// 优化前
$n_dis_info = $n_dis_model->where(['id' => $n_dis_id])->where($_l_where)->find();

// 优化后
$n_dis_info = $this->getActivityInfoWithCache('n_discount', $n_dis_id, $membership, $owner, $n_dis_model);
```

#### 满减活动优化
```php
// 优化前
$full_dis_info = $full_dis_model->where(['id' => $full_dis_id])->where($_l_where)->find();

// 优化后
$full_dis_info = $this->getActivityInfoWithCache('full_discount', $full_dis_id, $membership, $owner, $full_dis_model);
```

#### 活动商品信息优化
```php
// 优化前
$limit_goods_info = $limit_goods_model->getOne(['where' => ['limit_discount_id' => $cheap_dis_id, 'commodity_id' => $v['commodity_id']]]);

// 优化后
$limit_goods_info = $this->getActivityGoodsInfoWithCache('limit_discount', $cheap_dis_id, $v['commodity_id'], $limit_goods_model);
```

## 缓存策略

### 1. 双重缓存机制
- **内存缓存**：使用静态变量 `$activityCache` 存储当前请求周期内的数据
- **Redis缓存**：持久化缓存，跨请求共享，缓存时间30分钟

### 2. 缓存键设计
- 活动信息：`{type}_info_{activity_id}_{membership}_{owner}`
- 活动商品：`{type}_goods_{activity_id}_{commodity_id}`

### 3. 缓存穿透防护
**问题**：当查询结果为空时，缓存也是空的，导致每次都重新查询数据库

**解决方案**：
```php
// 使用特殊标记缓存空结果
if ($data) {
    // 有数据，正常缓存30分钟
    redis($cache_key, $data, 1800);
} else {
    // 无数据，缓存空结果标记5分钟
    redis($cache_key, '__EMPTY_RESULT__', 300);
}

// 读取时判断标记
$cached_data = redis($cache_key);
if ($cached_data === '__EMPTY_RESULT__') {
    return null; // 空结果
} else {
    return $cached_data; // 正常数据
}
```

**优势**：
- ✅ 避免缓存穿透，减少无效数据库查询
- ✅ 空结果缓存时间较短，避免长期缓存错误数据
- ✅ 区分"未缓存"和"查询结果为空"两种状态

### 4. 缓存失效策略
- **正常数据**：30分钟自动过期
- **空结果**：5分钟自动过期
- **手动清理**：提供缓存清理函数

## 性能提升预期

### 1. 查询次数减少
- **优化前**：每个商品都会查询活动信息，重复查询严重
- **优化后**：相同活动ID只查询一次，后续使用缓存

### 2. 响应时间优化
- **数据库查询时间**：从多次查询减少到首次查询
- **内存访问时间**：缓存命中时几乎为0
- **预期提升**：活动查询部分性能提升60-80%

### 3. 数据库负载降低
- 减少重复的SELECT查询
- 降低数据库连接占用时间
- 提高系统并发处理能力

## 测试验证

使用 `test_activity_cache.php` 脚本可以验证缓存机制是否正常工作：

```bash
php test_activity_cache.php
```

## 注意事项

1. **缓存一致性**：活动信息变更时需要清理相关缓存
2. **内存使用**：内存缓存会占用一定内存，但在请求结束后会自动释放
3. **Redis依赖**：确保Redis服务正常运行
4. **监控告警**：建议添加缓存命中率监控

## 新增缓存服务类

为了更好地管理缓存和防止缓存穿透，新增了 `CacheService` 类：

```php
// 单个查询
$activity_info = CacheService::getWithAntiPenetration($cache_key, function() {
    return $model->where(['id' => $activity_id])->find();
});

// 批量查询
$results = CacheService::batchGetWithAntiPenetration($cache_keys, function($missed_keys) {
    return $model->where('id', 'in', $missed_activity_ids)->select();
});
```

**特性**：
- ✅ 自动处理缓存穿透
- ✅ 支持批量查询优化
- ✅ 统一的缓存键生成
- ✅ 内存缓存 + Redis缓存双重机制

## 后续优化建议

1. **迁移到新缓存服务**：逐步将现有缓存逻辑迁移到 `CacheService`
2. **预热机制**：对于热门活动，可以考虑预热缓存
3. **缓存分层**：根据访问频率设置不同的缓存时间
4. **异步更新**：活动信息变更时异步更新缓存
5. **监控告警**：添加缓存命中率和穿透率监控
