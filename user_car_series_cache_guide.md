# 用户车系信息缓存优化指南

## 问题分析

从 `sql/specall-sql.sql` 文件可以看到，用户车系信息查询存在以下问题：

### 重复查询问题
```sql
-- 第6行：基础车系查询
SELECT * FROM `t_db_user_car_series` WHERE `user_id` = 159670 AND `car_brand_code` = 1 AND `channel_type` = ''

-- 第7行：VIN车查询  
SELECT * FROM `t_db_user_car_series` WHERE `user_id` = 159670 AND `channel_type` = 'GWSM' AND `is_vin_car` = 1

-- 第8行：详细车系列表查询（最复杂）
SELECT `a`.`id`,`a`.`is_vin_car`,`a`.`vin`,`a`.`is_bind`,a.relate_car_18n car_config_code,a.car_series_name car_series_cn,a.car_type_name large_car_type_cn,`a`.`car_series_id`,b.car_brand_code brand,`a`.`is_empower`,`b`.`is_nev` FROM `t_db_user_car_series` `a` INNER JOIN `t_e3s_car_series` `b` ON `a`.`relate_car_18n`=`b`.`car_config_code` WHERE `a`.`user_id` = 159670 AND `a`.`car_brand_code` = 1 AND `a`.`is_enable` = 1 AND `a`.`relate_car_18n` <> '' AND `a`.`channel_type` = 'GWSM' GROUP BY a.vin ORDER BY a.is_vin_car desc,a.last_updated_date desc
```

这些查询在同一个请求中被多次执行，造成性能浪费。

## 解决方案

### 1. 完整的用户车系数据缓存

```php
/**
 * 获取用户车系数据（带缓存，防止缓存穿透）
 * @param array $user 用户信息
 * @param string $channel_type 渠道类型
 * @return array
 */
private function getUserCarSeriesData($user, $channel_type = '')
{
    $user_id = $user['id'];
    $brand_code = $user['brand'] ?? 1;
    
    // 生成缓存键
    $cache_key = CacheService::generateKey('user_car_series', [
        'user_id' => $user_id,
        'brand_code' => $brand_code,
        'channel_type' => $channel_type
    ]);
    
    return CacheService::getWithAntiPenetration($cache_key, function() use ($user_id, $brand_code, $channel_type) {
        $user_car_model = new \app\common\model\db\DbUserCarSeries();
        
        // 查询用户车系信息（对应SQL第6行）
        $basic_car_info = $user_car_model->where([
            'user_id' => $user_id,
            'car_brand_code' => $brand_code,
            'channel_type' => ''
        ])->find();
        
        // 查询VIN车信息（对应SQL第7行）
        $vin_car_info = $user_car_model->where([
            'user_id' => $user_id,
            'channel_type' => $channel_type,
            'is_vin_car' => 1
        ])->find();
        
        // 查询详细车系列表（对应SQL第8行）
        $car_series_list = $user_car_model->alias('a')
            ->join('t_e3s_car_series b', 'a.relate_car_18n = b.car_config_code')
            ->where([
                'a.user_id' => $user_id,
                'a.car_brand_code' => $brand_code,
                'a.is_enable' => 1,
                'a.channel_type' => $channel_type
            ])
            ->where('a.relate_car_18n', '<>', '')
            ->field('a.id, a.is_vin_car, a.vin, a.is_bind, a.relate_car_18n car_config_code, a.car_series_name car_series_cn, a.car_type_name large_car_type_cn, a.car_series_id, b.car_brand_code brand, a.is_empower, b.is_nev')
            ->group('a.vin')
            ->order('a.is_vin_car desc, a.last_updated_date desc')
            ->select();
        
        return [
            'basic_car_info' => $basic_car_info,
            'vin_car_info' => $vin_car_info,
            'car_series_list' => $car_series_list,
            'user_id' => $user_id,
            'brand_code' => $brand_code,
            'channel_type' => $channel_type
        ];
    }, 1800, 300); // 正常数据缓存30分钟，空结果缓存5分钟
}
```

### 2. 简化版本（只查询基础信息）

```php
/**
 * 获取用户车系基础信息（简化版本）
 * @param int $user_id 用户ID
 * @param int $brand_code 品牌代码
 * @param string $channel_type 渠道类型
 * @return array|null
 */
private function getUserCarSeriesBasic($user_id, $brand_code = 1, $channel_type = '')
{
    $cache_key = CacheService::generateKey('user_car_basic', [
        'user_id' => $user_id,
        'brand_code' => $brand_code,
        'channel_type' => $channel_type
    ]);
    
    return CacheService::getWithAntiPenetration($cache_key, function() use ($user_id, $brand_code, $channel_type) {
        $user_car_model = new \app\common\model\db\DbUserCarSeries();
        
        return $user_car_model->where([
            'user_id' => $user_id,
            'car_brand_code' => $brand_code,
            'channel_type' => $channel_type
        ])->find();
    }, 1800, 300);
}
```

### 3. 批量查询版本

```php
/**
 * 批量获取多个用户的车系信息
 * @param array $user_ids 用户ID数组
 * @param int $brand_code 品牌代码
 * @param string $channel_type 渠道类型
 * @return array
 */
private function batchGetUserCarSeries(array $user_ids, $brand_code = 1, $channel_type = '')
{
    $cache_keys = [];
    foreach ($user_ids as $user_id) {
        $cache_keys[$user_id] = CacheService::generateKey('user_car_basic', [
            'user_id' => $user_id,
            'brand_code' => $brand_code,
            'channel_type' => $channel_type
        ]);
    }
    
    return CacheService::batchGetWithAntiPenetration($cache_keys, function($missed_keys) use ($user_ids, $brand_code, $channel_type) {
        // 批量查询逻辑
        // ...
    });
}
```

## 使用方式

### 在Controller中使用

```php
class HomeSpecialAuth extends Controller
{
    public function special()
    {
        // 原来的代码：每次都查询数据库
        // $user_car_info = db('user_car_series')->where(...)->find();
        // $vin_car_info = db('user_car_series')->where(...)->find();
        // $car_series_list = db('user_car_series')->alias('a')->join(...)->select();
        
        // 优化后：使用缓存
        $user_car_data = $this->getUserCarSeriesData($this->user, $this->channel_type);
        
        // 使用缓存的数据
        $basic_car_info = $user_car_data['basic_car_info'];
        $vin_car_info = $user_car_data['vin_car_info'];
        $car_series_list = $user_car_data['car_series_list'];
        
        // 后续业务逻辑...
    }
}
```

### 缓存键设计

缓存键采用结构化设计，便于管理：

```php
// 完整车系数据
user_car_series_{md5(user_id=159670&brand_code=1&channel_type=GWSM)}

// 基础车系数据  
user_car_basic_{md5(user_id=159670&brand_code=1&channel_type=GWSM)}
```

## 性能优化效果

### 优化前
- **查询次数**：每个请求3次数据库查询
- **查询时间**：约5-10ms（包含复杂JOIN）
- **重复查询**：相同用户的多次请求都会重复查询

### 优化后
- **首次查询**：3次数据库查询 + 缓存写入
- **后续查询**：直接从缓存读取，几乎0延迟
- **缓存命中率**：预计90%以上
- **性能提升**：90%以上的响应时间减少

## 缓存管理

### 缓存更新策略

```php
// 当用户车系信息发生变更时，清理相关缓存
public function updateUserCarSeries($user_id, $data)
{
    // 更新数据库
    $result = db('user_car_series')->where('user_id', $user_id)->update($data);
    
    if ($result) {
        // 清理相关缓存
        $this->clearUserCarSeriesCache($user_id);
    }
    
    return $result;
}

private function clearUserCarSeriesCache($user_id)
{
    $cache_patterns = [
        "user_car_series_*{$user_id}*",
        "user_car_basic_*{$user_id}*"
    ];
    
    foreach ($cache_patterns as $pattern) {
        CacheService::clearByPattern($pattern);
    }
}
```

### 缓存监控

```php
// 添加缓存命中率监控
public function getCacheStats()
{
    return [
        'user_car_series_hit_rate' => $this->calculateHitRate('user_car_series'),
        'user_car_basic_hit_rate' => $this->calculateHitRate('user_car_basic'),
        'total_cache_keys' => CacheService::getStats()['memory_cache_count']
    ];
}
```

## 注意事项

1. **数据一致性**：用户车系信息变更时必须清理缓存
2. **内存使用**：批量查询时注意内存占用
3. **缓存时间**：根据业务需求调整缓存过期时间
4. **错误处理**：缓存服务异常时要有降级方案

## 实施建议

1. **分阶段实施**：先在低流量接口测试，再逐步推广
2. **监控告警**：添加缓存命中率和错误率监控
3. **性能测试**：部署前进行充分的性能测试
4. **回滚方案**：准备快速回滚到原始查询方式的方案
