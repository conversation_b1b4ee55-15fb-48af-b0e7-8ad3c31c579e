<?php
/**
 * 用户车系信息缓存使用示例
 * 
 * 展示如何使用新的缓存方法来优化用户车系查询
 */

require_once 'application/common/net_service/NetGoods.php';
require_once 'application/common/service/CacheService.php';

use app\common\net_service\NetGoods;
use app\common\service\CacheService;

/**
 * 示例1: 在Controller中使用用户车系缓存
 */
class HomeSpecialAuthOptimized
{
    private $netGoods;
    
    public function __construct()
    {
        $this->netGoods = new NetGoods();
    }
    
    /**
     * 优化后的special方法
     */
    public function special()
    {
        $user = $this->getUser(); // 假设这是获取用户信息的方法
        $channel_type = 'GWSM';
        
        // 使用缓存获取用户车系数据，避免重复查询
        $user_car_data = $this->getUserCarSeriesDataWithCache($user, $channel_type);
        
        // 使用车系数据进行后续业务逻辑
        $this->processSpecialLogic($user_car_data);
        
        return $this->response(['code' => 0, 'data' => $user_car_data]);
    }
    
    /**
     * 获取用户车系数据（带缓存）
     */
    private function getUserCarSeriesDataWithCache($user, $channel_type)
    {
        $cache_key = CacheService::generateKey('user_car_series_complete', [
            'user_id' => $user['id'],
            'brand_code' => $user['brand'] ?? 1,
            'channel_type' => $channel_type
        ]);
        
        return CacheService::getWithAntiPenetration($cache_key, function() use ($user, $channel_type) {
            // 这里是原来的数据库查询逻辑
            return $this->queryUserCarSeriesFromDB($user, $channel_type);
        }, 1800, 300); // 30分钟缓存，空结果5分钟缓存
    }
    
    /**
     * 从数据库查询用户车系信息（原始逻辑）
     */
    private function queryUserCarSeriesFromDB($user, $channel_type)
    {
        $user_id = $user['id'];
        $brand_code = $user['brand'] ?? 1;
        
        // 对应SQL第6行查询
        $basic_car_info = db('user_car_series')->where([
            'user_id' => $user_id,
            'car_brand_code' => $brand_code,
            'channel_type' => ''
        ])->find();
        
        // 对应SQL第7行查询
        $vin_car_info = db('user_car_series')->where([
            'user_id' => $user_id,
            'channel_type' => $channel_type,
            'is_vin_car' => 1
        ])->find();
        
        // 对应SQL第8行查询
        $car_series_list = db('user_car_series')
            ->alias('a')
            ->join('t_e3s_car_series b', 'a.relate_car_18n = b.car_config_code')
            ->where([
                'a.user_id' => $user_id,
                'a.car_brand_code' => $brand_code,
                'a.is_enable' => 1,
                'a.channel_type' => $channel_type
            ])
            ->where('a.relate_car_18n', '<>', '')
            ->field('a.id, a.is_vin_car, a.vin, a.is_bind, a.relate_car_18n car_config_code, a.car_series_name car_series_cn, a.car_type_name large_car_type_cn, a.car_series_id, b.car_brand_code brand, a.is_empower, b.is_nev')
            ->group('a.vin')
            ->order('a.is_vin_car desc, a.last_updated_date desc')
            ->select();
        
        return [
            'basic_car_info' => $basic_car_info,
            'vin_car_info' => $vin_car_info,
            'car_series_list' => $car_series_list,
            'cache_time' => date('Y-m-d H:i:s'),
            'from_cache' => false
        ];
    }
    
    private function getUser()
    {
        // 模拟用户数据
        return [
            'id' => 159670,
            'brand' => 1,
            'openid' => 'test_openid'
        ];
    }
    
    private function processSpecialLogic($user_car_data)
    {
        // 处理业务逻辑
        echo "处理用户车系数据: " . json_encode($user_car_data, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    private function response($data)
    {
        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 示例2: 批量处理多个用户的车系信息
 */
function batchProcessUserCarSeries($user_ids, $brand_code = 1, $channel_type = 'GWSM')
{
    echo "=== 批量处理用户车系信息 ===\n";
    
    // 生成批量缓存键
    $cache_keys = [];
    foreach ($user_ids as $user_id) {
        $cache_keys[$user_id] = CacheService::generateKey('user_car_basic', [
            'user_id' => $user_id,
            'brand_code' => $brand_code,
            'channel_type' => $channel_type
        ]);
    }
    
    // 批量获取数据
    $results = CacheService::batchGetWithAntiPenetration($cache_keys, function($missed_keys) use ($user_ids, $brand_code, $channel_type) {
        echo "缓存未命中，需要查询数据库的键: " . count($missed_keys) . " 个\n";
        
        // 提取未命中的用户ID
        $missed_user_ids = [];
        foreach ($missed_keys as $key) {
            foreach ($user_ids as $user_id) {
                $expected_key = CacheService::generateKey('user_car_basic', [
                    'user_id' => $user_id,
                    'brand_code' => $brand_code,
                    'channel_type' => $channel_type
                ]);
                if ($key === $expected_key) {
                    $missed_user_ids[] = $user_id;
                    break;
                }
            }
        }
        
        // 批量查询数据库
        $db_results = db('user_car_series')->where([
            'user_id' => ['in', $missed_user_ids],
            'car_brand_code' => $brand_code,
            'channel_type' => $channel_type
        ])->select();
        
        // 按缓存键组织结果
        $organized_results = [];
        foreach ($db_results as $result) {
            $key = CacheService::generateKey('user_car_basic', [
                'user_id' => $result['user_id'],
                'brand_code' => $brand_code,
                'channel_type' => $channel_type
            ]);
            $organized_results[$key] = $result;
        }
        
        return $organized_results;
    });
    
    echo "批量处理完成，获取到 " . count($results) . " 条用户车系数据\n";
    return $results;
}

/**
 * 示例3: 缓存性能测试
 */
function testCachePerformance()
{
    echo "\n=== 缓存性能测试 ===\n";
    
    $user = ['id' => 159670, 'brand' => 1];
    $channel_type = 'GWSM';
    
    // 第一次查询（缓存未命中）
    $start_time = microtime(true);
    $controller = new HomeSpecialAuthOptimized();
    $result1 = $controller->getUserCarSeriesDataWithCache($user, $channel_type);
    $time1 = microtime(true) - $start_time;
    
    // 第二次查询（缓存命中）
    $start_time = microtime(true);
    $result2 = $controller->getUserCarSeriesDataWithCache($user, $channel_type);
    $time2 = microtime(true) - $start_time;
    
    echo "第一次查询时间: " . number_format($time1 * 1000, 2) . "ms (缓存未命中)\n";
    echo "第二次查询时间: " . number_format($time2 * 1000, 2) . "ms (缓存命中)\n";
    echo "性能提升: " . number_format(($time1 - $time2) / $time1 * 100, 2) . "%\n";
}

/**
 * 示例4: 缓存清理
 */
function clearUserCarSeriesCache($user_id, $brand_code = 1, $channel_type = 'GWSM')
{
    echo "\n=== 清理用户车系缓存 ===\n";
    
    $cache_keys = [
        CacheService::generateKey('user_car_series_complete', [
            'user_id' => $user_id,
            'brand_code' => $brand_code,
            'channel_type' => $channel_type
        ]),
        CacheService::generateKey('user_car_basic', [
            'user_id' => $user_id,
            'brand_code' => $brand_code,
            'channel_type' => $channel_type
        ])
    ];
    
    CacheService::clear($cache_keys);
    echo "已清理用户 {$user_id} 的车系缓存\n";
}

// 运行示例
if (php_sapi_name() === 'cli') {
    echo "用户车系缓存优化示例\n";
    echo "========================\n";
    
    // 示例1: 单个用户查询
    $controller = new HomeSpecialAuthOptimized();
    echo $controller->special() . "\n";
    
    // 示例2: 批量查询
    batchProcessUserCarSeries([159670, 159671, 159672]);
    
    // 示例3: 性能测试
    testCachePerformance();
    
    // 示例4: 缓存清理
    clearUserCarSeriesCache(159670);
}
