<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/17
 * Time: 6:29 PM
 */

namespace app\common\net_service;


use api\lianyou\Ccs;
use app\common\model\act\AcGongHuiInfo;
use app\common\model\act\AcGroup;
use app\common\model\act\AcHaveTradeList;
use app\common\model\act\AcPhonelist;
use app\common\model\act\AcSignCount;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuCheapSuitCommodity;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\bu\BuCheapSuitSub;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuShoppingCart;
use app\common\model\db\DbBdpRecommend;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbCommodityDes;
use app\common\model\db\DbCommodityDesType;
use app\common\model\db\DbCommodityExpand;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySegmentDis;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySub;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbCommoditySpecUnion;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCommodityTypeSegmentDiscount;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbCrowdfundAgreement;
use app\common\model\db\DbCrowdfundOrder;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFightGroupCommodity;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbGift;
use app\common\model\db\DbGiftCommodity;
use app\common\model\db\DbHomeType;
use app\common\model\db\DbHomeTypeCommodity;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbHomeSm;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountInfo;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbServerBag;
use app\common\model\db\DbSpecValue;
use app\common\model\db\DbSystemValue;
use app\common\model\e3s\E3sCarSeries;
use app\common\model\e3s\E3sParePartTime;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSparePart;
use app\common\port\connectors\CarLive;
use app\common\port\connectors\Website;
use think\Cache;
use app\task\controller\Car;
use think\Controller;
use think\Db;
use think\Exception;
use think\Model;
use think\Session;
use tool\Logger;
use app\common\service\CacheService;

class NetGoods extends Common
{
    /**
     * 活动信息缓存管理
     * @var array
     */
    private static $activityCache = [];

    /**
     * 获取活动信息（带缓存，防止缓存穿透）
     * @param string $type 活动类型 limit|n_dis|full_dis
     * @param int $activity_id 活动ID
     * @param string $membership 会员等级
     * @param string $owner 品牌标识
     * @param object $model 模型对象
     * @return array|null
     */
    private function getActivityInfoWithCache($type, $activity_id, $membership, $owner, $model)
    {
        $cache_key = "{$type}_info_{$activity_id}_{$membership}_{$owner}";

        // 先检查内存缓存
        if (isset(self::$activityCache[$cache_key])) {
            $cached_result = self::$activityCache[$cache_key];
            // 如果是空结果标记，返回null
            return $cached_result === '__EMPTY_RESULT__' ? null : $cached_result;
        }

        // 检查Redis缓存
        $activity_info = redis($cache_key);

        if ($activity_info === false || $activity_info === null) {
            // 缓存不存在，查询数据库
            $_l_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
            $activity_info = $model->where(['id' => $activity_id])->where($_l_where)->find();

            if ($activity_info) {
                // 有数据，正常缓存
                redis($cache_key, $activity_info, 1800); // 缓存30分钟
                self::$activityCache[$cache_key] = $activity_info;
            } else {
                // 无数据，缓存空结果标记，防止缓存穿透
                redis($cache_key, '__EMPTY_RESULT__', 300); // 空结果缓存5分钟
                self::$activityCache[$cache_key] = '__EMPTY_RESULT__';
                return null;
            }
        } else {
            // 缓存存在
            if ($activity_info === '__EMPTY_RESULT__') {
                // 是空结果标记
                self::$activityCache[$cache_key] = '__EMPTY_RESULT__';
                return null;
            } else {
                // 是正常数据
                self::$activityCache[$cache_key] = $activity_info;
            }
        }

        return $activity_info;
    }

    /**
     * 获取活动商品信息（带缓存，防止缓存穿透）
     * @param string $type 活动类型
     * @param int $activity_id 活动ID
     * @param int $commodity_id 商品ID
     * @param object $model 模型对象
     * @return array|null
     */
    private function getActivityGoodsInfoWithCache($type, $activity_id, $commodity_id, $model)
    {
        $cache_key = "{$type}_goods_{$activity_id}_{$commodity_id}";

        // 先检查内存缓存
        if (isset(self::$activityCache[$cache_key])) {
            $cached_result = self::$activityCache[$cache_key];
            // 如果是空结果标记，返回null
            return $cached_result === '__EMPTY_RESULT__' ? null : $cached_result;
        }

        // 检查Redis缓存
        $goods_info = redis($cache_key);

        if ($goods_info === false || $goods_info === null) {
            // 缓存不存在，查询数据库
            $where_field = $type === 'limit_discount' ? 'limit_discount_id' : $type . '_id';
            $goods_info = $model->getOne(['where' => [$where_field => $activity_id, 'commodity_id' => $commodity_id]]);

            if ($goods_info) {
                // 有数据，正常缓存
                redis($cache_key, $goods_info, 1800); // 缓存30分钟
                self::$activityCache[$cache_key] = $goods_info;
            } else {
                // 无数据，缓存空结果标记，防止缓存穿透
                redis($cache_key, '__EMPTY_RESULT__', 300); // 空结果缓存5分钟
                self::$activityCache[$cache_key] = '__EMPTY_RESULT__';
                return null;
            }
        } else {
            // 缓存存在
            if ($goods_info === '__EMPTY_RESULT__') {
                // 是空结果标记
                self::$activityCache[$cache_key] = '__EMPTY_RESULT__';
                return null;
            } else {
                // 是正常数据
                self::$activityCache[$cache_key] = $goods_info;
            }
        }

        return $goods_info;
    }

    /**
     * 使用新缓存服务获取活动信息（推荐使用）
     * @param string $type 活动类型
     * @param int $activity_id 活动ID
     * @param string $membership 会员等级
     * @param string $owner 品牌标识
     * @param object $model 模型对象
     * @return array|null
     */
    private function getActivityInfoWithCacheService($type, $activity_id, $membership, $owner, $model)
    {
        $cache_key = CacheService::generateKey("{$type}_info", [
            'activity_id' => $activity_id,
            'membership' => $membership,
            'owner' => $owner
        ]);

        return CacheService::getWithAntiPenetration($cache_key, function() use ($activity_id, $membership, $owner, $model) {
            $_l_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
            return $model->where(['id' => $activity_id])->where($_l_where)->find();
        });
    }

    /**
     * 批量获取活动信息（推荐使用）
     * @param string $type 活动类型
     * @param array $activity_ids 活动ID数组
     * @param string $membership 会员等级
     * @param string $owner 品牌标识
     * @param object $model 模型对象
     * @return array
     */
    private function batchGetActivityInfo($type, array $activity_ids, $membership, $owner, $model)
    {
        $cache_keys = [];
        foreach ($activity_ids as $activity_id) {
            $cache_keys[$activity_id] = CacheService::generateKey("{$type}_info", [
                'activity_id' => $activity_id,
                'membership' => $membership,
                'owner' => $owner
            ]);
        }

        return CacheService::batchGetWithAntiPenetration($cache_keys, function($missed_keys) use ($activity_ids, $membership, $owner, $model) {
            // 从缓存键中提取活动ID
            $missed_activity_ids = [];
            foreach ($missed_keys as $key) {
                foreach ($activity_ids as $id) {
                    $expected_key = CacheService::generateKey("{$type}_info", [
                        'activity_id' => $id,
                        'membership' => $membership,
                        'owner' => $owner
                    ]);
                    if ($key === $expected_key) {
                        $missed_activity_ids[] = $id;
                        break;
                    }
                }
            }

            // 批量查询数据库
            $_l_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
            $results = $model->where('id', 'in', $missed_activity_ids)->where($_l_where)->select();

            // 按缓存键组织结果
            $organized_results = [];
            foreach ($results as $result) {
                $key = CacheService::generateKey("{$type}_info", [
                    'activity_id' => $result['id'],
                    'membership' => $membership,
                    'owner' => $owner
                ]);
                $organized_results[$key] = $result;
            }

            return $organized_results;
        });
    }

    protected $sku_model;
    protected $sp_model;
    protected $com_model;
    protected $set_sku_model;
    protected $set_comm_model;

    protected $order_model;
    protected $user;
    protected $unionid;
    private $jk_one_point_good;

    private $car_vip = ['会员金卡', '会员金卡(VIP)', '会员金卡（VIP）', '会员银卡', '会员银卡VIP', '会员银卡（VIP）', '员工卡', '铂金卡', '黑卡']; //金银卡会员登记，用于积分兑换
    private $jk_goods = [2780, 2782, 2784, 2786, 2788, 2790];
    private $pk_goods = [2779, 2781, 2783, 2785, 2787, 2789];
    private $te_card_num = 9999999999;

    private function sj_ccs()
    {
        if (config('app_status') == "develop") {
            $ids = [2940, 2941];
        } else {
            $ids = [3610, 3611];
        }
        return $ids;
    }

    public function __construct()
    {
        parent::__construct();
        $this->sku_model = new DbCommoditySku();
        $this->set_sku_model = new DbCommoditySetSku();
        $this->sp_model = new DbSpecValue();
        $this->com_model = new DbCommodity();
        $this->order_model = new BuOrder();
//        $this->gong_hui_id = config('gong_hui.goods_id');
    }

    public function getCommoditySegmentDiscount($commodityId, $brand_id = 0, $refresh_cache = 0, $segment_arr = null)
    {
        $commodity_model = new DbCommodity();
        $commodity_vip_model = new DbCommoditySegmentDis();
        $goods_segment_type_model = new DbCommodityTypeSegmentDiscount();

        if (empty($brand_id)) {
            $brand_id = Session::has('net_api-brand') ? Session::get('net_api-brand') : 1;
        }
        $cache_key = "CommoditySegmentDis:{$brand_id}";
        if ($refresh_cache) {
            Cache::rm($cache_key);
        }
        $commodity_segment_arr = $segment_arr ?? Cache::get($cache_key);
        if ($commodity_segment_arr === false) {
            $segment_discount_ids = [];
            $commodity_type_ids = [];

            $commodity_segment_arr = [
                'commodity_dis_arr' => [],
                'commodity_arr' => []
            ];
            $goods_segment_types_arr = [];

            $goods_dis_items = $commodity_vip_model->where(['brand' => $brand_id, 'is_active' => 1, 'is_enable' => 1])
                ->field('id, user_segment, discount_type, discount_body, max_discount_amount')->select();
            if ($goods_dis_items) {
                foreach ($goods_dis_items as $goods_dis) {
                    $segment_discount_ids[] = $goods_dis['id'];
                    $commodity_segment_arr['commodity_dis_arr'][$goods_dis['id']] = $goods_dis;
                }

                $goods_segment_types = $goods_segment_type_model->where('segment_discount_id', 'in', $segment_discount_ids)
                    ->where(['brand' => $brand_id, 'is_enable' => 1])->field('commodity_type_id, segment_discount_id')->select();

                foreach ($goods_segment_types as $goods_segment_type) {
                    $commodity_type_ids[] = $goods_segment_type['commodity_type_id'];
                    $goods_segment_types_arr[$goods_segment_type['commodity_type_id']] = $goods_segment_type['segment_discount_id'];
                }

                $commodity_items = $commodity_model->field('id, commodity_name, comm_type_id')
                    ->where('comm_type_id', 'in', $commodity_type_ids)->select();
                foreach ($commodity_items as $commodity) {
                    $commodity_segment_arr['commodity_arr'][$commodity['id']] = $goods_segment_types_arr[$commodity['comm_type_id']];
                }
            }
            Cache::remember($cache_key, $commodity_segment_arr, 4 * 3600);
        }
        $segment_discount_id = $commodity_segment_arr['commodity_arr'][$commodityId] ?? 0;
        $goods_dis_info = $commodity_segment_arr['commodity_dis_arr'][$segment_discount_id] ?? [];
        $dis_info = [];
        if ($goods_dis_info) {
            $segment_info = get_user_segment_info();
            $membership = $segment_info['membership_level'];
            $owner = $segment_info['brand_owner_label'];

            $dis_arr = json_decode_assoc($goods_dis_info['discount_body']);
            if (($goods_dis_info['user_segment'] == 1 && !empty($dis_arr[$membership]))
                || ($goods_dis_info['user_segment'] == 2 && !empty($dis_arr[$owner]))) {
                $dis_info = [
                    'user_segment' => $goods_dis_info['user_segment'],
                    'discount_type' => $goods_dis_info['discount_type'],
                    'discount' => $dis_arr[$membership] ?? $dis_arr[$owner],
                    'max_discount_amount' => $goods_dis_info['max_discount_amount'],
                    'membership_level' => $membership,
                    'brand_owner_label' => $owner,
                ];
            }
        }
        return $dis_info;
    }

    public function getCommodityDisFinalPrice($dis_info, $price)
    {
        if ($dis_info['discount_type'] == 1) {
            $commodity_dis_final_price = round_bcmul($price, $dis_info['discount'] / 10);
        } else {
            $commodity_dis_final_price = round_bcsub($price, $dis_info['discount']);
        }
        if ($dis_info['max_discount_amount'] > 0 && (($price - $commodity_dis_final_price) > $dis_info['max_discount_amount'])) {
            $commodity_dis_final_price = round_bcsub($price, $dis_info['max_discount_amount']);
        }
        return sprintf("%.2f", max(0, $commodity_dis_final_price));
    }

    public function getLimitDisByPrice($limit_info, $sku_id = 0, $price = 0, $commodity_dis_info = false, $user_segment_info = null)
    {
        $segment_key = '';
        $commodity_dis_user_segment = 0;

        $m_price = $old_price = $price;
        if ($commodity_dis_info) {
            $m_price = $this->getCommodityDisFinalPrice($commodity_dis_info, $price);
        }

        $segment_info = $user_segment_info ?? get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];

        if ($limit_info['user_segment'] == 1) {
            $segment_key = $membership;
            $commodity_dis_user_segment = 1;
        } else if ($limit_info['user_segment'] == 2) {
            $segment_key = $owner;
            $commodity_dis_user_segment = 2;
        }

        $dis_price = $m_price;
        $current_price = $old_price;

        $have_sku_dis = 0;
        $limit_info_sku_dis = $limit_info['sku_dis_decoded'] ?? json_decode_assoc($limit_info['sku_dis']);
        if ($sku_id) {
            if (isset($limit_info_sku_dis[$sku_id])) {
                $discount_main = $limit_info_sku_dis[$sku_id];
                $have_sku_dis = 1;
            } else {
                $commodity_dis_user_segment = 0;
            }
        } else {
            $discount_main = current($limit_info_sku_dis);
            $have_sku_dis = 1;
        }
        if ($have_sku_dis) {
            if ($limit_info['user_segment']) {
                $discount = $discount_main[$segment_key];

                if ($limit_info['dis_type'] == 1) {
                    $discount = $discount / 10;
                    $dis_price = round_bcmul($m_price, $discount);
                } else {
                    $dis_price = round_bcsub($m_price, $discount);
                }
                if (isset($discount_main['NONE'])) {
                    $g_discount = $discount_main['NONE'];
                    if ($limit_info['dis_type'] == 1) {
                        $g_discount = $g_discount / 10;
                        $current_price = round_bcmul($old_price, $g_discount);
                    } else {
                        $current_price = round_bcsub($old_price, $g_discount);
                    }
                }
            } else {
                $discount = $discount_main;
                if ($limit_info['dis_type'] == 1) {
                    $discount = $discount / 10;
                    $dis_price = round_bcmul($m_price, $discount);
                    $current_price = round_bcmul($old_price, $discount);
                } else {

                    $dis_price = round_bcsub($m_price, $discount);
                    $current_price = round_bcsub($old_price, $discount);
                }
            }
        }
        return [
            'member_price' => $m_price,
            'dis_price' => sprintf("%.2f", max(0, $dis_price)),
            'current_price' => sprintf("%.2f", max(0, $current_price)),
            'user_segment' => $commodity_dis_user_segment,
            'have_sku_dis' => $have_sku_dis,
        ];
    }

    public function getSecKillDisByPrice($seckill_info, $sku_id = 0, $price = 0, $commodity_dis_info = false, $user_segment_info = null)
    {
        $segment_key = '';
        $commodity_dis_user_segment = 0;

        $m_price = $old_price = $price;
        if ($commodity_dis_info) {
            $m_price = $this->getCommodityDisFinalPrice($commodity_dis_info, $price);
        }

        $segment_info = $user_segment_info ?? get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];

        if ($seckill_info['user_segment'] == 1) {
            $segment_key = $membership;
            $commodity_dis_user_segment = 1;
        } else if ($seckill_info['user_segment'] == 2) {
            $segment_key = $owner;
            $commodity_dis_user_segment = 2;
        }

        $dis_price = $m_price;
        $current_price = $old_price;

        $seckill_info_sku_dis = json_decode_assoc($seckill_info['sku_dis']);
        if ($sku_id) {
            $discount_main = $seckill_info_sku_dis[$sku_id] ?? 0;
        } else {
            $discount_main = current($seckill_info_sku_dis);
//            try{
//                $discount_main = current($seckill_info_sku_dis);
//
//            }catch (Exception $e){
//                print_json($seckill_info);
//            }
        }
        if ($seckill_info['user_segment']) {
            $discount = $discount_main[$segment_key];

            if ($seckill_info['dis_type'] == 1) {
                $discount = $discount / 10;
                $dis_price = round_bcmul($m_price, $discount);
            } else if ($seckill_info['dis_type'] == 2) {
                $dis_price = round_bcsub($m_price, $discount);
            } else {
                $dis_price = round_bcmul($discount, 1);
            }
            if (isset($discount_main['NONE'])) {
                $g_discount = $discount_main['NONE'];
                if ($seckill_info['dis_type'] == 1) {
                    $g_discount = $g_discount / 10;
                    $current_price = round_bcmul($old_price, $g_discount);
                } else if ($seckill_info['dis_type'] == 2) {
                    $current_price = round_bcsub($old_price, $g_discount);
                } else {
                    $current_price = round_bcmul($g_discount, 1);
                }
            }
        } else {
            $discount = $discount_main;
            if ($seckill_info['dis_type'] == 1) {

                $discount = $discount / 10;
                $dis_price = round_bcmul($m_price, $discount);
                $current_price = round_bcmul($old_price, $discount);
            } else if ($seckill_info['dis_type'] == 2) {

                $dis_price = round_bcsub($m_price, $discount);
                $current_price = round_bcsub($old_price, $discount);
            } else {
                $dis_price = round_bcmul($discount, 1);
                $current_price = round_bcmul($discount, 1);
            }
        }
        return [
            'member_price' => $m_price,
            'dis_price' => sprintf("%.2f", max(0, $dis_price)),
            'current_price' => sprintf("%.2f", max(0, $current_price)),
            'user_segment' => $commodity_dis_user_segment,
        ];
    }

    public function getUserSegmentInfo($user)
    {
        $this->_getCarer($user['bind_unionid'], $user['member_id'], $user['one_id'], $this->channel_type);
    }


    /**
     * @param $commodity_id
     * @param $dlr_code
     * @param $preview
     * @param $prev_key
     * @param $fight_group_id
     * @param $shelves_type
     * @param $cheap_suit_id
     * @param $user
     * @param $dd_dlr_code
     * @param $sub_commodity_id
     * @param $kilometer
     * @param $is_gift
     * @param $gift_id
     * @param $use_discount
     * @param $entry_from
     * @param $act_type_id
     * @param $r_set_sku_id
     * @param $user_vin  如果是从保养推荐过来则带上这个VIN,通过VIN+商品ID获取sku，组合商品则不判断
     * @return array|false|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCommodityInfo($commodity_id = 0, $dlr_code = '', $preview = 0, $prev_key = '', $fight_group_id = 0, $shelves_type = 2, $cheap_suit_id = 0, $user = [], $dd_dlr_code = '', $sub_commodity_id = 0, $kilometer = 0, $is_gift = 0, $gift_id = 0, $use_discount = 1, $entry_from = 1, $act_type_id = 1, $r_set_sku_id = 0,$user_vin='',$set_sku_id_arr=[],$commodity_set_id=0,$form_no_card_list = 0)
    {
        $this->channel_type = $dlr_code;
        //$user_vin
        $commodity_info = '';
        if (empty($commodity_info)) {
            $limit_sku = [];
            $limit_row = [];
            $seckill_row = [];

            $dlr_level = '';
            if (empty($preview)) {   //非预览 获取默认值
                // $commodity_id=50;
                //获取商品上架信息


                if($commodity_set_id){
                    $com_where = ['d.commodity_set_id' => $commodity_set_id];
                }else{
                    $com_where = ['a.id' => $commodity_id];
                    $com_where[] = ['exp', " (find_in_set('{$dlr_code}',d.up_down_channel_dlr)) "];
                }
                $com_row = $this->com_model->getCommodityInfo($com_where);

//                dd($this->com_model->getLastSql());
                if (!$com_row) {
                    return array();
                }
                $commodity_id = $com_row['id'];
                $bdp_sku_id_list = [];
                if($user_vin && !$sub_commodity_id){

                    $bdp_model =  new DbBdpRecommend();
                    $bdp_one = $bdp_model->getOne(['where'=>['vin'=>$user_vin]]);
                    if($bdp_one){
                        $bdp_goods_list = json_decode($bdp_one['goods'], true);
                        foreach ($bdp_goods_list as $bdp_v){
                            if($bdp_v['commodity_id']==$commodity_id){
                                $bdp_sku_id_list=explode(',',$bdp_v['set_sku_id']);
                            }
                        }
                    }
                }
                if($set_sku_id_arr){
                    $bdp_sku_id_list = $set_sku_id_arr;
                }


                //0423版本
                if (!empty($sub_commodity_id) && !empty($com_row['group_commodity_ids_info'])) {
                    $group_commodity_ids_arr = json_decode($com_row['group_commodity_ids_info'], true);
                    $sku_tmp_list = [];
                    foreach ($group_commodity_ids_arr as $group_commodity_ids_item) {
                        if ($sub_commodity_id == $group_commodity_ids_item['commodity_id']) {
                            foreach ($group_commodity_ids_item['sku_list'] as $sku_item) {
                                $sku_tmp_list[] = $sku_item['group_sub_set_sku_id'];
                            }
                        }
                    }
                    if (!empty($sku_tmp_list)) {
                        $stocktmp = $this->set_sku_model->whereIn('id', $sku_tmp_list)->sum('stock');
                        $com_row['count_stock'] = $stocktmp;
                    }
                }

                $user_info = $this->getFriendBaseInfo($user);
                $user_vin = $user_info['vin'];
                $is_vin_car = $user_info['user_status'];//是否VIN车
                $com_row['car_number'] = $user_info['car_number'];//vin车数
//                $user_vin = '';//用户vin车 用当前车获取价格，不需要默认车，
//                if($user_info['user_status']==2){
//                    $user_vin = $user_info['vin'];
//                }
                //众筹情况下不返回车主价
                if ($com_row['listing_type'] == 1) {
                    $commodity_dis_info = $this->getCommoditySegmentDiscount($commodity_id);
                } else {
                    $commodity_dis_info = [];
                }

                $maintain_type = '';
                $maintain_can_buy = 1;
                $maintain_can_buy_word = '';
                $maintain_upgrade_type = 0;
                $maintain_times = 0;
                $maintain_num = 0;
                $is_by_pp = 1;//保养是否匹配
                $oil_type = '';
                $is_by_tc_3 = 0;
                //oli_liters
                $not_pp_car_where = ['a.commodity_set_id' => $com_row['commodity_set_id'], 'a.is_enable' => 1];
                if($bdp_sku_id_list){
                    $not_pp_car_where['a.id']=['in',$bdp_sku_id_list];
                }
                if (in_array($com_row['dd_commodity_type'], [1, 3, 4, 41,12])) {
                    if (in_array($dlr_code, ['QCSM', 'QCAPP']) && $user_info['18_oil_type'] == 4) {
                        $oil_type = '3.5,4';
                    } else {
                        $oil_type = $user_info['18_oil_type'];
                    }
                    $not_pp_car_where['b.oli_liters'] = ['in', $oil_type];
//                    $data_car_where .=  sprintf(" and (a.oli_liters in (%s) || a.oli_liters='')",$oil_type);
                    $is_by_tc_3 = 1;
                }
                //"1保养套餐-老友惠保养套餐,3保养套餐-心悦保养套餐,4保养套餐-五年双保升级权益套餐.
                if (in_array($com_row['dd_commodity_type'], [1, 3,12])) {
                    $is_by_pp = 0;//保养是否匹配
                    $dlr_level = 'A';
//                    $maintain_type = '8.0';
                    if (!empty($dd_dlr_code) && $dd_dlr_code != 'V0000') {
                        $dlrObj = new DbDlr();
                        $dlr_info = $dlrObj->alias("a")->join("t_db_area b", "a.area_id=b.area_id")->where(['a.dlr_code' => $dd_dlr_code, 'a.is_enable' => 1])->field("b.city_type,a.dlr_name,b.brand_city_type")->find();
                        if (in_array($dlr_code, ['QCSM', 'QCAPP'])) {
                            $dlr_level = $dlr_info['brand_city_type'];
                        } else {
                            $dlr_level = $dlr_info['city_type'];
                        }
                        $is_by_pp = 1;
                    }
                    if ($com_row['dd_commodity_type'] == 3) {
                        $is_by_pp = 0;//保养是否匹配
                        $maintain_type_info = $this->vehicleAge2($user_info['vin'], $kilometer);//https://app.mockplus.cn/s/NQRdQQDR1?请查看《201901-DFN-NI+商城》 按照产品说法这里用当前vin.
                        $maintain_can_buy = $maintain_type_info['can_buy'];
                        $maintain_can_buy_word = $maintain_type_info['can_buy_word'];
                        $maintain_type = $maintain_type_info['type'] ? $maintain_type_info['type'] : '8.0';
                        $not_pp_car_where['b.maintain_q'] = $maintain_type;

                    }
//                    AND ( ( find_in_set( 'A', c.city_type ) || c.city_type = '' || c.city_type IS NULL ) )
//	AND ( ( d.upgrade_type = 0 || d.upgrade_type = '0' ) )
//                    AND ( (( d.maintain_q = '8.0' || d.maintain_q = '' ) || a.dd_commodity_type <> 3 ) )
//                    AND ( d.oli_liters IN ( 3.5, 4 ) || d.oli_liters = '' )
                    $not_pp_car_where['a.city_type'] = $dlr_level;
                }
                if ($com_row['dd_commodity_type'] == 4) {
                    $is_by_pp = 0;//保养是否匹配
                    $maintain_type_info = $this->maintion_info($user_info['vin']);//https://app.mockplus.cn/s/NQRdQQDR1?请查看《201901-DFN-NI+商城》 按照产品说法这里用当前vin.
                    // $data =['can_buy'=>0,'upgrade_type'=>$upgrade_type,'times'=>$times,'can_buy_word'=>'无升级次数'];
                    $maintain_can_buy = $maintain_type_info['can_buy'];
                    $maintain_can_buy_word = $maintain_type_info['can_buy_word'];
                    $maintain_upgrade_type = $maintain_type_info['upgrade_type'];
                    $maintain_times = $maintain_type_info['times'];
                    $maintain_num = $maintain_type_info['num'];
                    $not_pp_car_where['b.maintain_num'] = $maintain_times >= 3 ? $maintain_times : 3;

                }

                if ($com_row['dd_commodity_type'] == 12) {
                    $is_by_pp = 0;//保养是否匹配
                    $maintain_type_info = $this->maintion_info($user_info['vin'],'jiexu');//https://app.mockplus.cn/s/NQRdQQDR1?请查看《201901-DFN-NI+商城》 按照产品说法这里用当前vin.
                    $maintain_is_sb = $maintain_type_info['is_sb'];
                    $maintain_can_buy_word = $maintain_type_info['can_buy_word'];
                    if($maintain_is_sb){
                        $maintain_can_buy = 1;
                    }else{
                        $maintain_can_buy = 0;
                        $maintain_can_buy_word='此套餐为五年双保车主专享';
                    }
                    $maintain_type = '7.0';
                    $not_pp_car_where['b.maintain_q'] = $maintain_type;

                }

                //
                $not_pp_car_info = $this->set_sku_model->alias('a')->join('t_db_commodity_sku b', 'a.commodity_sku_id=b.id')->where($not_pp_car_where)->order('a.price')->field('a.*,b.maintain_q')->find();
//                print_json($not_pp_car_info);
//                echo $this->set_sku_model->getLastSql();die();
                $com_row['dis_p_s_old'] = $com_row['dis_p_s'];
                if ($not_pp_car_info) {
                    $com_row['maintain_dis'] = $not_pp_car_info['maintain_q'];
                    $com_row['dis_p_s'] = $not_pp_car_info['price'];
//                    dd($not_pp_car_info['maintain_q']);
                    if ($not_pp_car_info['maintain_q']) {
                        $com_row['dis_p_s_old'] = sprintf("%.2f", bcdiv($com_row['dis_p_s'], $not_pp_car_info['maintain_q'] / 10, 0));
                    }
                }

                if (in_array($com_row['dd_commodity_type'], [3, 4,12]) && $maintain_can_buy == 1) {
                    $is_by_pp = 1;
                }
                if (in_array($com_row['dd_commodity_type'], [3, 4,12]) && $is_vin_car <> 2) {
                    $is_by_pp = 0;
                }
                $com_row['maintain_can_buy'] = $maintain_can_buy;
                $com_row['is_vin_car'] = $is_vin_car;
                $com_row['maintain_can_buy_word'] = $maintain_can_buy_word;
//                dd($com_row);
                if ($maintain_can_buy == 1) {
                    $com_row['maintain_times'] = $maintain_num < 0 ?: $maintain_num;
                } else {
                    $com_row['maintain_times'] = 0;
                }
                $com_row['commodity_dis_user_segment'] = 0;
                $com_row['commodity_dis_act_user_segment'] = 0;
                $com_row['commodity_dis_label'] = '';
                $com_row['commodity_dis_label_cn'] = '';
                $com_row['current_price'] = sprintf("%.2f", $com_row['dis_p_s_old']);
                $com_row['original_price_range_end'] = $com_row['original_price_range_start'] = sprintf("%.2f", $com_row['dis_p_s_old']);
                $com_row['discount_price_range_end'] = $com_row['discount_price_range_start'] = sprintf("%.2f", $com_row['dis_p_s']); //只要一个最低价，20220712 Tzl
                $goods_max_point_arr = $this->goods_point_js(['commodity_id' => $commodity_id, 'price' => $com_row['dis_p_s'], 'channel_type' => $dlr_code]);
                $com_row['max_point'] = $goods_max_point_arr['point'];
                $com_row['pay_com'] = $goods_max_point_arr['pay_com'];
                //                $com_row['discount_price_range_end']   = $com_row['dis_p_e'];
                //                $com_row['point_discount'] = '';
                $com_row['factory_points'] = 0;
                $com_row['de_sku_id'] = 0;
                $com_row['de_sku_key'] = 0;
                $price_ret = 0;
                $service_channel_labels = [];
                if (!empty($com_row['service_channel'])) {
                    $system_val_model = new DbSystemValue();
                    $service_channels = $system_val_model->getNameList('23');
                    $service_channel_ids = explode(',', $com_row['service_channel']);
                    foreach ($service_channel_ids as $channel_id) {
                        if (isset($service_channels[$channel_id])) {
                            $service_channel_labels[] = $service_channels[$channel_id];
                        }
                    }
                }
                $com_row['service_channel_labels'] = $service_channel_labels;

                if ($dd_dlr_code) {
                    if ($com_row['work_hour_type'] && $user['car_18n'] && $dd_dlr_code) {
                        $ww_price_data = [
                            'dlr_code' => $dd_dlr_code,
                            'car_config_code' => $user['car_18n'],
                            'repair_type' => $com_row['work_hour_type'],
                        ];
                        $price_ret = $this->set_sku_model->getE3sPrice($ww_price_data);
                    }
                }
                $com_row['work_time_price'] = $price_ret; //工时单价跟着主商品走


                //7.车系信息 车型晚点做
                $com_row['user_car_date'] = '';
                if ($user['car_series_id']) {
                    $com_row['user_car_series'] = $user_info['car_type_name'];
                    $com_row['user_car_date'] = $user_info['car_offline_date'];//车型下架时间
                }
                if (in_array($com_row['pay_style'], [1, 3])) {
                    $com_row['factory_points'] = 1;
                }
                //获取规格列表-- 正常来讲，列表页面进行了过滤，就不会到详情页有没车型的情况
                $detail_list_key = 'detail_set_sku_list_key66' . $com_row['commodity_set_id'] . $com_row['commodity_id'] . $com_row['last_updated_date'] . $user['car_series_id'] . $sub_commodity_id;
                //                $list            = redis($detail_list_key);
                $list = '';
                $relate_car = 0;
                $is_mate = 0;
                //因为flat的定时任务跑18N太废资源，用到店类型判断是否需要车型，2023-05-26 11:01:59 T确认
                if ($com_row['dd_commodity_type']) {
//                    if(date('Ymd')>='20240610'){
//                        $relate_car = 1;
//                    }
                    $relate_car = 1;

                }
                if ($sub_commodity_id) {
                    $sub_com_row = $this->com_model->getOneByPk($sub_commodity_id);
                    $com_set_model = new DbCommoditySet();
                    $sub_where = ['commodity_id' => $sub_commodity_id, 'is_enable' => 1];
                    $sub_where[] = ['exp', " (find_in_set('{$dlr_code}',up_down_channel_dlr)) "];

                    $sub_set_info = $com_set_model->getUpCommodity($sub_commodity_id,$dlr_code);
                    if (!$sub_set_info) {
                        $com_row['off_shelf'] = 1;
                    }

                } else {
                    $sub_com_row['machine_oil_type'] = 0;
                }
                //                dd($sub_com_row);
                $set_sku_ids = [];


                if (empty($list) || getRedisLock($detail_list_key . '-lock', 60)) {
                    //判断是否需要城市级别价格
                    $sp_v_list_arr = [];
                    $sp_v_list_sku_arr = [];
                    //"1保养套餐-老友惠保养套餐,3保养套餐-心悦保养套餐,4保养套餐-五年双保升级权益套餐
                    if (in_array($com_row['dd_commodity_type'], [10])) {
                        $dlr = (new DbDlr())->getOne(['field' => 'dlr_level_grand', 'where' => ['dlr_code' => $dd_dlr_code]]);
                        $dlr_level = $dlr['dlr_level_grand'] ?? '';
                    }
                    //新增判断是否积分专区
                    if ($com_row['is_integral_shop'] == 1) {
                        $car_info = $this->_getCarer($user['bind_unionid'], $user['member_id'], $user['one_id'], $dlr_code);
                        Logger::error('integral_car:', ['car_info' => json_encode($car_info)]);
                        $list = $this->sku_model->newGetSetSkuByCommoditySetId($com_row['commodity_set_id'], $com_row['commodity_id'], $user['car_series_id'], $sub_commodity_id, $dlr_level, 1, $car_info['card_degree_name'] ?? '会员普卡', $com_row['user_car_date']);
                    } else {
                        //有联动则查出了所有的子商品再进行联动处理
                        //没有联动就带入子商品ID
                        if ($sub_commodity_id && !empty($com_row['is_sp_associated'])) {
                            $sub_commodity_list_arr = $this->sku_model->getSetSkuByCommoditySetId($com_row['commodity_set_id'], $com_row['commodity_id'], $user['car_series_id'], 0, $dlr_level, $com_row['user_car_date'], $maintain_type, $maintain_upgrade_type, $maintain_times, $oil_type, $dd_dlr_code,$bdp_sku_id_list);
//                            if($commodity_set_id){
//                                echo $this->sku_model->getLastSql();die();
//                            }
//                            print_json($sub_commodity_list_arr);
//                            dd($this->sku_model->getLastSql());
                            $filtered_result = $this->commodityAssociateFilter($com_row['commodity_set_id'], $sub_commodity_list_arr);
//                            print_json($filtered_result);
                            $list = array_filter($filtered_result['sub_commodity_list_arr'], function ($item) use ($sub_commodity_id) {
                                return $item['sub_commodity_id'] == $sub_commodity_id;
                            });
                        } else {
                            $list = $this->sku_model->getSetSkuByCommoditySetId($com_row['commodity_set_id'], $com_row['commodity_id'], $user['car_series_id'], $sub_commodity_id, $dlr_level, $com_row['user_car_date'], $maintain_type, $maintain_upgrade_type, $maintain_times, $oil_type, $dd_dlr_code,$bdp_sku_id_list);
                        }
                    }
//                    echo $this->sku_model->getLastSql();die();
                    if($commodity_set_id){
//                        echo $this->sku_model->getLastSql();die();
                    }
//                                        echo $this->sku_model->getLastSql();die();
                    //没车型会有最低价，
                    //添加赠品券判断是否有
                    $gift_card_sku_arr=[];
                    $gift_card_sku_class_arr=[];
                    if(isset($user['card_r_gift_wjh'])){
                        if($user['card_r_gift_wjh']){
                            $wjh_card_act_id = array_column($user['card_r_gift_wjh'],'activity_id');
                            $gift_card_rule_list= $this->gift_card_rule($wjh_card_act_id);
                            $gift_card_sku_arr          = $gift_card_rule_list['gift_card_sku_arr'];
                            $gift_card_sku_class_arr    = $gift_card_rule_list['gift_card_sku_class_arr'];
                        }

                    }

                    $list = collection($list)->toArray();
                    Logger::error('goodsskulistsku', ['sql' => $this->sku_model->getLastSql()]);
                    //要判断商品是否没匹配车型..。。

                    if ($list) {
//                        $re_part_list = [];
                        $re_part_list = array_column($list, 'sku_code');
                        foreach ($list as $kkk => $vvv) {
                            if($vvv['relate_car_ids']){
                                $com_row['relate_car_ids']=$vvv['relate_car_ids'];
                            }
                            //把工时拿出来
//                            if(isset($vvv['rep_part_no']) && !empty($vvv['rep_part_no'])){
//                                $re_part_list[$kkk] = explode(',', $vvv['rep_part_no']);
//                            }
                            //如果关联了替换件sku的，那么自身就隐藏
//                            if(isset($vvv['re_sku_code']) && !empty($vvv['re_sku_code'])){
                            $vv_sku_re_no_arr = explode(',', $vvv['rep_part_no']);
                            if (array_intersect($vv_sku_re_no_arr, $re_part_list) && !empty($vvv['rep_part_no'])) {
                                unset($list[$kkk]);
                            } else {
                                $sp_v_list_arr[$vvv['sp_value_list'] . $vvv['sub_commodity_id']][] = $kkk;
                                $sp_v_list_sku_arr[$vvv['sp_value_list']][] = $vvv['sku_code'];
                                $list[$kkk]['wi_code'] = '';
                                $list[$kkk]['work_time_number'] = 0;
                                $wi_qty = 0;
                                if ($vvv['relate_car_work_hour']  && $com_row['work_hour_type']  && ($com_row['is_grouped'] <> 1 || $sub_commodity_id)) {
                                    $relate_car_work_hour = json_decode($vvv['relate_car_work_hour'], true);
                                    foreach ($relate_car_work_hour as $kk => $vv) {
                                        if (in_array($user['car_18n'], $vv)) {
                                            $work_model = new E3sPartCarSeries();
                                            $work_info = $work_model->getOneByPk($kk);
                                            //写入备件号
                                            if ($work_info) {
                                                $wi_code = $work_info['wi_code'];
                                                $wi_qty = $work_info['wi_qty']; //工时数量
                                            }
                                            break;
                                        }
                                    }
                                }
                                //加个前置条件：商品工时类别为空，不需要展示工时 TZL && $com_row['machine_oil_type']<>1
                                if ($vvv['relate_car_work_hour'] && $com_row['work_hour_type'] && $user['car_18n'] && $sub_com_row['machine_oil_type'] <> 1 && $wi_qty) {
                                    $list[$kkk]['work_time_price'] = $price_ret;
                                    $list[$kkk]['wi_code'] = $wi_code;
                                    $list[$kkk]['work_time_number'] = $wi_qty; //工时数量
                                }
//                                unset($list[$kkk]['relate_car_18n']);
                                unset($list[$kkk]['relate_car_work_hour']);
                            }
                            $set_sku_ids[] = $vvv['id'];
                        }
//                        //过滤之后如果还有规格》1的则整个都规格都不要显示了 todo 不需要再进行过滤了，直接请求出来
//                        foreach ($sp_v_list_arr as $sp_kkk => $sp_vvv) {
//                            if (count($sp_vvv) > 1) {
//                                //记录表并且过滤对应的ID
//                                $this->error_vin_goods(isset($user_info['vin']) ? $user_info['vin'] : $user['id'], ['sp' => $sp_kkk, 'goods_id' => $commodity_id], 'd_d');
//                                foreach ($sp_vvv as $sp_vvv_kkk => $sp_vvv_val) {
//                                    if (!empty($sp_v_list_sku_arr[$sp_kkk][$sp_vvv_kkk])) {
//                                        unset ($list[$sp_vvv_val]);
//                                    }
//                                }
//                            }
//                        }
//                        redis($detail_list_key, $list, 80);
                    } else {
                    }
                }
//                Logger::error('goodsskulistsku2',['list2'=>json_encode($list)]);

                if ($relate_car == 1 && $is_mate == 1) {
                    $is_mate = 2;
                }
                //                print_json($list);
                $com_row['relate_car'] = $relate_car;

                $com_row['is_have_car'] = 1;
                if ($relate_car == 1 && empty($user['car_series_id'])) {
                    $com_row['is_have_car'] = 0;
                }

                $ac_dis_type = 0;
                $ac_dis_count = [];
                $act_is_user_segment = 0;

                if ($com_row['limit_dis']) {
                    $limit_dis_arr = json_decode($com_row['limit_dis'], true);

                    $limit_dis_id = isset($limit_dis_arr[$dlr_code][0]) ? $limit_dis_arr[$dlr_code][0] : '';
                    if ($limit_dis_id) {
                        $com_row['limit_dis_id'] = $limit_dis_id;
                        $limit_model = new DbLimitDiscountCommodity();
                        $limit_row = $limit_model->getLimitDisComInfo($com_row['commodity_id'], $limit_dis_id);

                        if ($limit_row) {
                            if (in_array($limit_row['discount_type'], [1, 3]) && in_array($act_type_id, [1, 10])) {
                                $ac_dis_type = $limit_row['dis_type'];
                                $ac_dis_count = json_decode($limit_row['sku_dis'], true);//阶梯，以json格式
                                $act_is_user_segment = $limit_row['user_segment'];
                            }

                            $limit_sku = json_decode($limit_row['sku_price'], true);
                            $show_limit_label = 0;
                            foreach ($limit_sku as $sku_key => $sku_val) {
                                if (in_array($sku_key, $set_sku_ids)) {
                                    $show_limit_label = 1;
                                    break;
                                }
                            }
                            if ($show_limit_label == 0) {
                                $com_row['limit_dis_id'] = 0;
                            }
                        } else {
                            $com_row['limit_dis'] = '';
                            $com_row['limit_dis_id'] = '';
                        }
                    }
                }

                if ($com_row['crowdfund_dis']) {
                    $crowdfund_dis_arr = json_decode($com_row['crowdfund_dis'], true);

                    $crowdfund_dis_id = isset($crowdfund_dis_arr[$dlr_code][0]) ? $crowdfund_dis_arr[$dlr_code][0] : 0;
                    $com_row['crowdfund_dis_id'] = $crowdfund_dis_id;
                }
                $show_kk_point = 1;//展示limit_price对应积分
                // 秒杀--对应限时购字段
                if ($com_row['seckill_dis']) {
                    $seckill_dis_arr = json_decode($com_row['seckill_dis'], true);
                    $seckill_dis_id = isset($seckill_dis_arr[$dlr_code][0]) ? $seckill_dis_arr[$dlr_code][0] : '';
                    if ($seckill_dis_id) {
                        $com_row['seckill_dis_id'] = $seckill_dis_id;
                        $seckill_model = new DbSeckillCommodity();

                        $seckill_row = $seckill_model->getSeckillDisComInfonoTime($com_row['commodity_id'], $seckill_dis_id);
                        if ($seckill_row) {
                            if (in_array($seckill_row['discount_type'], [1, 3]) && in_array($act_type_id, [1, 10])) {
                                if ($seckill_row['act_status'] == 2) {
                                    $ac_dis_type = $seckill_row['dis_type'];
                                    $ac_dis_count = json_decode_assoc($seckill_row['sku_dis']);
                                }
                                $act_is_user_segment = $seckill_row['user_segment'];
                            }

                            $seckill_sku = $limit_sku = json_decode($seckill_row['sku_price'], true);
//                            dd($seckill_sku);
                            $seckill_stock = json_decode($seckill_row['sku_stock'], true);
                            //秒杀判断--can_number小于等0或者miaosha_stock小于等于0 且总库存大于0，就是原价购买 seckill_info.act_status === 1
                            $seckill_sku = json_decode($seckill_row['sku_price'], true);
                            $show_seckill_label = 0;
                            foreach ($seckill_sku as $sku_key => $sku_val) {
                                if (in_array($sku_key, $set_sku_ids)) {
                                    $show_seckill_label = 1;
                                    break;
                                }
                            }
                            if ($show_seckill_label == 0) {
                                $com_row['seckill_dis_id'] = 0;
                            }

                            $seckill = $this->_seckill($com_row['commodity_id'], $seckill_dis_id, $user['id'], $dlr_code);
                            $seckill_info = $seckill['seckill_info'];

                            //|| $seckill_info['act_status']<>2 //活动前跟活动中返回秒杀价。

                            if ($seckill_info['act_status'] <> 2) {
                                $show_kk_point = 0;
                            }
                            //加一个是否能购买的权限...
                            if ($seckill['can_no'] <= 0 || $seckill['kill_count'] <= 0) {
                                $seckill_sku = $limit_sku = [];
//                                $com_row['seckill_dis_id'] = '';
//                                $com_row['seckill_dis'] = '';
//                                $seckill_info=[];
                                $show_kk_point = 0;
                            }
                        } else {
                            $com_row['seckill_dis_id'] = '';
                            $com_row['seckill_dis'] = '';
                        }
                    }
                }


                if ($com_row['limit_wi_dis']) {
                    $limit_wi_dis_arr = json_decode($com_row['limit_wi_dis'], true);
                    $com_row['limit_wi_dis_id'] = $limit_wi_dis_arr[$dlr_code][0] ?? '';
                }
                //,n_dis,group_dis,pre_sale,suit_dis,full_dis
                if ($com_row['n_dis']) {
                    $n_dis_arr = json_decode($com_row['n_dis'], true);
                    $com_row['n_dis_id'] = isset($n_dis_arr[$dlr_code]) ? $n_dis_arr[$dlr_code] : '';
                    if (empty($com_row['n_dis_id'])) {
                        $com_row['n_dis'] = '';
                    }
                }
                if ($com_row['group_dis']) {
                    $group_dis_arr = json_decode($com_row['group_dis'], true);
                    $com_row['group_dis_id'] = isset($group_dis_arr[$dlr_code]) ? $group_dis_arr[$dlr_code] : '';
                    if (!$com_row['group_dis_id']) {
                        $fight_group_id = '';
                    }
                }

                if ($com_row['pre_dis']) {
                    $pre_sale_arr = json_decode($com_row['pre_dis'], true);
                    $com_row['pre_sale_id'] = isset($pre_sale_arr[$dlr_code]) ? $pre_sale_arr[$dlr_code] : '';
                    if (!$com_row['pre_sale_id']) {
                        $com_row['pre_dis'] = '';
                    }
                }
                if ($com_row['cheap_dis']) {
                    $suit_dis_arr = json_decode($com_row['cheap_dis'], true);
                    $com_row['suit_dis_id'] = isset($suit_dis_arr[$dlr_code]) ? $suit_dis_arr[$dlr_code] : '';
                    if (empty($com_row['suit_dis_id'])) {
                        $com_row['cheap_dis'] = '';
                    }
                }
                if ($com_row['full_dis']) {
                    $full_dis_arr = json_decode($com_row['full_dis'], true);
                    $com_row['full_dis_id'] = isset($full_dis_arr[$dlr_code]) ? $full_dis_arr[$dlr_code] : '';
                    if (empty($com_row['full_dis_id'])) {
                        $com_row['full_dis'] = '';
                    }
                }
                if ($com_row['full_wi_dis']) {
                    $full_wi_dis_arr = json_decode($com_row['full_wi_dis'], true);
                    $com_row['full_wi_dis_id'] = $full_wi_dis_arr[$dlr_code] ?? '';
                }

                // 取送车卡券包
                $qsc_groups_data = [];
                $qsc_group = array_filter(explode(',', $com_row['qsc_group']));
                if (!empty($qsc_group)) {
                    // 查询电子卡券信息
                    $set_sku_model = new DbCommoditySetSku();
                    $map = ['commodity_set_id' => ['in', $qsc_group]];
                    $field = 'a.price, a.stock, b.id, b.commodity_name, b.cover_image';
                    $gsc_list = $set_sku_model->getQscGroupList($map, $field);
                    foreach ($gsc_list as $key => $value) {
                        $qsc_groups_data[$key] = $value->toArray();
                        $qsc_groups_data[$key]['buy_num'] = $com_row['qsc_group_num'];
                    }
                }
                $com_row['qsc_groups_data'] = $qsc_groups_data;
                if ($com_row['gift_dis']) {
                    $gift_dis_arr = json_decode($com_row['gift_dis'], true);
                    $gift_dis_id = $gift_dis_arr[$dlr_code][0] ?? '';
                    if ($gift_dis_id) {
                        $com_row['gift_id'] = $gift_dis_id;
                        $gift_model = new DbGiftCommodity();
                        $gift_row = $gift_model->getGiftComInfo($com_row['commodity_id'], $gift_dis_id);
                        if ($gift_row) {
                            $gift_dis_sku = json_decode($gift_row['sku_price'], true);
                            $show_limit_label = 0;
                            foreach ($gift_dis_sku as $sku_key => $sku_val) {
                                if (in_array($sku_key, $set_sku_ids)) {
                                    $show_limit_label = 1;
                                    break;
                                }
                            }
                            if ($show_limit_label == 0) {
                                $com_row['gift_dis_id'] = 0;
                            }
                        }
                    }
                }
                if ($is_gift == 1) {
                    $com_row['gift_id'] = $gift_id;
                }




                //point_dis
                //                if($com_row['point_dis']){
                //                    $point_dis_arr = json_decode($com_row['point_dis'],true);
                //                    $com_row['point_discount'] =isset($point_dis_arr[$dlr_code])?$point_dis_arr[$dlr_code]:'';
                //                }
                $com_row['point_discount'] = ''; //纯积分折扣不加


            } elseif ($preview == 1) {  //预览默认值
                $list = $this->sku_model->getList(['where' => ['commodity_id' => $commodity_id, 'is_enable' => 1]]);
                $com_row = $this->com_model->getOne(['where' => ['id' => $commodity_id], 'field' => '*']); //暂时
                $com_row['validity_date_start'] = '';
                $com_row['validity_date_end'] = '';
                $com_row['commodity_set_id'] = 0;
            } elseif ($preview == 2) {  //预览获取缓存
                $list = redis($prev_key . 'sku');
                if (empty($list)) {
                    return false;
                }
                $com_row = redis($prev_key . 'commodity');
                $com_row['validity_date_start'] = '';
                $com_row['validity_date_end'] = '';
                $com_row['commodity_set_id'] = 0;
            }


            //            if (empty($list) || empty($com_row)) {
            if (empty($com_row)) {
                return [];
            }
            if (empty($list)) {
                $is_mate = 0;
            }

            $card_rule_goods_info =[
                ['goods_set_id' => $com_row['commodity_set_id'], 'sub_goods_id' => '', 'sku_id' => array_column($list,'id'),'oil_type'=>$com_row['machine_oil_type']]
            ];



            //修改为全部匹配卡券  ++ 如果是commodity_set_id +子商品过来 就不显示卡券ID $commodity_set_id && $sub_commodity_id &&
//            var_dump($set_sku_id_arr);
//            echo '---';
//            echo $commodity_id.'=+=';
//
            $com_row['card_vin_tip']='';//精推普推提示
            if(($com_row['relate_car_ids'] && !$user['id']) || ($form_no_card_list==1)){

                $card_list=[];
            }else{
//                $card_list = $this->_goods_can_get_card($com_row['commodity_set_id'], $user['id'], $this->channel_type,0,$set_sku_ids,$sub_commodity_id);
                //$commodity_set_ids,$goods_info,$user,$channel_type,$card_ids=[],$sub_goods_id=''
                $card_get_use = $this->card_get_use($com_row['commodity_set_id'],$card_rule_goods_info,$user, $this->channel_type,[],$sub_commodity_id,1,[],'','goodsdetail','',99,1);
//                print_json($card_get_use);
                $card_list = $card_get_use['all_card'];
                $card_rules = $card_get_use['goods_card_rule'];
                $can_get_card_list_1 = $card_get_use['get_card_list'];
//                $can_get_card_list = $this->card_list_ok($card_rules,$card_rule_goods_info,$can_get_card_list_1,$com_row['commodity_set_id']);  //这个应该在detail判断

                $com_row['not_article_work'] = $card_get_use['not_article_work'];
                if($com_row['dd_commodity_type']>=1 && (isset($card_get_use['push_type_err']) && $card_get_use['push_type_err']) ){
                    //两个都有那就显示普推的
                    //那就是活动属性要跟人跟车的这种券，但是因为无认证vin导致的券不显示就要提示咯？
                    if(in_array(2,$card_get_use['push_type_err'])){
                        $com_row['card_vin_tip']='当前商品有车主活动，认证车辆后即可参与';//精推普推提示
                    }else{
                        $com_row['card_vin_tip']='认证车辆后有机会参与车主活动，享更多优惠';//精推普推提示
                    }
                }

                //新版卡券列表

            }

            //这一块应该在活动中心做处理，登录 之类的逻辑也是

//            if (!empty($card_list)) {
//                $card_ids = [];
//                $card_no_c_ids = [];
//                foreach ($card_list as $item) {
////                    if($item['is_can_receive']){
////
////                    }
//                    if($item['can_user_type']) {
//                        $card_ids[] = $item['id'];
//                    }else{
//                        $card_no_c_ids[]=$item['id'];
//                    }
//                }
                //这一块应该在活动中心做处理，登录 之类的逻辑也是
//                $card_ret = [];
//                //登录才进入判断人群，没有登录以及不需要判断人群的在  $card_no_c_ids 里 合并了人群跟非人群的就是总的
//                if($user['id']){
//                    $netUserObj = new NetUser();
//                    $card_ret = $netUserObj->canGetCards($user, $card_ids, $this->channel_type);
//                }
//                $card_all_ret = array_merge($card_ret,$card_no_c_ids);
//                $card_list_tmp=[];
//                if (!empty($card_all_ret)) {
//                    foreach ($card_list as $item) {
//                        if (in_array($item['id'], $card_all_ret)) {
//                            $card_list_tmp[] = $item;
//                        }
//                    }
//                }
//                $card_list = $card_list_tmp;
//            }

            unset($com_row['relate_car_ids']);


            $val_arr = [];
            $sku_list = [];
            $limit_sku_list = [];
            $low_p = 0;
            $com_cover_img = $com_row['cover_image'];
            if ($sub_commodity_id) {
                $sub_commodity_info = $this->com_model->getOneByPk($sub_commodity_id);
                if($sub_commodity_info){
                    $com_cover_img = $sub_commodity_info['cover_image'];
                }
            }
//            print_json($list);
            //给一个默认的折扣类型，对前端使用
            $com_row['ac_dis_type'] = $ac_dis_type;
//            $com_row['ac_dis_count'] = $ac_dis_count?end($ac_dis_count):0;
            $com_row['ac_dis_count'] = 0;
            if ($ac_dis_count) {
                if (is_array($ac_dis_count)) {
                    $com_row['ac_dis_count'] = end($ac_dis_count);
                } else {
                    $com_row['ac_dis_count'] = $ac_dis_count;
                }

                if ($act_is_user_segment) {
                    if (!$com_row['is_grouped']) {
                        $com_row['ac_dis_count'] = $com_row['ac_dis_count']['NONE'] ?? 0;
                    } else {
                        $segment_info = get_user_segment_info();
                        $membership = $segment_info['membership_level'];
                        $owner = $segment_info['brand_owner_label'];
                        $com_row['ac_dis_count'] = $act_is_user_segment == 2 ? $com_row['ac_dis_count'][$owner] : $com_row['ac_dis_count'][$membership];
                    }
                }
            }

            $qsc_group = array_filter(explode(',', $com_row['qsc_group']));
            $min_price_sku_id_price = 0;
            foreach ($list as $key => $val) {
                //判断整个商品是否有买赠，要在下面去掉买赠等信息
                if($gift_card_sku_arr || $gift_card_sku_class_arr){
                    $d_sku_code_arr = explode(',',$val['sku_code']);
                    $d_v_code_arr = explode(',',$val['variety_code']);
//                    print_json($d_sku_code_arr);
                    if(array_intersect($gift_card_sku_arr,$d_sku_code_arr) || array_intersect($gift_card_sku_class_arr,$d_v_code_arr)){
                        $com_row['have_gift_card']=1;
                        $com_row['gift_dis'] = '';
                        $com_row['gift_id']  = '';
                    }
                }


                if ($val['id']) {
                    //set_sku_id
                    $is_mate = 1;
                }
                if ($val['price']) { //left 之后有set id/price才算上架
                    if (!empty($val['sp_value_list'])) {
                        $val_arr = array_merge($val_arr, explode(',', $val['sp_value_list']));
                        $list[$key]['sp_value_arr'] = explode(',', $val['sp_value_list']);
                    } else {
                        $list[$key]['sp_value_arr'] = [];
                    }
                    if (empty($val['id'])) {
                        $val['id'] = 0;
                    }
                    if ($com_row['is_integral_shop'] == 1) {
                        foreach ($list[$key]['sp_value_arr'] as $k => $v) {
                            if (in_array($v, [3930, 3931, 3932])) {
                                unset($list[$key]['sp_value_arr'][$k]);
                            }
                        }
                    }
                    $ac_dis_count_one = 0;
                    if ($ac_dis_count) {
                        if (is_array($ac_dis_count)) {
                            $ac_dis_count_one = isset($ac_dis_count[$val['id']]) ? $ac_dis_count[$val['id']] : 0;
                        } else {
                            $ac_dis_count_one = $ac_dis_count;
                        }

                        if ($act_is_user_segment && $entry_from == 2) {
                            $ac_dis_count_one = $ac_dis_count_one['NONE'] ?? 0;
                        } else if ($act_is_user_segment) {
                            $segment_info = get_user_segment_info();
                            $membership = $segment_info['membership_level'];
                            $owner = $segment_info['brand_owner_label'];
                            $ac_dis_count_one = $act_is_user_segment == 2 ? $ac_dis_count_one[$owner] : $ac_dis_count_one[$membership];
                        }
                    }
//                    $com_row['ac_dis_count'] = $ac_dis_count_one;
                    $current_price = $val['old_price'];
                    //fix:价格修改为四舍五入到角然后补充2位0
                    if ($is_by_tc_3 == 1 && $val['maintain_q'] && $val['maintain_q']>0) {
                        $val['old_price'] = sprintf("%.2f", bcdiv($val['old_price'], $val['maintain_q'] / 10, 0));
                    }
                    $one_maintain_q = 0;
                    if (isset($val['maintain_q'])) {
                        if ($val['maintain_q'] <> 10) {
                            $one_maintain_q = $val['maintain_q'];
                        }
                    }
                    $p_s = [
                        'price' => sprintf("%.2f", $val['price']),
                        'current_price' => sprintf("%.2f", $current_price),
                        'old_price' => sprintf("%.2f", $val['old_price']),
                        'stock' => $sub_commodity_id > 0 ? $val['sub_stock'] : $val['stock'],
                        'limit_dis_price' => isset($limit_sku[$val['id']]) ? $limit_sku[$val['id']] : $val['price'],
                        'sku_code' => isset($val['sku_code']) ? $val['sku_code'] : '',
                        'sku_id' => isset($val['id']) ? $val['id'] : 1000000,
                        'image' => empty($val['image']) ? $com_cover_img : $val['image'],
                        'sp_value_arr' => array_values($list[$key]['sp_value_arr']),
                        'work_time_code' => isset($val['wi_code']) ? $val['wi_code'] : 0,
                        'work_time_number' => isset($val['work_time_number']) ? $val['work_time_number'] : 0,
                        'work_time_price' => isset($val['work_time_price']) ? $val['work_time_price'] : 0,
                        'maintain_dis' => $one_maintain_q,
                        'ac_dis_type' => isset($limit_sku[$val['id']]) ? $ac_dis_type : 0,
                        'ac_dis_count' => isset($limit_sku[$val['id']]) ? $ac_dis_count_one : 0,
                        //                        'work_hour_type'           => isset($com_row['work_hour_type']?$com_row['work_hour_type']:'',
                        'commodity_dis_user_segment' => 0,
                        'commodity_dis_act_user_segment' => 0,
                        'commodity_dis_label' => '',
                        'commodity_dis_label_cn' => '',
                    ];
                    if ($use_discount && $commodity_dis_info && !empty($com_row['is_have_car']) && empty($com_row['group_dis']) && empty($com_row['pre_dis']) && empty($com_row['cheap_dis']) && empty($qsc_group)) {
                        $p_s['commodity_dis_user_segment'] = $commodity_dis_info['user_segment'];
                        $p_s['price'] = $p_s['limit_dis_price'] = $this->getCommodityDisFinalPrice($commodity_dis_info, $val['price']);
                        $p_s['commodity_dis_label'] = get_user_segment_label($commodity_dis_info['user_segment']);
                        $p_s['commodity_dis_label_cn'] = get_user_segment_label_cn($p_s['commodity_dis_label']);
                    }
                    if ($val['stock']) {
                        if (($limit_row || $seckill_row) && in_array($act_type_id, [1, 10])) {
                            if ($limit_row) {
                                $dis_info = $this->getLimitDisByPrice($limit_row, $val['id'], $val['price'], $use_discount ? $commodity_dis_info : false);
                            } else {
                                $dis_info = $this->getSecKillDisByPrice($seckill_row, $val['id'], $val['price'], $use_discount ? $commodity_dis_info : false);
                            }
                            $p_s['limit_dis_price'] = sprintf("%.2f", $dis_info['dis_price']);
                            if ($show_kk_point) {
                                if ($seckill_row && $seckill_row['dis_type'] == 3) {
                                    $p_s['commodity_dis_label'] = '';
                                    $p_s['commodity_dis_label_cn'] = '';
                                }
                                $p_s['current_price'] = $dis_info['current_price'];
                                $p_s['commodity_dis_act_user_segment'] = $dis_info['user_segment'];
                                $dis_info['user_segment'] && $p_s['commodity_dis_label'] = get_user_segment_label($dis_info['user_segment']);
                                $dis_info['user_segment'] && $p_s['commodity_dis_label_cn'] = get_user_segment_label_cn($p_s['commodity_dis_label']);
                            }
                        }
                    }
                    $p_s['limit_dis_price'] = sprintf("%.2f", $p_s['limit_dis_price']);
                    //增加现金+积分显示;;;;20220507
                    $goods_point_price_old = $p_s['limit_dis_price'];
                    if (!$show_kk_point || $maintain_can_buy <> 1) {
                        $goods_point_price_old = $p_s['price'];
                    }
                    if (($com_row['is_grouped'] <> 1 || $sub_commodity_id) && $maintain_can_buy == 1) {
                        $money_point_js = $this->goods_point_js(['commodity_id' => $commodity_id, 'price' => $goods_point_price_old, 'channel_type' => $dlr_code]);//计算显示最低积分
                        $money_point = $money_point_js['point'];
                    } else {
                        $money_point = $goods_point_price_old * 10;
                    }
                    $p_s['can_most_point'] = $money_point;
//                    dd($com_row['seckill_dis_id']);

                    if ($val['stock'] > 0) {
                        if (!empty($r_set_sku_id)) {//检测页有传set_sku_id
                            $bool = $val['id'] == $r_set_sku_id;
                        } else {
                            $bool = $min_price_sku_id_price >= $p_s['limit_dis_price'] || $min_price_sku_id_price == 0;
                        }

                        if ($bool) {
                            $com_row['de_sku_id'] = $val['id'];
                            $com_row['de_sku_key'] = $val['sp_value_list'];
                            $com_row['max_point'] = $p_s['can_most_point'];
                            $com_row['maintain_dis'] = $p_s['maintain_dis'];

                            $com_row['original_price_range_start'] = $com_row['original_price_range_end'] = sprintf("%.2f", $p_s['old_price']);
                            $com_row['discount_price_range_start'] = $com_row['discount_price_range_end'] = sprintf("%.2f", $p_s['price']);

                            $com_row['commodity_dis_user_segment'] = $p_s['commodity_dis_user_segment'];
                            $com_row['commodity_dis_act_user_segment'] = $p_s['commodity_dis_act_user_segment'];
                            $com_row['commodity_dis_label'] = $p_s['commodity_dis_label'];
                            $com_row['commodity_dis_label_cn'] = $p_s['commodity_dis_label_cn'];
                            $com_row['current_price'] = $p_s['current_price'];

                            if ($limit_row) {
                                $com_row['limit_original_price_range_end'] = $com_row['limit_original_price_range_start'] = sprintf("%.2f", $p_s['old_price']);
                                $com_row['limit_discount_price_range_start'] = $com_row['limit_discount_price_range_end'] = sprintf("%.2f", $p_s['limit_dis_price']);
                            } else if ($seckill_row) {
                                $com_row['limit_original_price_range_end'] = $com_row['limit_original_price_range_start'] = sprintf("%.2f", $p_s['old_price']);
                                $com_row['limit_discount_price_range_start'] = $com_row['limit_discount_price_range_end'] = sprintf("%.2f", $p_s['limit_dis_price']);

                                $com_row['seckill_original_price_range_start'] = $com_row['seckill_original_price_range_end'] = sprintf("%.2f", $p_s['old_price']);
                                $com_row['seckill_discount_price_range_start'] = $com_row['seckill_discount_price_range_end'] = sprintf("%.2f", $p_s['limit_dis_price']);
                            }
                            $min_price_sku_id_price = $p_s['limit_dis_price'];
                        }
                    }

                    if ($p_s['maintain_dis'] && ($entry_from == 1) && ($p_s['commodity_dis_label'] != '')) {
                        if ($p_s['commodity_dis_user_segment']) {
                            $p_s['ac_dis_type'] = 2;
                            $p_s['ac_dis_count'] = round($val['price'] - $p_s['limit_dis_price'], 2);
                        }
                    }
                    //                    dd($low_p);

                    $p_s['max_point_price'] = sprintf("%.2f", $p_s['price'] - $com_row['max_point'] / 10);
                    $p_s['max_point_old_price'] = sprintf("%.2f", $p_s['old_price'] - $com_row['max_point'] / 10);
                    $p_s['max_point_limit_dis_price'] = sprintf("%.2f", $p_s['limit_dis_price'] - $com_row['max_point'] / 10);
                    if ($p_s['max_point_limit_dis_price'] < 0) {
                        $p_s['max_point_limit_dis_price'] = 0;
                    }
                }
                //                if(isset($limit_sku[$val['id']])){
                //                    $limit_sku_list[$val['sp_value_list']]=[
                //                        'price'        => $limit_sku[$val['id']],
                //                        'stock'        => $val['stock'],
                //                        // 'discount_price'=> !empty($limit_sku[$val['id']]) ? $limit_sku[$val['id']] :'0.00',
                //                        'sku_id'   => isset($val['id']) ? $val['id'] : 1000000,
                //                        'image'        => empty($val['image']) ? $com_row['cover_image'] : $val['image'],
                //                        'sp_value_arr' => $list[$key]['sp_value_arr'],
                //                    ];
                //                }
//                dd($val['sp_value_list']);
                $p_s['card_can_get']=0;
                $p_s['card_can_use']=0;

//                print_json($card_list);
                //普通商品加入卡券可用券，可领券
                if($card_list){
                    foreach ($card_list as $c_v){
                        // 产品说 "我说的是子商品规格，我说子商品规格没有显示" 2024-06-25 18:33:14  zlq 也就是规格弹窗不管卡券类型
                        if($c_v['group_card_type']!=2){
                            $v_sku_arr = explode(',',$c_v['set_sku_ids']);
                            if(!$c_v['set_sku_ids'] || in_array($p_s['sku_id'],$v_sku_arr)){
                                if($c_v['can_get']==1){
                                    $p_s['card_can_get']=1;
                                }
                                if($c_v['can_use_card']>0){
                                    $p_s['card_can_use']=1;
                                }
                            }
                        }
                        //因为 显示问题，出现了没有卡券，但是规格会显示可领券的情况，因为整体不匹配 又回滚  2024-06-26 09:59:40 zlq

//                        $v_sku_arr = explode(',',$c_v['set_sku_ids']);
//                        if(!$c_v['set_sku_ids'] || in_array($p_s['sku_id'],$v_sku_arr)){
//                            if($c_v['available_count']>0){
//                                $p_s['card_can_get']=1;
//                            }
//                            if($c_v['can_use_card']>0){
//                                $p_s['card_can_use']=1;
//                            }
//
//                        }

                    }
                }
                if ($com_row['is_integral_shop'] == 1) {
                    $string = explode(',', $val['sp_value_list']);
                    foreach ($string as $k => $v) {
                        if (in_array($v, [3930, 3931, 3932])) {
                            unset($string[$k]);
                        }
                    }
                    if (!empty($string)) {
                        $sku_list[implode(',', $string)] = $p_s;
                    }
                } else {
                    $sku_list[$val['sp_value_list']] = $p_s;
                }
            }

//            print_json($com_row['discount_price_range_end']);
            if ($is_by_pp == 0) {
                $is_mate = 0;
            }
            $com_row['is_mate'] = $is_mate;

            $sp_key = 'detail_spec_val77' . implode(',', $val_arr);
            $sp_all_list = redis($sp_key);

            if (empty($sp_all_list) || getRedisLock($sp_key . '-lock', 60)) {
                $sp_all_list = $this->sp_model->getAllList(['a.id' => ['in', $val_arr]]);
                $sp_all_list = collection($sp_all_list)->toArray();
                redis($sp_key, $sp_all_list, 80);
            }

            $sp_list = [];
            foreach ($sp_all_list as $key => $val) {
                if ($com_row['is_integral_shop'] == 1) {
                    if (!in_array($val['id'], [3930, 3931, 3932])) {
                        $sp_list[$val['sp_id']]['sp_name'] = $val['sp_name'];
                        $sp_list[$val['sp_id']]['sp_id'] = $val['sp_id'];
                        $sp_list[$val['sp_id']]['sp_value_list'][] = [
                            'sp_value_id' => $val['id'],
                            'sp_value_name' => $val['sp_value_name'],
                        ];
                    }
                } else {
                    $sp_list[$val['sp_id']]['sp_name'] = $val['sp_name'];
                    $sp_list[$val['sp_id']]['sp_id'] = $val['sp_id'];
                    $sp_list[$val['sp_id']]['sp_value_list'][] = [
                        'sp_value_id' => $val['id'],
                        'sp_value_name' => $val['sp_value_name'],
                    ];
                }
            }

            if (!empty($com_row['activity_image'])) {
                $activity_image = [$com_row['activity_image']];
                $sku_img_json = json_decode($com_row['sku_image'], true);
                $switch_image = array_merge($activity_image, $sku_img_json);
            } else {
                $switch_image = json_decode($com_row['sku_image'], true);
            }

            $fight_sku_list = [];
            $fight_sp_list = [];
            $val_arr = [];
            $cheap_sp_list = [];
            $cheap_sku_list = [];
            if (empty($preview)) {
                if ($fight_group_id) {
                    $fight_sku_model = new DbFightGroupCommodity();
                    $where = ['fight_group_id' => $com_row['group_dis_id'][0], 'commodity_set_id' => $com_row['commodity_set_id']];
                    //                    $where[] = ['exp', "FIND_IN_SET('{$dlr_code}',dlr_code)"];
                    $fight_sku_data = $fight_sku_model->getOne(['where' => $where, 'field' => 'sku_price']);
                    //                    echo $fight_sku_model->getLastSql();die();
                    //                    var_dump($fight_sku_data);die();
                    //                    print_json($sku_list);
                    $fight_sku_data = json_decode($fight_sku_data['sku_price'], true);
                    if ($fight_sku_data) {
                        //                        var_dump($sku_list);die();
                        foreach ($sku_list as $s_key => $s_val) {
                            foreach ($fight_sku_data as $f_key => $f_val) {
                                if ($s_val['sku_id'] == $f_key) {
                                    $s_val['price'] = $f_val;
                                    $money_point_js = $this->goods_point_js(['commodity_id' => $commodity_id, 'price' => $f_val, 'channel_type' => $dlr_code]);//计算显示最低积分
                                    $money_point = $money_point_js['point'];

                                    $s_val['max_point_limit_dis_price'] = sprintf("%.2f", $f_val - $com_row['max_point'] / 10);
                                    $s_val['can_most_point'] = $money_point;
                                    //$s_val['set_sku_id']    =$f_val['id'];
                                    $fight_sku_list[$s_key] = $s_val;
                                    $val_arr = array_merge($val_arr, $s_val['sp_value_arr']);
                                }
                            }
                        }
                        foreach ($val_arr as $sp_value_id) {
                            foreach ($sp_all_list as $key => $val) {
                                if ($val['id'] == $sp_value_id) {
                                    $fight_sp_list[$val['sp_id']] = $sp_list[$val['sp_id']];
                                }
                            }
                        }
                        //                var_dump($fight_sp_list);
                        //                var_dump($val_arr);
                        foreach ($fight_sp_list as $kk => $vv) {
                            foreach ($vv['sp_value_list'] as $kkk => $vvv) {
                                if (!in_array($vvv['sp_value_id'], $val_arr)) {
                                    unset($fight_sp_list[$kk]['sp_value_list'][$kkk]);
                                }
                            }
                        }
                    }
                } elseif (!empty($cheap_suit_id)) {
                    //  $cheap_index = new BuCheapSuitIndex();
                    //修改规格时候弹窗不显示折后价--2023-03-27 15:32:58 T
                    $val_arr = [];
                    $cheap_modal = new BuCheapSuitCommodity();
                    $cheap_list = $cheap_modal->getOne(['where' => ['suit_id' => $cheap_suit_id, 'commodity_id' => $commodity_id]]);
//                   print_json($sku_list);
                    foreach ($sku_list as $key => $val) {
                        $cheap_sku = json_decode($cheap_list['sku_json'], true);
                        foreach ($cheap_sku as $c_key => $c_val) {
                            if ($val['sku_id'] == $c_key) {
                                $val['price'] = $c_val;
                                //$s_val['set_sku_id']    =$f_val['id'];
                                $cheap_sku_list[$key] = $val;
                                $val_arr = array_merge($val_arr, $val['sp_value_arr']);
                            }
                        }
                    }

                    foreach ($val_arr as $sp_value_id) {
                        foreach ($sp_all_list as $key => $val) {
                            if ($val['id'] == $sp_value_id) {
                                $cheap_sp_list[$val['sp_id']] = $sp_list[$val['sp_id']];
                            }
                        }
                    }

                    foreach ($cheap_sp_list as $kk => $vv) {
                        foreach ($vv['sp_value_list'] as $kkk => $vvv) {
                            if (!in_array($vvv['sp_value_id'], $val_arr)) {
                                unset($cheap_sp_list[$kk]['sp_value_list'][$kkk]);
                            }
                        }
                    }
                } elseif (!empty($com_row['gift_id'])) {

                    $gift_model = new DbGiftCommodity();
                    $gift_row = $gift_model->getGiftComInfo($com_row['commodity_id'], $com_row['gift_id']);
                    if (!empty($gift_row)) {
                        $limit_sku = json_decode($gift_row['sku_price'], true);
                        if ($is_gift == 1) {
                            foreach ($sku_list as $key => $val) {
                                $sku_list[$key]['is_gift'] = 0;
                                foreach ($limit_sku as $c_key => $c_val) {
                                    $sku_list[$key]['is_gift'] = 1;
                                    $sku_list[$key]['price'] = 0;
                                }
                            }
                        } else {
                            foreach ($sku_list as $key => $val) {
                                $sku_list[$key]['is_gift'] = 0;
                                foreach ($limit_sku as $c_key => $c_val) {
                                    if ($val['sku_id'] == $c_key) {
                                        $sku_list[$key]['is_gift'] = 1;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $com_row['sku_image'] = $switch_image;

            // 取送车卡券包
            $qsc_groups_data = [];
            $qsc_group = array_filter(explode(',', $com_row['qsc_group']));
            $qsc_price = 0; // 电子卡券金额
            if (!empty($qsc_group)) {
                // 查询电子卡券信息
                $set_sku_model = new DbCommoditySetSku();
                $map = ['commodity_set_id' => ['in', $qsc_group]];
                $field = 'a.id as sku_id, a.price, a.stock, b.id, b.commodity_name, b.cover_image';
                $gsc_list = $set_sku_model->getQscGroupList($map, $field);
                if ($com_row['is_mail'] == 1 && $com_row['is_store'] == 1) {
                    $mail_method = 3;
                } elseif ($com_row['is_mail'] == 1 && $com_row['is_store'] == 0) {
                    $mail_method = 2; //快递
                } else {
                    $mail_method = 1; //到店
                }
                if ($com_row['mail_type']) {
                    $mail_method = $com_row['mail_type'];
                }
                // 计算取送车服务包售价
                $qsc_groups_data[] = [
                    'sku_id' => $com_row['de_sku_id'],
                    'price' => $com_row['discount_price_range_start'],
                    'id' => $com_row['id'],
                    'commodity_name' => $com_row['commodity_name'],
                    'cover_image' => $com_row['cover_image'],
                    'buy_num' => 1,
                    'mail_method' => $mail_method,
                    'dlr_code' => $dd_dlr_code
                ];
                $com_row['commodity_name'] = $com_row['qsc_group_name']; // 取送车服务包名称


                foreach ($gsc_list as $key => $value) {
                    $qsc_groups_data[$key + 1] = $value->toArray();
                    $qsc_groups_data[$key + 1]['buy_num'] = $com_row['qsc_group_num'];
                    $qsc_groups_data[$key + 1]['mail_method'] = $mail_method;
                    $qsc_groups_data[$key + 1]['dlr_code'] = $dd_dlr_code;
                    $qsc_price = bcadd($qsc_price, bcmul($value['price'], $com_row['qsc_group_num'], 2), 2);
                }

                $qsc_total_price = bcadd($com_row['discount_price_range_start'], $qsc_price, 2);

                $com_row['discount_price_range_start'] = $qsc_total_price; // 售价开始范围
                $com_row['discount_price_range_end'] = $qsc_total_price; // 售价结束范围
                $com_row['original_price_range_start'] = $qsc_total_price;
                $com_row['original_price_range_end'] = $qsc_total_price;
                if (!empty($com_row['qsc_group_price'])) {
                    $com_row['original_price_range_start'] = $com_row['qsc_group_price'];
                    $com_row['original_price_range_end'] = $com_row['qsc_group_price'];
                }
            }
            $com_row['qsc_groups_data'] = $qsc_groups_data;

            $sku_detail_data = $this->getSkuDetailContent($com_row['id'], $com_row['de_sku_id']);
            $com_row['detail_content'] = $sku_detail_data['commodity_sku_detail'];
            $commodity_info = ['sku_list' => $sku_list, 'sp_list' => array_values($sp_list), 'sku_image' => [], 'commodity_data' => $com_row, 'fight_sku_list' => $fight_sku_list, 'fight_sp_list' => $fight_sp_list, 'cheap_sp_list' => $cheap_sp_list, 'cheap_sku_list' => $cheap_sku_list, 'user_info' => isset($user_info) ? $user_info : '', 'qsc_groups_data' => $qsc_groups_data,'card_list'=>$card_list,'can_get_card_list'=>$can_get_card_list_1??[],'card_rules'=>$card_rules??[]];
        }

        return $commodity_info;
    }

    /**
     * 订单类型 1.普通订单 2.拼团,3 优惠套装,4 臻享服务包
     * 套装商品时候增加order_code,计算出总的优惠应该是多少.
     * @param $set_sku_id
     * @param int $order_type
     * @return array|false|\PDOStatement|string|Model
     */

    public function getOneSku($set_sku_id, $order_type = 1, $dlr_code = '', $cheap_suit_id = 0, $limit_id = 0, $is_sub = 0, $group_id = 0, $serv_pack_id = 0, $seckill_id = 0, $order_code = '', $user = [])
    {
        $redis_name = "order_get_one_sku." . $set_sku_id . $dlr_code . $cheap_suit_id . $limit_id . $is_sub . $group_id . $serv_pack_id;
        $row = redis($redis_name); //写入缓存--
        if (!$row) {
            $where = [
                'a.id' => $set_sku_id,'a.is_enable'=>1
            ];
            //            $where[] = ['exp', "FIND_IN_SET('{$dlr_code}',e.up_down_channel_dlr)"];//加入渠道代码
            $where[] = ['exp', "FIND_IN_SET('{$dlr_code}',d.up_down_channel_dlr)"]; //加入渠道代码
            $field = 'a.id, a.commodity_sku_id,d.group_commodity_ids_info,a.stock, b.sp_value_list, b.image, a.is_enable AS is_sku_out, b.sku_code, IF ((c.is_enable=0 OR c.is_enable=0), "0", 1) AS is_sold_out, c.commodity_name, c.cover_image, c.commodity_class, c.commodity_card_ids, c.card_id, c.id AS commodity_id, d.is_mail, d.is_store, favourable_introduction, favourable_detail, d.pay_style, a.price b_act_price, a.price, limit_dis,seckill_dis,n_dis, group_dis, pre_dis, cheap_dis, full_dis, d.commodity_dlr_type_id,d.is_card_multic,d.mail_price,a.divided_into,a.install_fee,a.commodity_set_id,d.max_point,d.factory_points,d.dlr_points,e.pay_dlr_code,d.spr_id,d.subscribe_type,c.supplier,b.tax_code,b.cost_price,b.tax,c.dd_commodity_type,c.commodity_class,b.card_id goods_card_ids,a.group_sub_set_sku_id,a.group_sub_commodity_id , sub_c.commodity_name sub_goods_name, sub_c.cover_image sub_cover_image, sub_c.commodity_class sub_commodity_class, sub_c.commodity_card_ids sub_commodity_card_ids, sub_c.card_id sub_card_id, sub_c.id AS sub_commodity_id,c.work_hour_type,sub_c.work_hour_type sub_work_hour_type,a.commodity_id mo_commodity_id,sub_set_sku.stock sub_set_sku_stock,b.e3s_bj_type_name,part.part_name,c.is_integral_shop,d.service_channel,b.maintain_q maintain_dis,b.relate_dlr_code,d.listing_type,c.is_grouped,e.crowdfund_dis,b.delivery_coefficient,e.gift_dis,a.cost_price dlr_price,b.variety_code,d.dlr_groups';//备件成本价==网点价
            $row = $this->set_sku_model->getSkuInfoNet($where, $field);
            if (!$row) {
                return [];
            } else {
                if ($is_sub == 1) {
                    $row['commodity_name'] = $row['sub_goods_name'];
                    $row['cover_image'] = $row['sub_cover_image'];
                    $row['commodity_class'] = $row['sub_commodity_class'];
                    $row['commodity_card_ids'] = $row['sub_commodity_card_ids'];
                    $row['card_id'] = $row['sub_card_id'];
                    $row['commodity_id'] = $row['sub_commodity_id'];
                    $row['stock'] = $row['sub_set_sku_stock'];
                    //                    $row['work_hour_type'] = $row['sub_work_hour_type'];
                    $row['mo_id'] = $row['mo_commodity_id'];
                }
                redis($redis_name, $row, 5);
            }
        }

        $service_channel_labels = [];
        if (!empty($row['service_channel'])) {
            $system_val_model = new DbSystemValue();
            $service_channels = $system_val_model->getNameList('23');
            $service_channel_ids = explode(',', $row['service_channel']);
            foreach ($service_channel_ids as $channel_id) {
                if (isset($service_channels[$channel_id])) {
                    $service_channel_labels[] = $service_channels[$channel_id];
                }
            }
        }
        $row['service_channel_labels'] = $service_channel_labels;

        if (!$row['part_name']) {
            $row['part_name'] = '';
        }
        //团购ID判断
        if ($group_id > 0) {
            $order_type = 2;
        }
        if ($cheap_suit_id > 0) {
            $order_type = 3;
        }
        if ($serv_pack_id > 0) {
            $order_type = 4;
        }
        //到店对应订单来源类型；
        $dd_order_s = 0;
        if ($row['dd_commodity_type']) {
            $dd_order_s_arr = $this->com_model::dd_order_s('order_s');
            if ($dd_order_s_arr) {
                $dd_order_s = isset($dd_order_s_arr[$row['dd_commodity_type']]) ? $dd_order_s_arr[$row['dd_commodity_type']] : 0;
            }
        }
        $row['dd_order_s'] = $dd_order_s;
        ///$sku_image     =json_decode($row['sku_image'],true);
        $sp_value_arr = explode(',', $row['sp_value_list']);
        $sp_value_list = $this->sp_model->getAllList(['a.id' => ['in', $sp_value_arr]]);
        /*if(isset($sp_value_arr[0]) && !empty($sku_image[$sp_value_arr[0]])){
            $sku_row_image=$sku_image[$sp_value_arr[0]][0];
        }else{
            $sku_row_image='';
        }*/
        //查询卡券
        $card_model = new DbCommodityCard();
        $card_row = $card_model->where('commodity_id', $row['commodity_id'])->field('GROUP_CONCAT(distinct card_id) as commodity_card_ids')->find();
        if ($card_row) {
            $row['card_id'] = $card_row['commodity_card_ids'];
        }
        $row['point_discount'] = 0;

        $row['sp_value_list'] = $sp_value_list;
        $row['sku_image'] = !empty($row['image']) ? $row['image'] : $row['cover_image'];
        $row['divided_price'] = sprintf("%.2f", self::getDividedrice($row['price'], $row['divided_into'], $row['install_fee']));
        $row['limit_discount_id'] = 0;
        //限时购--订单页传的时候传limitid， 如果要算组合商品的，那么后台要对应改sku:price
        if ($limit_id) {
            $dis_limit = json_decode($row['limit_dis'], true);
            if (isset($dis_limit[$dlr_code][0])) { //获取限时购ID
                if ($limit_id == $dis_limit[$dlr_code][0]) {
                    $limit_model = new DbLimitDiscountCommodity();
                    $limit_row = $limit_model->getOneDisByCommodity($row['commodity_id'], $limit_id);
                    if ($limit_row) {
                        $sku_price = json_decode($limit_row['sku_price'], true);
                        if (!empty($sku_price[$set_sku_id])) {
                            $row['limit_price'] = $sku_price[$set_sku_id];
                        }
                        $row['limit_discount_id'] = $limit_row['limit_discount_id'];
                        $row['limit_dis_type'] = $limit_row['dis_type'];
                    }
                }
            }
            //拼团订单
        } elseif ($seckill_id) {
            // 秒杀
            $dis_seckill = json_decode($row['seckill_dis'], true);
            if (isset($dis_seckill[$dlr_code][0])) { //获取限时购ID
                if ($seckill_id == $dis_seckill[$dlr_code][0]) {
                    $seckill_model = new DbSeckillCommodity();
                    $seckill_row = $seckill_model->getOneDisByCommodity($row['commodity_id'], $seckill_id);
                    if ($seckill_row) {
                        $sku_price = json_decode($seckill_row['sku_price'], true);
                        if (!empty($sku_price[$set_sku_id])) {
                            $row['limit_price'] = $sku_price[$set_sku_id];
                        }
                        $row['seckill_discount_id'] = $seckill_row['seckill_id'];
                        $row['seckill_dis_type'] = $seckill_row['dis_type'];
                    }
                }
            }
        } elseif ($order_type == 2) {
            $group_dis = json_decode($row['group_dis'], true);
            if (isset($group_dis[$dlr_code][0])) {
                $fight_model = new DbFightGroupCommodity();
                $where = ['commodity_id' => $row['commodity_id'], 'fight_group_id' => $group_dis[$dlr_code][0]]; //用主键ID去获取
                $fight_row = $fight_model->getOne(['where' => $where, 'field' => 'sku_price']);
                if ($fight_row) {
                    $fight_row = json_decode($fight_row['sku_price'], true);
                    $group_info_model = new DbFightGroup();
                    $group_info = $group_info_model->getOne(['where' => ['id' => $group_dis[$dlr_code][0]], 'field' => 'id,title,settlement_rule_id,settlement_rule_type,settlement_rule_value,title act_name,e3s_activity_id,card_available,rel_card_ids,act_sett_standard']);
                    if (!empty($fight_row[$set_sku_id])) {
                        $row['group_price'] = $fight_row[$set_sku_id];
                        $row['group_info'] = $group_info;
                    } else {
                        return [];
                    }
                }
            } else {
                return [];
            }
        } elseif ($order_type == 3) {
            if (!$order_code) {
                //没有传订单号的时候只有个优惠信息
                $cheap_info_model = new BuCheapSuitIndex();
                $cheap_info = $cheap_info_model->getOne(['where' => ['id' => $cheap_suit_id], 'field' => 'id,settlement_rule_id,settlement_rule_type,settlement_rule_value,name act_name,e3s_activity_id,card_available,rel_card_ids,can_refund,act_sett_standard']);
                $row['cheap_info'] = $cheap_info;
            } else {
                $suit_redis_name = 'suit_redis_' . $order_code;
                $suit_redis_data = redis($suit_redis_name);
                if (!$suit_redis_data) {
                    $order_commodity_model = new BuOrderCommodity();
                    $order_goods = $order_commodity_model->getList(['where' => ['parent_order_code' => $order_code], 'filed' => 'order_code,commodity_id,sku_id']);
                    $goods_ids = '';
                    $sku_ids = '';
                    foreach ($order_goods as $og_v) {
                        $goods_ids .= $og_v['commodity_id'] . ',';
                        $sku_ids .= $og_v['sku_id'] . ',';
                    }
                    $suite_q = [
                        'commodity_ids' => trim($goods_ids, ','),
                        'sku_ids' => trim($sku_ids, ','),
                        'suit_ids' => $cheap_suit_id,
                    ];
                    $suit_info_list = $this->suitList($suite_q, $user, $dlr_code);
                    $suit_info = $suit_info_list['msg']['suit_info'];
                    $suit_price_list = $suit_info_list['msg']['sku_id_price'];
                    $suit_redis_data = [
                        'suit_info' => $suit_info,
                        'suit_price_list' => $suit_price_list,
                    ];
                    redis($suit_redis_name, $suit_redis_data, 800);
                }
                if (isset($suit_redis_data['suit_info'])) {
                    $row['cheap_price'] = $suit_redis_data['suit_price_list'][$set_sku_id];
                    $row['cheap_info'] = $suit_redis_data['suit_info'];
                } else {
                    return [];
                }
            }

//            $cheap_modal = new BuCheapSuitSub();
//            $where = ['index_id' => $cheap_suit_id, 'sku' => $set_sku_id];
//            $cheap_row = $cheap_modal->getOne(['where' => $where]);
//            if ($cheap_row) {
//                $cheap_info_model = new BuCheapSuitIndex();
//                $cheap_info = $cheap_info_model->getOne(['where' => ['id' => $cheap_suit_id], 'field' => 'id,settlement_rule_id,settlement_rule_type,settlement_rule_value,name act_name,e3s_activity_id,card_available,rel_card_ids']);
//                $row['cheap_price'] = $cheap_row['price'];
//                $row['cheap_info'] = $cheap_info;
//            } else {
//                return [];
//            }
        } elseif (4 == $order_type) {
            $serv_pack_modal = new DbServerBag();
            $where = ['id' => $serv_pack_id];
            $serv_pack_row = $serv_pack_modal->getOne(['where' => $where]);
            if (!$serv_pack_row) {
                return [];
            }
        }

        // 23-3-8活动  --活动进来的话直接判断价格==0.1
        $check_draw = $this->isHaveDrawCap($row['commodity_id'], session('net_api_channel_type'));
        if ($check_draw) {
            $row['price'] = '0.1';
            $row['b_act_price'] = '0.1';
        }
        return $row;
    }


    public function goodsClass($channel_type = '', $index_type = 0, $requestData)
    {

        $model = new DbCommodityType();
        $field = "a.comm_type_name aname,a.id aid,t.comm_type_name twoname,b.img,t.id twoid,b.comm_type_name bname,b.id bid,b.img";
        $goods_sql = sprintf("SELECT 1 from (SELECT a.* from t_db_commodity_flat a join t_db_commodity_set b on a.commodity_id=b.commodity_id join t_db_commodity_set_sku c on b.id=c.commodity_set_id where find_in_set('{$channel_type}',b.up_down_channel_dlr) and find_in_set(1,b.sales_channel) and b.count_stock>0 and c.is_enable=1 and c.stock>0) cf where cf.comm_type_id=b.id and cf.is_enable=1 AND  find_in_set('%s',up_down_channel_dlr) and find_in_set(1,cf.sales_channel)", $channel_type);
//        $car_id = !empty($requestData['car_id']) ? $requestData['car_id'] : '';
//        if ($car_id && !in_array($channel_type, ['PZ1AAPP', 'PZ1ASM'])) {
//            $goods_sql .= sprintf(" and ( (find_in_set({$car_id},cf.relate_car_ids) || cf.relate_car_ids='' ||  cf.relate_car_ids is null)  || cf.dd_commodity_type  in (1,3,4,41,12) )");
//        }
        $types = $model->alias('a')->join("t_db_commodity_type t ", "a.id=t.comm_parent_id ")->join("t_db_commodity_type b ", "t.id=b.comm_parent_id")->whereExists($goods_sql)->where("a.is_enable=1 and b.is_enable=1 and t.is_enable=1")->field($field)->order("a.sort,t.sort,b.sort,a.last_updated_date desc")->select();
        //        echo $model->getLastSql();die();
        $a_type = [];
        $b_type = [];
        $c_type = [];
        $sm_type = [];
        if ($types) {
            //            var_dump($types);die();
            foreach ($types as $v) {
                $a_type[] = ['id' => $v['aid'], 'type_name' => $v['aname']];
                $b_type[] = ['id' => $v['twoid'], 'type_name' => $v['twoname']];
                $c_type[] = ['id' => $v['bid'], 'type_name' => $v['bname']];

                $sm_type[$v['aid']]['aname'] = $v['aname'];
                $sm_type[$v['aid']]['aid'] = $v['aid'];
                $sm_type[$v['aid']]['b_type'][$v['twoid']]['twoname'] = $v['twoname'];
                $sm_type[$v['aid']]['b_type'][$v['twoid']]['twoid'] = $v['twoid'];
                //                $sm_type[$v['aid']]['b_type'][$v['twoid']]['twoname'] = [
                //                    'twoname'=>$v['twoname'],
                //                    'twoid'=>$v['twoid']
                //                ];
                $sm_type[$v['aid']]['b_type'][$v['twoid']]['list'][] =
                    [
                        'bname' => $v['bname'],
                        'bid' => $v['bid'],
                        'img' => $v['img']
                    ];
            }
            $a_type = array_merge(array_unique($a_type, SORT_REGULAR));
            $b_type = array_merge(array_unique($b_type, SORT_REGULAR));
            $c_type = array_merge(array_unique($c_type, SORT_REGULAR));
        }

        if ($index_type == 0) {
            $sm_type = array_values($sm_type);
            foreach ($sm_type as $k => $v) {
                if (isset($v['b_type'])) {
                    $sm_type[$k]['b_type'] = array_values($v['b_type']);
                }
            }
            return array_values($sm_type);
        }
        if ($index_type == 1) {
            return $a_type;
        }
        if ($index_type == 2) {
            return $b_type;
        }
        if ($index_type == 3) {
            return $c_type;
        }
        return $types;
    }

    //新版分类
    public function newGoodsClass($channel_type = '', $index_type = 0, $requestData)
    {
        $model = new DbHomeType();
        $field = "a.comm_type_name aname,a.id aid,t.comm_type_name twoname,b.img,t.id twoid,b.comm_type_name bname,b.id bid,b.img";
        //2023/06/14 雄老板说relate_car_ids字段用t_db_commodity_set_sku表然后重命名
//        $goods_sql = sprintf("SELECT 1 from (SELECT a.*,c.relate_car_ids as relate_car_idss from t_db_commodity_flat a join t_db_commodity_set b on a.commodity_id=b.commodity_id join t_db_commodity_set_sku c on b.id=c.commodity_set_id where find_in_set('{$channel_type}',b.up_down_channel_dlr) and find_in_set(1,b.sales_channel) and c.is_enable=1) cf where (cf.comm_type_id=c.old_comm_type_id or c.comm_id = cf.commodity_id) and cf.is_enable=1 AND  find_in_set('%s',up_down_channel_dlr) and find_in_set(1,cf.sales_channel)", $channel_type);
        $goods_sql = sprintf("SELECT comm_type_id,commodity_id from (SELECT a.*,c.relate_car_ids as relate_car_idss from t_db_commodity_flat a join t_db_commodity_set b on a.commodity_id=b.commodity_id join t_db_commodity_set_sku c on b.id=c.commodity_set_id where find_in_set('{$channel_type}',b.up_down_channel_dlr) and find_in_set(1,b.sales_channel) and c.is_enable=1) cf where  cf.is_enable=1 AND  find_in_set('%s',up_down_channel_dlr) and find_in_set(1,cf.sales_channel)", $channel_type);
        $car_id = !empty($requestData['car_id']) ? $requestData['car_id'] : '';
        if ($car_id && !in_array($channel_type, ['PZ1AAPP', 'PZ1ASM'])) {
            $goods_sql .= sprintf(" and ( (find_in_set({$car_id},cf.relate_car_idss) || cf.relate_car_idss='' ||  cf.relate_car_idss is null)  || cf.dd_commodity_type  in (1,3,4,41,12) )");
        }
        $goods_sql .= "GROUP BY commodity_id";
        $where = [];
        $res = $model->query($goods_sql);
        if (!empty($res)) {
            $comm_id = [];
            $old_comm_type_id = [];
            foreach ($res as $val) {
                $comm_id[] = $val['commodity_id'];
                $old_comm_type_id[] = $val['comm_type_id'];
            }
            $new_comm_id = array_merge($comm_id, $old_comm_type_id);
            $where['c.comm_id|c.old_comm_type_id'] = array('in', $new_comm_id);
        }

        if (in_array($channel_type, ['GWSM', 'GWAPP'])) {
            $where['a.page_type'] = 1;
            $where['t.page_type'] = 1;
            $where['b.page_type'] = 1;
        } elseif (in_array($channel_type, ['QCSM', 'QCAPP'])) {
            $where['a.page_type'] = 5;
            $where['t.page_type'] = 5;
            $where['b.page_type'] = 5;
        } elseif (in_array($channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
            //新增判断pz
            $where['a.page_type'] = 6;
            $where['t.page_type'] = 6;
            $where['b.page_type'] = 6;
        }
        $types = $model->alias('a')
            ->join("t_db_home_type t ", "a.id=t.comm_parent_id ")
            ->join("t_db_home_type b ", "t.id=b.comm_parent_id")
            ->join("t_db_home_type_commodity c ", "b.id = c.new_comm_type_id")
            ->where("a.is_enable=1 and b.is_enable=1 and t.is_enable=1 and c.is_enable = 1")
            ->where("a.is_publish = 1 and t.is_publish = 1 and b.is_publish = 1")
            ->where($where)
            ->field($field)
            ->group('b.id')
            ->order("a.sort,t.sort,b.sort,c.sort,a.last_updated_date desc")->select();
//                echo $model->getLastSql();die();
        $a_type = [];
        $b_type = [];
        $c_type = [];
        $sm_type = [];
        if ($types) {
            //            var_dump($types);die();
            foreach ($types as $v) {
                $a_type[] = ['id' => $v['aid'], 'type_name' => $v['aname']];
                $b_type[] = ['id' => $v['twoid'], 'type_name' => $v['twoname']];
                $c_type[] = ['id' => $v['bid'], 'type_name' => $v['bname']];

                $sm_type[$v['aid']]['aname'] = $v['aname'];
                $sm_type[$v['aid']]['aid'] = $v['aid'];
                $sm_type[$v['aid']]['b_type'][$v['twoid']]['twoname'] = $v['twoname'];
                $sm_type[$v['aid']]['b_type'][$v['twoid']]['twoid'] = $v['twoid'];
                //                $sm_type[$v['aid']]['b_type'][$v['twoid']]['twoname'] = [
                //                    'twoname'=>$v['twoname'],
                //                    'twoid'=>$v['twoid']
                //                ];
                $sm_type[$v['aid']]['b_type'][$v['twoid']]['list'][] =
                    [
                        'bname' => $v['bname'],
                        'bid' => $v['bid'],
                        'img' => $v['img']
                    ];
            }
            $a_type = array_merge(array_unique($a_type, SORT_REGULAR));
            $b_type = array_merge(array_unique($b_type, SORT_REGULAR));
            $c_type = array_merge(array_unique($c_type, SORT_REGULAR));
        }

        if ($index_type == 0) {
            $sm_type = array_values($sm_type);
            foreach ($sm_type as $k => $v) {
                if (isset($v['b_type'])) {
                    $sm_type[$k]['b_type'] = array_values($v['b_type']);
                }
            }
            return array_values($sm_type);
        }
        if ($index_type == 1) {
            return $a_type;
        }
        if ($index_type == 2) {
            return $b_type;
        }
        if ($index_type == 3) {
            return $c_type;
        }
        return $types;
    }

    /**
     * 热销
     * @param $requestData
     * @param string $channel_type
     * @return array
     */
    public function goodsSale($requestData)
    {
        $order_goods_model = new BuOrderCommodity();
        $channel_type = $requestData['dlr_code'] ?? '';
        $limit = $requestData['pageSize'] ?? 5;
        $limit = sprintf("0,%s", $limit);
        $where = ['a.is_enable' => 1, 'b.order_status' => ['not in', [1, 8, 3, 5]]];
        if ($channel_type) {
            $where['b.dlr_code'] = $channel_type;
        } else {
            $cut_where[] = ['exp', " (find_in_set('{$this->channel_type}',flat.up_down_channel_dlr)) "];
        }
        $field = "flat.commodity_id,flat.commodity_name,flat.cover_image,flat.cover_image,flat.price,flat.final_price,sum(a.count) sale_num";
        $goods = $order_goods_model->alias('a')->join("t_bu_order b", 'a.order_code=b.order_code')->join('t_db_commodity_flat flat', "a.commodity_id=flat.commodity_id")->where($where)->field($field)->group('flat.commodity_id')->order('sum(a.count) desc')->limit($limit)->select();

        return $this->re_msg($goods);
    }
    /**
     * @param $requestData
     * @param $user
     * @param $channel_type
     *
     */
    public function goodsList($requestData, $user, $channel_type, $where = [], $from='', $type='')
    {
        $api_start_at = microtime(true);
        $this->user = $user;

        $this->channel_type = $channel_type;
        if ($user) {
            $this->user_id = $user['id'];
            $this->unionid = $user['bind_unionid'];
        }
        $g_must_delete = [];
        $page = $requestData['page'] ?? 1;
        $page_size = $requestData['pageSize'] ?? 30;
        $comm_type_id = $requestData['comm_type_id'] ?? '';
        $com_s_types = $requestData['com_s_types'] ?? '';
        $car_s_id = $requestData['car_id'] ?? '';
        $name = $requestData['search'] ?? '';
        $commodity_ids = $requestData['commodity_ids'] ?? '';
        $commodity_ids = trim($commodity_ids, ',');
        $card_id = $requestData['card_id'] ?? '';
        $price_s = $requestData['price_start'] ?? '';
        $price_e = $requestData['price_end'] ?? '';
        $ask_at = $requestData['ask_at'] ?? 0;
        $dd_dlr_code = $requestData['dd_dlr_code'] ?? 0;
        $kilometer = $requestData['kilometer'] ?? ''; //公里数
        $lng = $requestData['lng'] ?? ''; //经度
        $lat = $requestData['lat'] ?? ''; //纬度
        $n_dis_id = $requestData['n_dis_id'] ?? 0;
        $full_cut_id = $requestData['full_cut_id'] ?? 0;
        $sku_ids = $requestData['sku_ids'] ?? 0;
        $new_comm_type_id = $requestData['new_comm_type_id'] ?? '';//新版分类
        $order = $requestData['order_by'] ?? '';//排序
        $use_gift_card = $requestData['use_gift_card'] ?? 0;//使用未激活的赠品券
        $gift_card_main = $requestData['gift_card_main'] ?? 0;//是否主品待激活
        $new_order = $requestData['new_order'] ?? '';//新排序，sale_number 销量,price_asc 价格升序,price_desc价格降序,new_goods新品
        $time = date('Y-m-d H:i:s');
        $where['a.is_enable'] = $count_where['a.is_enable'] = 1;
        $where['c.is_enable'] = 1;
//        $where['c.stock'] =['>',0];
        if (!isset($requestData['fu_pin']) && !isset($requestData['miao_sha']) && !isset($requestData['suit_list']) &&  !isset($requestData['card_gift'])) {
            $where['b.count_stock'] = ['>', 0];
        }

        if(!isset($requestData['card_gift'])){
            //待激活的--主品+赠品都显示
            //已激活--只显示主品
            $use_gift_card=1;//如果不是从赠品券获取赠品列表过来，那么就只取卡券状态==1的 不获取7的
        }
        $commoditySet = new DbCommoditySet();
        if($commodity_ids){
            $order = "field(a.commodity_id, $commodity_ids),";
        }
        if($gift_card_main && $card_id){
            $gift_goods = $this->getGiftMainGoods($card_id,$user);
            $commodity_ids = $gift_goods['goods_id'];
            $card_id='';
        }

        $not_show_key = 'not_show_dlr_tmp_' . $this->channel_type;
        $not_show_ids = redis($not_show_key);

        // 使用特殊标记来区分"未缓存"和"查询结果为空"
        if ($not_show_ids === false || $not_show_ids === null) {
            // 缓存不存在，需要查询数据库
            $not_show_ids = $commoditySet->getColumn(['where' => "FIND_IN_SET('{$this->channel_type}', not_show_dlr)", 'column' => 'commodity_id']);

            // 如果查询结果为空，缓存一个特殊标记，避免缓存穿透
            if (empty($not_show_ids)) {
                $not_show_ids = ['__EMPTY_RESULT__']; // 使用特殊标记表示空结果
            }

            redis($not_show_key, $not_show_ids, 300); // 增加缓存时间到5分钟
        }

        // 如果缓存中是空结果标记，则返回空数组
        if ($not_show_ids === ['__EMPTY_RESULT__']) {
            $not_show_ids = [];
        }

        if ($not_show_ids) {
            $where['a.commodity_id'] = $count_where['a.commodity_id'] = ['not in', $not_show_ids];
        }


        if (!empty($ask_at)) {
            //获取 pz1a 的commodity_id_str
            $home = new DbHomeSm();
            $pz_data = $home->getOneByPk(DbHomeSm::PZ1A_PAGE);
            $where['a.commodity_id'] = $count_where['a.commodity_id'] = ['not in', explode(',', $pz_data['commodity_id_str'])];
        }

        //n件N折 搜索列表
        $act_title = '';
        if ($n_dis_id) {
            $com_id_list = $this->getNDisCountNid($n_dis_id);
            if ($com_id_list) {
                $dis_model = new DbNDiscount();
                $n_dis = $dis_model->getOneByPk($n_dis_id);
                if ($n_dis) {
                    $act_title = $n_dis['title'];
                }
                $commodity_ids .= $com_id_list['g_ids'];
            }
        }
        if ($full_cut_id) {
            $cut_model = new DbFullDiscount();
            $cut_where = ["a.id" => $full_cut_id, 'a.start_time' => ['<=', $time], 'a.end_time' => ['>=', $time]];
            $cut_where[] = ['exp', " (find_in_set('{$this->channel_type}',a.up_down_channel_dlr)) "];
            $full_cut_info = $cut_model->getOneU(["where" => $cut_where, 'field' => "a.id,GROUP_CONCAT(b.commodity_id SEPARATOR ',') commodity_ids,a.activity_title"]);
            //            echo $cut_model->getLastSql();die();
            $commodity_ids .= $full_cut_info['commodity_ids'];
            if (!$full_cut_info['activity_title']) {
                $full_cut_info['activity_title'] = "满减活动";
            }
            $act_title = $full_cut_info['activity_title'];
        }

        $data = ''; #todo, optmize
        $flat = new DbCommodityFlat();
        if (!empty($data)) {
            $list = $data;
        } else {
            $where[] = ['exp', " (find_in_set('{$this->channel_type}',a.up_down_channel_dlr)) "];
            $count_where[] = ['exp', " (find_in_set('{$this->channel_type}',a.up_down_channel_dlr)) "];
            $order .= "card_c.sorts desc,a.last_updated_date asc";
            if ($name) {
                if (preg_match('/^\d+$/', $name)) {
                    $where['a.commodity_id'] = $count_where['a.commodity_id'] = $name;
                } else {
                    //把搜索名字改成商品ID
                    $count_where['a.commodity_name'] = $flat_goods_where['commodity_name'] = ['like', '%' . $name . '%'];//$where['a.commodity_name']=
                    $flat_goods_ids = $flat->getColumn(['where' => $flat_goods_where, 'column' => 'commodity_id']);
                    $where['a.commodity_id'] = ['in', $flat_goods_ids];

                }
//                $where[] = ['exp', "(a.commodity_id ='{$name}'  or a.commodity_name like '%{$name}%')"];
            }
            if ($comm_type_id) {
                $comm_type_all_id = $this->_goods_type_arr(intval($comm_type_id));
                $where['a.comm_type_id'] = ["in", $comm_type_all_id];
            }

//            if ($comm_type_id) {
//                $comm_type_all_id = $this->_goods_type_arr(intval($comm_type_id));
//                if(!in_array($this->channel_type,['GWSM',"GWAPP","QCSM","QCAPP"])){
//                    $where['a.comm_type_id']=$count_where['a.comm_type_id'] = ["in", $comm_type_all_id];
//                }else{
//                    $where['a.commodity_id'] = ['in',$comm_type_all_id] ;
//                    if(!empty($comm_type_all_id)) {
//                        $order = "FIELD(a.commodity_id,".implode(',',$comm_type_all_id).")";
//                    }
//                }
//            }
            $search_tip = '';
            //新版分类查询条件
            if (!empty($new_comm_type_id)) {
                $type_model = new DbHomeType();
                $type_info = $type_model->getOneByPk($new_comm_type_id);
                $search_tip = $type_info['comm_type_name'];
                $comm_type_all_id = $this->_goods_type_arr(intval($new_comm_type_id), 1);
                $where['a.commodity_id'] = ['in', $comm_type_all_id];
                $count_where['commodity_id']= ['in', $comm_type_all_id];
                if (!empty($comm_type_all_id)) {
                    $order = "FIELD(a.commodity_id," . implode(',', $comm_type_all_id) . ")";
                }
            }

            if ($com_s_types) {
                if (is_numeric($com_s_types)) {
                    $com_s_types = $this->_goods_type_arr(intval($com_s_types));
                }
                $where['a.comm_type_id'] = ["in", $com_s_types];
                $count_where['comm_type_id'] = ["in", $com_s_types];
            }
//            if ($com_s_types) {
//                if (is_numeric($com_s_types)) {
//                    $com_s_types = $this->_goods_type_arr(intval($com_s_types));
//
//                    if(!in_array($this->channel_type,['GWSM',"GWAPP","QCSM","QCAPP"])){
//                        $where['a.comm_type_id']=$count_where['a.comm_type_id'] = ["in", $com_s_types];
//                    }else{
//                        $where['a.commodity_id'] = ['in',$com_s_types] ;
//                        if(!empty($com_s_types)){
//                            $order .= "FIELD(a.commodity_id,".implode(',',$com_s_types).")";
//                        }
//                    }
//
//                }else{
//                    if(!in_array($this->channel_type,['GWSM',"GWAPP","QCSM","QCAPP"])){
//                        $where['a.comm_type_id']=$count_where['a.comm_type_id'] = ["in", $com_s_types];
//                    }else{
//                        $com_s_types = $this->_goods_type_arr($com_s_types,2);
//                        $where['a.commodity_id'] = ['in',$com_s_types] ;
//                        if(!empty($com_s_types)){
//                            $order .= "FIELD(a.commodity_id,".implode(',',$com_s_types).")";
//                        }
//                    }
//                }
//            }

            if (!empty($commodity_ids)) {
                //                $arr                   = explode(',', $commodity_ids);
                $where[] = ['exp', sprintf(" a.commodity_id in (%s)", $commodity_ids)];
                $count_where[] = ['exp', sprintf(" a.commodity_id in (%s)", $commodity_ids)];
            }
            //            if ($car_s_id) {
            //                $car_s_id = intval($car_s_id);
            //                $where[]  = ['exp', " (find_in_set({$car_s_id},a.car_series_id) || a.car_series_id='') "];
            //            }
            //sy版本，车型关联sku表
            if (!$car_s_id) {
                $car_s_id = $user['car_series_id'] ?? '';
            }
            $user_car_date = '';
            $car_where_arr = [];

            //1保养套餐-老友惠保养套餐,3保养套餐-心悦保养套餐,4保养套餐-五年双保升级权益套餐.
            //没有城市时候默认的t1最低

            $dd_goods_type_not_price = "1,3,4";
//            $dlr_level = 'A';
//            $user_info_redis_name = 'userinfobyid' . $user['id'] .$car_s_id . $lng . $lat. $kilometer;
//            $user_info_str = redis($user_info_redis_name);
//            if (!$user_info_str) {
//                $user_info = $this->getFriendBaseInfo($user, $lng, $lat);
//                redis($user_info_redis_name, json_encode($user_info), 600);
//            } else {
//                $user_info = json_decode($user_info_str, true);
//            }
//            if (empty($dd_dlr_code) || $dd_dlr_code == 'V0000') {
//                $dd_dlr_code = $user_info['dlr_code'];
//            }
//            $user_vin = $user_info['vin'];
//            if (!empty($dd_dlr_code) && $dd_dlr_code != 'V0000') {
//                $dlrObj = new DbDlr();
//                $dlr_info = $dlrObj->alias("a")->join("t_db_area b", "a.area_id=b.area_id")->where(['a.dlr_code' => $dd_dlr_code, 'a.is_enable' => 1])->field("b.city_type,a.dlr_name,b.brand_city_type")->find();
//                if (in_array($this->channel_type, ['QCSM', 'QCAPP'])) {
//                    $dlr_level = $dlr_info['brand_city_type'];
//                } else {
//                    $dlr_level = $dlr_info['city_type'];
//                }
//            }
//            $maintain_type = '8.0';
//            $maintain_upgrade_type = 0;
//            $maintain_times = 3;
//            $xy_can_buy = 0;
//            $wn_can_buy = 0;
//            if ($user_vin) {
//                $maintain_type_info = $this->vehicleAge2($user_vin, $kilometer);
//                $maintain_type = $maintain_type_info['type'];// 最高8折默认
//                $xy_can_buy = $maintain_type_info['can_buy'];
//                $maintain_type_info_b = $this->maintion_info($user_vin);
//                // $data =['can_buy'=>0,'upgrade_type'=>$upgrade_type,'times'=>$times,'can_buy_word'=>'无升级次数'];
//                $wn_can_buy = $maintain_type_info_b['can_buy'];
//                $maintain_upgrade_type = $maintain_type_info_b['upgrade_type'];
//                $maintain_times = $maintain_type_info_b['times'];
//            }
            //直接封装在这里去处理 前置套餐查询
            $tc_qz = $this->tc_zg($user, $lng, $lat, $kilometer, $dd_dlr_code);
            $dlr_code = $tc_qz['dlr_code'];
            $dlr_name = $tc_qz['dlr_name'];
            $dlr_level = $tc_qz['dlr_level'];
            $maintain_type = $tc_qz['maintain_type'];
            $maintain_upgrade_type = $tc_qz['maintain_upgrade_type'];
            $maintain_times = $tc_qz['maintain_times'];
            $xy_can_buy = $tc_qz['xy_can_buy'];
            $wn_can_buy = $tc_qz['wn_can_buy'];
            $user_info = $tc_qz['user_info'];
            $user_is_sb = $tc_qz['is_sb'] ?? 0;
            $user_vin = $user_info['vin'];
            Logger::error($api_start_at . '-goodslistuntime-0', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type);

            $where[] = ['exp', " (find_in_set('{$dlr_level}',d.city_type) || d.city_type='' || d.city_type is null )  "];//城市级别 1,老友惠默认要有城市几杯 || a.dd_commodity_type  in (3,4,41,12) 必须都匹配-焕新
            if ($dlr_code) {
                $where[] = ['exp', " (find_in_set('{$dlr_code}',d.relate_dlr_code) || d.relate_dlr_code='' || d.relate_dlr_code is null ) "];//KV 关联了价格
            }
            if ($car_s_id) {
                $car_s_id = intval($car_s_id);
//                $where[]  = ['exp', " (find_in_set({$car_s_id},c.relate_car_ids) || c.relate_car_ids='' || c.relate_car_ids is null) "];
                $car_where_arr = ['exp', " ((find_in_set({$car_s_id},c.relate_car_ids) || c.relate_car_ids='' || c.relate_car_ids is null)  )"];//|| a.dd_commodity_type  in (1,3,4,41,12) 所有的都要适配
//                $user_info = $this->getFriendBaseInfo($user);
                $user_car_date = isset($user['car_offline_date']) ? $user['car_offline_date'] : $user_info['car_offline_date'];//车型下架时间
            }
            $user_oil_type_s = isset($user['18_oil_type']) ? $user['18_oil_type'] : 4;
            if (in_array($this->channel_type, ['QCAPP', 'QCSM'])) {
                if ($user_oil_type_s == 4) {
                    $user_oil_type_s = '3.5,4';
                }
            }
            if(!$user_oil_type_s){
                $user_oil_type_s = "4";
            }
            if(!$xy_can_buy && !$wn_can_buy){
                $where[] = ["exp", sprintf("d.oli_liters in (%s) || d.oli_liters =''", '99900')];
            }else{
                $where[] = ["exp", sprintf("d.oli_liters in (%s) || d.oli_liters =''", $user_oil_type_s)];
            }
//            $where[] = ['exp', " ((d.maintain_q='{$maintain_type}' || d.maintain_q='' ) || a.dd_commodity_type not in (3,12))"];//折扣
            if($user_is_sb){
                $sb_main_q = '7.0';
            }else{
                $sb_main_q = '99';
            }

            $where[] = ['exp', " ((d.maintain_q='{$maintain_type}' and  a.dd_commodity_type=3 ) || (d.maintain_q='{$sb_main_q}' and  a.dd_commodity_type=12 ) || a.dd_commodity_type not in (3,12) )"];//折扣   所有的都要适配--焕新


            $card_goods_model = new DbCommodityCard();
            $and_where = '';
            $card_use_goods_arr = [];
            if ($card_id) {
                $card_id = (string)($card_id); # 7:限时购不和card同时出现 --- 2023-10-19 删除此逻辑
                $where[] = ['exp', " (find_in_set({$card_id},a.card_id)) "];
                $count_where[] = ['exp', " (find_in_set({$card_id},card_id)) "];

                $and_where = " and find_in_set($card_id,card_c.card_id) ";
                //如果配置了sku,就要查sku
                $card_info_where = ['card_id' => $card_id,'is_enable'=>1];
//                $card_info_where[] =['exp', sprintf(" set_sku_ids is not null and set_sku_ids!=''")];
                $card_info_list = $card_goods_model->getList(['where' =>$card_info_where]);// ,'column'=>'set_sku_ids'
                if($card_info_list){
                    $card_set_sku_ids = [];
                    $card_cc_goods_ids = [];
                    $card_class_str = '';
                    foreach ($card_info_list as $cc_gv){
                        if($cc_gv['set_sku_ids']){
                            foreach (explode(',',$cc_gv['set_sku_ids']) as $cc_gv_sku){
                                $card_use_goods_arr[$cc_gv['card_id']][$cc_gv['commodity_id']][]=$cc_gv_sku;
                                $card_set_sku_ids[]=$cc_gv_sku;

                            }
                        }elseif($cc_gv['commodity_id']){
                            $card_use_goods_arr[$cc_gv['card_id']][$cc_gv['commodity_id']][]=$this->te_card_num;
                            $card_cc_goods_ids[]=$cc_gv['commodity_id'];
                        }
                        if($cc_gv['class_id']){
                            $card_class_str.=sprintf(" find_in_set('%s',a.comm_type_id_str) ||",$cc_gv['class_id']);
                        }
                    }
                    $card_set_sku_ids_str = implode(',',$card_set_sku_ids);
                    $card_cc_goods_ids_sql = '';
                    if($card_cc_goods_ids){
                        $card_cc_goods_ids_arr = implode(',',$card_cc_goods_ids);
                        if($card_class_str){
                            $card_cc_goods_ids_sql = sprintf(" || a.commodity_id in (%s)",$card_cc_goods_ids_arr);
                        }else{
                            $card_cc_goods_ids_sql = sprintf("a.commodity_id in (%s)",$card_cc_goods_ids_arr);

                        }
                    }
                    if($card_set_sku_ids){
                        //不在这里判断，在下面过滤
//                        $where[] = ['exp', sprintf(" (c.id in ({$card_set_sku_ids_str}) %s %s) ",$card_class_str,$card_cc_goods_ids_sql)];
                    }else{
                        $where[] = ['exp', sprintf(" ( %s %s) ",trim($card_class_str,'||'),$card_cc_goods_ids_sql)];
                    }
                }
            }
            if ($price_s) {
                $where[] = ["exp", "c.price>=" . $price_s];
                $count_where[]=["exp", "final_price>=" . $price_s];
                //                $where['c.price'] = [">=", $price_s];
            }

            $date_car_where = [];
            if ($user_car_date) {
                $user_car_date = date('Y/m', strtotime($user_car_date));//只需要判断月份即可--要这个 2023-04-11 10:02:36
                //$where[] = ['exp', sprintf("  (unix_timestamp(d.part_start_time)<='%s' ||  d.part_start_time ='') and (unix_timestamp(d.part_end_time)>'%s' || d.part_end_time ='') ", $user_car_date,$user_car_date)];//这个不要了 2023-04-11 10:02:33
                $date_car_where = ['exp', sprintf(" ((d.part_start_time<='%s' ||  d.part_start_time ='') and (d.part_end_time>='%s' || d.part_end_time ='') ) ", $user_car_date, $user_car_date)];//要这个 2023-04-11 10:02:30  || a.dd_commodity_type  in (1,3,4,41,12) 焕新，必须都匹配
            }
            if ($price_e) {
                $where[] = ["exp", "c.price<=" . $price_e];
                $count_where[]=["exp", "final_price<=" . $price_e];
            }
            #销售渠道
            if (!isset($requestData['miao_sha']) && !$card_id) {
                $where[] = ['exp', " find_in_set(1,a.sales_channel)"];
            }

            //zlq 有卡券的时候取活动+商城
            if ($card_id) {
                $where[] = ['exp', " find_in_set(1,a.sales_channel) or find_in_set(4,a.sales_channel)"];
            }
            //PZ1A 先不过滤车型.
            if (!in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                if ($car_where_arr) {
                    $where[] = $car_where_arr;
                }
                if ($date_car_where) {
                    $where[] = $date_car_where;
                }
            }
            if ($sku_ids) {
                $where['c.id'] = ['in', $sku_ids];
            }
            //心悦能买-- 上面已经做了对应过滤
            if ($xy_can_buy) {
//                $where[] = ['exp', " ((d.maintain_q='{$maintain_type}' || d.maintain_q='' ) || a.dd_commodity_type<>3)"];//折扣
            }
//            if($wn_can_buy){
//                $where[] = ['exp', " ((d.maintain_q='{$maintain_type}' || d.maintain_q='' ) || a.dd_commodity_type<>3)"];//折扣
//                $where[] = ['exp', " (d.maintain_num<={$maintain_times} || d.maintain_num='') "];//升级次数
//            }

            $where[] = ['exp', "( (d.maintain_num<={$maintain_times} || d.maintain_num='') || a.dd_commodity_type <> 4 )"];//升级次数
            $where[] = ['exp', " ((b.listing_type=2 and  a.crowdfund_dis like '%{$this->channel_type}%' )|| b.listing_type=1) "];//众筹上架+配置众筹活动


            //a.price,a.final_price, 替换成 min(c.price) price,max(c.price) final_price,a.price,a.final_price,,c.relate_car_ids 列表没有活动就没有划线价20221019 T
            //GROUP_CONCAT( CONCAT(d.commodity_id,d.sp_value_list) SEPARATOR ';' ) ss_plist ,
            //GROUP_CONCAT( d.rep_part_no ) d_rep_part_no,
            //GROUP_CONCAT(d.sku_code) d_sku_code,
            //GROUP_CONCAT(d.variety_code) d_v_code,
            //GROUP_CONCAT(d.maintain_q) gc_maintain_q,
            //GROUP_CONCAT(d.price) gc_old_price,
            //,GROUP_CONCAT(card_c.card_id) gc_g_card_id
            //GROUP_CONCAT(card_c.group_sub_commodity_id) gc_g_goods_set_id,
            $field = "a.commodity_id,a.commodity_name,a.tag,a.tag_gwnet,a.tag_gwapp,a.is_pure,a.cover_image,a.card_id,b.count_stock,a.sales_channel,a.cheap_dis,a.group_dis,a.full_dis,a.limit_dis,a.seckill_dis,a.n_dis,a.pre_dis,a.car_series_id,a.seckill_dis,min(c.price) price,min(c.price) final_price ,b.max_point,b.pay_style,a.tag_pz1asm,a.tag_pz1aapp,a.tag_qcsm,a.tag_qcapp,a.is_grouped,b.commodity_label,GROUP_CONCAT(c.id) gc_id,GROUP_CONCAT(c.price) gc_price,b.group_commodity_ids_info,b.is_sp_associated,a.commodity_set_id,
            b.qsc_group,b.qsc_group_price,b.first_free_price,b.qsc_group_num,b.qsc_group_name,a.gift_dis,a.dd_commodity_type ,b.is_store,a.comm_type_id,GROUP_CONCAT( c.stock ) gc_stock,c.relate_car_ids,b.listing_type,a.activity_image,a.comm_type_id_str,b.tag_zdy,b.mail_type mail_method";//,a.sku_code,a.rep_part_no,sku.sku_code re_sku_code,
//            $d_repart_no_arr = explode(',', $v['d_rep_part_no']);
//            $d_sku_code_arr = explode(',', $v['d_sku_code']);
//            $d_v_code_arr = explode(',', $v['d_v_code']);
//            $gc_maintain_q_arr = explode(',', $v['gc_maintain_q']);
//            $gc_old_price_arr = explode(',', $v['gc_old_price']);

            //实际的PZ字段--有车
            $pz_re_field = "c.price,c.price final_price,GROUP_CONCAT(c.id) gc_id,GROUP_CONCAT(c.price) gc_price,GROUP_CONCAT(d.sku_code) d_sku_code,GROUP_CONCAT( d.rep_part_no ) d_rep_part_no,GROUP_CONCAT( CONCAT(d.commodity_id,d.sp_value_list) SEPARATOR ';' ) ss_plist  ,c.commodity_id,GROUP_CONCAT(d.price) gc_old_price,GROUP_CONCAT(d.maintain_q) gc_maintain_q,GROUP_CONCAT( c.stock ) gc_stock,GROUP_CONCAT(d.variety_code) d_v_code";//PZ没有保养套餐 ,d.price old_sku_price 暂时不需要
            if (empty($order)) {
                $order = "a.last_updated_date asc";
            }
            if($new_order){
                //c.price
                //sale_number 销量,price_asc 价格升序,price_desc价格降序,new_goods新品
                if($new_order=='sale_number'){
                    $order = "b.front_sale_num desc";
                }
                if($new_order=='price_asc'){
                    $order = "c.price asc";
                }
                if($new_order=='price_desc'){
                    $order = "c.price desc";
                }
                if($new_order=='new_goods' || $new_order=='new_good'){
                    $order = "b.created_date desc";
                }
            }
            $params = array(
                'where' => $where,
                'group' => "a.commodity_id", //暂时看看这个能不能读取出组合商品最低最高价,d.id
                'pagesize' => $page_size,
                //                'limit' => sprintf("%s,%s",$page*$page_size,$page_size),
                // 'order' => $order, #商品排序规则和上架时间
                'order' => $order, #商品排序规则和上架时间
                'field' => $field,
                'and_where' => $and_where,
                'query' => ['page'=>$page],

            );
            //l列表划线价==商品管理的sku最低价
            //显示价格==商品上架sku_最低价
            Logger::error($api_start_at . '-goodslistuntime-1', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type);

            //组合商品==最低价之和 原价==现价
            $user_redis_name = "goods-list-user" . $car_s_id . md5(json_encode($where)) . $page . $n_dis_id . $full_cut_id.$new_order;
            $all_list = redis($user_redis_name);
            $list=[];
            $gift_card_sku_arr = [];
            $gift_card_sku_class_arr = [];
            if(isset($user['card_r_gift_wjh'])){
                if($user['card_r_gift_wjh']){
                    $wjh_card_act_id = array_column($user['card_r_gift_wjh'],'activity_id');
                    $gift_card_rule_list= $this->gift_card_rule($wjh_card_act_id);
//                    print_json($gift_card_rule_list);
                    $gift_card_sku_arr          = $gift_card_rule_list['gift_card_sku_arr'];
                    $gift_card_sku_class_arr    = $gift_card_rule_list['gift_card_sku_class_arr'];
                }
            }
//            print_json($gift_card_rule_list);
            $user_info['gift_card_sku_arr'] = $gift_card_sku_arr;
            $user_info['gift_card_sku_class_arr'] = $gift_card_sku_class_arr;
            if (!$all_list) {
                $pre_model = new DbPreSale();
                $limit_goods_model = new DbLimitDiscountCommodity();
                $limit_model = new DbLimitDiscount();
                $set_sku_model = new DbCommoditySetSku();
                $gift_model = new DbGift();
                $n_dis_model = new DbNDiscount();
                $full_dis_model = new DbFullDiscount();
                $gift_commodity = new DbGiftCommodity();
                try {
//                    $sql1 = "SET GLOBAL group_concat_max_len=100000;";
                    $sql2 = " SET SESSION group_concat_max_len=100000;";
//                    $flat->query($sql1);
                    $flat->query($sql2);
                } catch (Exception $e) {
//                dd($e->getMessage());
                }
                Logger::error($api_start_at . '-goodslistuntime-1.0', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type);
                $count = $flat->alias('a')->where($count_where)->count();
                $params['count'] = $count;
                Logger::error($api_start_at . '-goodslistuntime-1.1', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type);
                $list = $flat->getCommodityListUSku($params);
//                print_json($flat->getLastSql(),$list);
                Logger::error($api_start_at . '-goodslistuntime-2', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type);


//            print_json($list);
//                Logger::debug($api_start_at.'-goods_list_sql:' . $flat->getLastSql());
            Logger::error('net-goods-prams:', [
                 'pp' => $params, 'cc_id' => array_column(collection($list)->toArray(), 'commodity_set_id'), 'sql' => $flat->getLastSql()]);
                $card_id_arr = [];
                $goods_set_id_arr = [];
                foreach ($list as $kk => $tmp_vv) {
                    $list[$kk]['is_pp'] = 1;//chongtu
                    $old_cover_image = $list[$kk]['cover_image'];
                    if(!empty($tmp_vv['activity_image'])){
                        $list[$kk]['cover_image'] = $tmp_vv['activity_image'];
                    }

                    if (in_array($from,['special', 'home']) && $type == 'cGoods') {
                        $list[$kk]['cover_image'] = $old_cover_image;
                    }
                    if ($tmp_vv['card_id']) {
                        $card_id_arr = array_merge($card_id_arr,explode(',', $tmp_vv['card_id']));
                    }
                    $goods_set_id_arr[] = $tmp_vv['commodity_set_id'];

//
//                }
//                //commodity_card处理为数组
//                $goods_card = $this->_goods_card_arr([],$goods_set_id_arr);
//                $goods_card_arr = $goods_card['goods_card_arr'];
//                $goods_card_set_id = $goods_card['goods_card'];
//                $can_card_list = $this->user_can_card($goods_set_id_arr,$user,$channel_type);
//                if($can_card_list){
//                    $can_card_id_arr = [];
////                    print_json($can_card_list);
//                    foreach ($can_card_list as $can_v){
//                        if($can_v['can_use_card']>0 || $can_v['can_get']==1){
//                            $can_card_id_arr[]=$can_v['id'];
//                        }
//                    }
//                    $can_card_ids = array_intersect($goods_card['cards'],$can_card_id_arr);
//                    $goods_card['cards'] = $can_card_ids;
//                }else{
//                    $goods_card['cards'] = [];
//                    $goods_card_arr=[];
                }
                // 最新版，关联卡券
                if(!$from){
                    $from = 'goodslist';
                }
                //$card_id --入参
                $card_in_attr=[];
                if($card_id){
                    $card_in_attr =  $card_id;
                }else{
                    if($card_id_arr){
                        $card_in_attr = $card_id_arr;
                    }
                }
                if($card_in_attr){
                    $card_get_use = $this->card_get_use($goods_set_id_arr,[],$user, $this->channel_type,$card_in_attr,'',1,[],'',$from,'',99,$use_gift_card);
                }else{
                    $card_get_use=['get_card_list'=>[],'all_card'=>[],'card_rules'=>[],'goods_card_rule'=>[],'not_article_work'=>'','push_type_err'=>''];
                }
//                $card_get_use = $this->card_get_use($goods_set_id_arr,[],$user, $this->channel_type,$card_in_attr,'',1,[],'',$from,'',99,$use_gift_card);
                $all_card = $card_get_use['all_card'];//获取卡券列表
                $goods_card_rule = $card_get_use['goods_card_rule'];//可领可用卡券中每个商品对应的卡券列表
                $card_rules = $card_get_use['goods_card_rule'];//获取卡券列表
                $get_card_list = $card_get_use['get_card_list'];//可领取卡券列表
                $pp_card_ids = [];
                $not_pp_card_ids = [];

                $tmp_set_id = [];
                foreach ($list as $kk => $tmp_vv) {
                    if (in_array($tmp_vv['dd_commodity_type'], [1, 3, 4, 41,12])) {
                        $tmp_set_id[] = $tmp_vv['commodity_set_id'];
                        $list[$kk]['is_pp'] = 0;
                    } else {
                        if (in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP']) && $tmp_vv['dd_commodity_type'] > 0 && !empty($car_s_id)) {
                            $list[$kk]['is_pp'] = 0;
                        } else {
                            $list[$kk]['is_pp'] = 1;
                        }
                    }
                }
                //新版在上面的已经完整过滤
//                if (in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
//                    if ($car_where_arr || $date_car_where) {
//                        $tmp_set_id = [];
//                        foreach ($list as $kk => $tmp_v) {
//                            $tmp_set_id[] = $tmp_v['commodity_set_id'];
//                        }
//
//                        $list_c_set_id_arr = array_column(collection($list)->toArray(), 'commodity_set_id');
//                        $pz_re_where = ['c.commodity_set_id' => ['in', $tmp_set_id], 'c.is_enable' => 1, 'd.is_enable' => 1];
//                        if ($car_where_arr) {
//                            $pz_re_where[] = $car_where_arr;
//                        }
//                        if ($date_car_where) {
//                            $pz_re_where[] = $date_car_where;
//                        }
//                        $re_pz_list = $this->set_sku_model->alias('c')->join("t_db_commodity_sku d", "c.commodity_sku_id=d.id")->join('t_db_commodity_flat a', "a.commodity_set_id=c.commodity_set_id")->where($pz_re_where)->field($pz_re_field)->group('c.commodity_id')->select();
//                        Logger::error('goods_pz_list_sql:' . $this->set_sku_model->getLastSql());
//
//                        if ($re_pz_list) {
//                            foreach ($list as $kk => $v_list) {
//                                foreach ($re_pz_list as $re_v) {
//                                    if ($v_list['commodity_id'] == $re_v['commodity_id']) {
//                                        $list[$kk]['price'] = $re_v['price'];
//                                        $list[$kk]['final_price'] = $re_v['final_price'];
////                                    $list[$kk]['price'] = $list[$kk]['final_price'] = min(explode(',', $re_v['final_price']));
//                                        $list[$kk]['gc_id'] = $re_v['gc_id'];
//                                        $list[$kk]['gc_price'] = $re_v['gc_price'];
//                                        $list[$kk]['d_v_code'] = $re_v['d_v_code'];
//                                        $list[$kk]['d_rep_part_no'] = $re_v['d_rep_part_no'];
//                                        $list[$kk]['ss_plist'] = $re_v['ss_plist'];
//                                        $list[$kk]['gc_maintain_q'] = $re_v['gc_maintain_q'];
//                                        $list[$kk]['is_pp'] = 1;
//
//                                    }
//
//                                }
//                            }
//                        }
//                    }
//                }
//                Logger::error('goods_list_sql:'.$flat->getLastSql());
//                        echo $flat->getLastSql();die();
                Logger::error($api_start_at . '-goodslistuntime-3', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type);
                $segment_info = get_user_segment_info();

                $brand_id = Session::has('net_api-brand') ? Session::get('net_api-brand') : 1;
                $commodity_segment_cache_key = "CommoditySegmentDis:{$brand_id}";
                $segment_arr = Cache::get($commodity_segment_cache_key);
                foreach ($list as $k => $v) {
                    $gc_id_arr = explode(',', $v['gc_id']);//set_sku_id列表
//                    $ss_plist_arr = explode(';', $v['ss_plist']);//对应sp_value_数组;
//                    $d_repart_no_arr = explode(',', $v['d_rep_part_no']);
//                    $d_sku_code_arr = explode(',', $v['d_sku_code']);
//                    $d_v_code_arr = explode(',', $v['d_v_code']);
//                    $gc_maintain_q_arr = explode(',', $v['gc_maintain_q']);
//                    $gc_old_price_arr = explode(',', $v['gc_old_price']);
                    $gc_price_arr = explode(',', $v['gc_price']);

                    $card_rule =  $card_rules[$v['commodity_set_id']]??[];//单个商品卡券规则

                    //备件时间过滤开始--1120对应注释
//                foreach ($d_sku_code_arr as $kkk=> $vvv){
//                    if(!in_array($vvv,$d_repart_no_arr) && !empty($vvv) && !empty($d_repart_no_arr[$kkk]) && array_count_values($ss_plist_arr)[$ss_plist_arr[$kkk]]>1){
//                        unset($gc_id_arr[$kkk]);
//                        unset($ss_plist_arr[$kkk]);
//                        unset($gc_price_arr[$kkk]);
//                    }
//                }
//                if(count($ss_plist_arr)>count(array_unique($ss_plist_arr))){
//                    $un_ss_arr = array_unique($ss_plist_arr);
//                    $re_ss_arr = array_diff_assoc($ss_plist_arr,$un_ss_arr);
//                    foreach ($ss_plist_arr as $ss_kkk=>$ss_vvv){
//                        if(in_array($ss_vvv,$re_ss_arr) && !empty($ss_vvv) && isset($user['id'])){
//                            $this->error_vin_goods(isset($user_info['vin'])?$user_info['vin']:$user['id'],['sp'=>$ss_vvv,'goods_id'=>$v['commodity_id']],'list');
//                            unset($gc_id_arr[$ss_kkk]);
//                            unset($ss_plist_arr[$ss_kkk]);
//                            unset($gc_price_arr[$ss_kkk]);
//                        }
//                    }
//                }
//                if(empty($gc_price_arr)){
//                    unset($list[$k]);
//                    continue;
//                }


                    //按照商品详情页面逻辑 存正常的sku
                    $list[$k]['old_gc'] = $gc_id_arr;


                    $de_sku_id_arr = $this->sku_model->alias('a')->join('t_db_commodity_set_sku b','a.id=b.commodity_sku_id  and a.is_enable=1 and b.is_enable=1')->where([
                        'b.id'=>['in',$gc_id_arr]
//                        'b.stock'=>['>',0]
                    ])->field("a.id,a.sp_value_list,b.price,b.id,b.id bid,a.sku_code,a.variety_code,a.maintain_q")->order('b.price DESC,a.id DESC,b.id DESC')->group('a.id')->select();
//                    if(!$de_sku_id_arr){
//                        unset($list[$k]);
//                        continue;
//                    }
                    $gift_card_sku_code = [];
                    $gift_card_variety_code = [];
                    $de_sku_id_list = [];
                    $gc_price_arr_new=[];
                    $de_ss_plist = [];
                    foreach ($de_sku_id_arr as $de_v){
//                        $gift_card_sku_code[$de_v['sp_value_list']] = [];
//                        $gift_card_variety_code[$de_v['sp_value_list']]=[];
                        if($de_v['sku_code']){
                            foreach (explode(',',$de_v['sku_code']) as $sku_c_v){
                                if($sku_c_v){
//                                    $gift_card_sku_code[$de_v['sp_value_list']][]=$sku_c_v;
                                    $gift_card_sku_code[]=$sku_c_v;
                                }
                            }
                            unset($sku_c_v);
                            foreach (explode(',',$de_v['variety_code']) as $sku_c_v){
                                if($sku_c_v){
                                    $gift_card_variety_code[]=$sku_c_v;
                                }
                            }
                            unset($sku_c_v);
                        }
                        $de_sku_id_list[$de_v['sp_value_list']] = $de_v['bid'];
                        $gc_price_arr_new[$de_v['sp_value_list']] = $de_v['price'];
                        $gc_maintain_q_arr_new[$de_v['sp_value_list']] = $de_v['maintain_q'];
                        $de_ss_plist = $de_v['sp_value_list'];

                    }
//                    print_json($gift_card_sku_arr,$gift_card_sku_code,$de_sku_id_arr);
                    $d_sku_code_arr =  array_values($gift_card_sku_code);
                    $d_v_code_arr =  array_values($gift_card_variety_code);
                    $de_sku_id_list_arr = array_values($de_sku_id_list);
                    $one_price =  min($gc_price_arr_new);

//                    try {
//                        $one_price =  min($gc_price_arr_new);
//
//                    }catch (Exception $e){
//                        print_json($de_sku_id_arr,$this->sku_model->getLastSql());
//                    }
//备件时间过滤开始--1120对应注释--end
                    $re_m_q_sku_k = array_search($one_price, $gc_price_arr_new);
                    $list[$k]['maintain_q'] = $gc_maintain_q_arr_new[$re_m_q_sku_k];
                    $list[$k]['de_sku_id'] = $de_sku_id_list[$re_m_q_sku_k];
//                    try {
//                        $list[$k]['de_sku_id'] = $de_sku_id_list[$re_m_q_sku_k];
//
//                    }catch (Exception $e){
//                        print_json($de_sku_id_list,$gc_price_arr_new);
//                    }
                    $list[$k]['new_gc'] = $de_sku_id_list_arr;

                    if($card_id){
                        $card_rel_sku_id_arr=[];
//                        Logger::error('ngcard',['ccc'=>$card_use_goods_arr[$card_id][$v['commodity_id']],'te_card_num'=>$this->te_card_num,'de_sku_id_list_arr'=>$de_sku_id_list_arr,'card_use_goods_arr'=>$card_use_goods_arr]);
                        if(isset($card_use_goods_arr[$card_id][$v['commodity_id']])){
                            $card_rel_sku_id_arr = $card_use_goods_arr[$card_id][$v['commodity_id']];
                            if($card_rel_sku_id_arr[0]!=$this->te_card_num){
                                if(array_intersect($card_rel_sku_id_arr,$de_sku_id_list_arr)){
                                    foreach ($de_sku_id_list as $de_v_key=>$de_v_v){
                                        if(in_array($de_v_v,array_intersect($card_rel_sku_id_arr,$de_sku_id_list_arr))){
                                            $de_ss_plist=$de_v_key;
                                            $list[$k]['de_sku_id'] = $de_v_v;
                                            $one_price = $gc_price_arr_new[$de_v_key];
                                        }else{
                                            unset($de_sku_id_list[$de_v_key]);
                                        }
                                    }

                                }else{
                                    unset($list[$k]);
                                    continue;
                                }
                            }

                        }
                        $list[$k]['card_gc'] = $card_rel_sku_id_arr;
                        $list[$k]['card_de_ss_plist'] = $de_ss_plist;
                    }

                    $card_rule_goods_info =[
                        ['goods_set_id' => $v['commodity_set_id'], 'sub_goods_id' => '', 'sku_id' => $de_sku_id_list_arr]
                    ];

                    $list[$k]['final_price'] = $list[$k]['price'] = $list[$k]['current_price'] = $list[$k]['b_act_price'] = $v['final_price'] =$one_price;


//                    $list[$k]['de_ss_plist'] = str_replace($v['commodity_id'], "", $ss_plist_arr[$re_m_q_sku_k]);
                    $list[$k]['de_ss_plist'] = $de_ss_plist;
                    $list[$k]['is_have_car'] = 1;
                    $list[$k]['commodity_dis_user_segment'] = 0;
                    $list[$k]['commodity_dis_act_user_segment'] = 0;
                    $list[$k]['commodity_dis_label'] = '';
                    $list[$k]['commodity_dis_label_cn'] = '';

                    $can_not_with_promotion = 0;
                    // 获取会员价
                    if ($v['listing_type'] == 1) {
                        $commodity_dis_info = $this->getCommoditySegmentDiscount($v['commodity_id'], $brand_id, 0, $segment_arr);
                    } else {
                        $commodity_dis_info = [];
                    }
//                    Logger::error($api_start_at.'-goodslistuntime-4', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-".$user['id'].'-'.$v['commodity_id']);
//                    print_json($goods_card_set_id[$v['commodity_set_id']]);
                    if(isset($goods_card['cards'])){
//                        $goods_un_card_arr = explode(",",$v['card_id']);
//                        $goods_un_card_arr = array_intersect($goods_un_card_arr,$goods_card['cards']);
                        $goods_un_card_arr = array_intersect($goods_card_set_id[$v['commodity_set_id']]??[],$goods_card['cards']);
//                        $goods_un_card_arr = $goods_card_set_id[$v['commodity_set_id']]??[];

                    }else{
                        $goods_un_card_arr=[];
                    }

                    if ($v['is_grouped']) {

                        //需要判断油型之后  commodity_id拿出来
                        $g_com_count = [];
                        $g_all_goods_id = [];
                        $g_info_ids = json_decode($v['group_commodity_ids_info'], true);
                        //查询匹配的setsku对应的子商品setsku有哪些
                        $set_sku_group = $this->set_sku_model->getColumn(['where' => ['id' => ['in', array_values(array_unique($gc_id_arr))]], 'column' => 'group_sub_set_sku_id']);

//                        print_json($gc_id_arr);


                        $goods_id_group = $this->set_sku_model->getColumn(['where' => ['id' => ['in', array_values(array_unique($gc_id_arr))]], 'column' => 'group_sub_commodity_id']);

                        $g_must = [];
//                                        dd($g_info_ids);
                        $not_kz_sub_goods=[];
                        $card_rule_goods_info=[];
                        if ($g_info_ids) {
                            $isallcancount = 0;
                            $isallcancountids = [];
                            $all_sku_jj = [];
                            foreach ($g_info_ids as $g_vv) {
                                if ($g_vv['machine_oil_type'] == 1) { //1L机油商品
                                    //判断18n是否》4L。  initial_num...
                                    if ($car_s_id) {
                                        if ($user['18_oil_type'] > 4) {
                                            $g_vv['initial_num'] = $user['18_oil_type'] - 4;
                                        } else {
                                            $g_vv['initial_num'] = 0;
                                        }

                                    } else {
                                        $g_vv['initial_num'] = 1;
                                    }
                                }
                                if ($g_vv['initial_num'] > 0 && !$g_vv['can_select']) {
                                    $g_all_goods_id[] = $g_vv['commodity_id'];
                                }
                                $g_com_count[$g_vv['commodity_id']] = $g_vv['initial_num'];
                                //0423版本
                                /////组合商品无库存不显示
                                $set_sku_id_arr = [];

                                //这个里面只有group_sub_set_sku_id 也就是子商品的正式ID，没有在组合商品里面的set_sku_id
                                foreach ($g_vv['sku_list'] as $kn => $item) {
                                    $set_sku_id_arr[] = $item['group_sub_set_sku_id'];
                                }
//                                print_json($set_sku_id_arr);
                                //适配的sku_id,针对子商品
                                $sku_jj = array_intersect($set_sku_id_arr, $set_sku_group);
//                                print_json($sku_jj);
                                //如果不需要1L机油，那么就不判断1L机油是否匹配
                                $must_info = $this->set_sku_model->whereIn('id', $set_sku_id_arr)->where(['is_enable' => 1, "stock" => ['>', 0]])->find();
                                if(empty($must_info)){
                                    $not_kz_sub_goods[]=$g_vv['commodity_id'];
                                    //有库存的进入对比判断可用券列表
                                }else{
                                    $card_rule_goods_info[]=['goods_set_id' => $v['commodity_set_id'], 'sub_goods_id' => $g_vv['commodity_id'], 'sku_id' => $g_vv['sku_id_list'],'oil_type'=>$g_vv['machine_oil_type']];
                                }

                                if ($g_vv['can_select'] == 0 && $g_vv['initial_num'] > 0) {
                                    if (!empty($set_sku_id_arr)) {

                                        if (!$sku_jj) {
                                            $g_must_delete[] = $v['commodity_id'];
                                        } else {
                                            if (empty($must_info)) {
                                                $g_must_delete[] = $v['commodity_id'];
                                            }
                                        }

                                    }
                                } else {
                                    $isallcancount++;
                                    foreach ($g_vv['sku_list'] as $kn => $item) {
                                        $isallcancountids[] = $item['group_sub_set_sku_id'];
                                    }

                                    if (in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                                        if (!empty($set_sku_id_arr)) {
//                                            $must_info = $this->set_sku_model->whereIn('id', $set_sku_id_arr)->where(['is_enable' => 1, "stock" => ['>', 0]])->find();
                                            if (empty($must_info)) {
                                                $g_must_delete[] = $v['commodity_id'];
                                            }
                                        }
                                    }
                                }
                                $all_sku_jj = array_merge($all_sku_jj,$sku_jj);
                            }

                            //全是可选商品
                            if ($isallcancount == count($g_info_ids)) {
                                $must_info = $this->set_sku_model->whereIn('id', $isallcancountids)->where(['is_enable' => 1, "stock" => ['>', 0]])->find();
                                if (empty($must_info)) {
                                    $g_must_delete[] = $v['commodity_id'];
                                }
                            }


                            //进入判断组合商品卡券价值  暂时不用这个
                            $goods_id_group =  array_diff($goods_id_group,$not_kz_sub_goods);
//                            if($goods_un_card_arr){
//                                $goods_un_card_arr  =  $this->_mz_card_group($goods_un_card_arr,$goods_card_arr,$v['commodity_set_id'],$gc_id_arr,$goods_id_group,1,$channel_type);
////                                print_json($sku_jj);
//                                $list[$k]['card_list'] = $goods_un_card_arr;
//                            }
                        }



                        $group_where = ['commodity_id' => $v['commodity_id'], 'is_enable' => 1, 'id' => ['in', $gc_id_arr]];
                        $group_field = 'min(price) ff_price,group_sub_commodity_id,GROUP_CONCAT(group_sub_set_sku_id) sub_sku_ids,relate_car_ids,GROUP_CONCAT(id) set_sku_ids';
                        //                    $car_s_id= $this->user['car_series_id'];
                        //                    dd($this->user);
                        if ($car_s_id && !in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                            $car_s_id = intval($car_s_id);
                            $group_where[] = ['exp', " (find_in_set({$car_s_id},relate_car_ids) || relate_car_ids='') "];
                        }
                        $group_list = [];
                        if (!empty($v['is_sp_associated'])) {
                            $sub_commodity_list_arr = $this->set_sku_model->getSubCommidtyList($v['commodity_id'], $car_s_id, $user_car_date);
                            $filter_result = $this->commodityAssociateFilter($v['commodity_set_id'], $sub_commodity_list_arr, true);
                            $group_sub_sku_list = $filter_result['sub_commodity_list_arr'];

                            foreach ($group_sub_sku_list as $arr_v) {
//                                $card_rule_goods_info[]=['goods_set_id' => $v['commodity_set_id'], 'sub_goods_id' => $g_vv['commodity_id'], 'sku_id' => $g_vv['sku_id_list'],'oil_type'=>$g_vv['machine_oil_type']];

                                $group_list[$arr_v['group_sub_commodity_id']]['ff_price'] = $arr_v['price'];
                                $group_list[$arr_v['group_sub_commodity_id']]['group_sub_commodity_id'] = $arr_v['group_sub_commodity_id'];
                                if (!empty($group_list[$arr_v['group_sub_commodity_id']]['sub_sku_ids'])) {
                                    $group_list[$arr_v['group_sub_commodity_id']]['sub_sku_ids'] .= ",{$arr_v['group_sub_set_sku_id']}";
                                } else {
                                    $group_list[$arr_v['group_sub_commodity_id']]['sub_sku_ids'] = $arr_v['group_sub_set_sku_id'];
                                }
                                if (!empty($group_list[$arr_v['group_sub_commodity_id']]['set_sku_ids'])) {
                                    $group_list[$arr_v['group_sub_commodity_id']]['set_sku_ids'] .= ",{$arr_v['set_sku_id']}";
                                } else {
                                    $group_list[$arr_v['group_sub_commodity_id']]['set_sku_ids'] = $arr_v['set_sku_id'];
                                }
                            }
                            $group_list = array_values($group_list);
                        } else {
                            $group_list = $this->set_sku_model->getList(['where' => $group_where, 'field' => $group_field, 'group' => 'group_sub_commodity_id']);
                        }
//                    echo $this->set_sku_model->getLastSql();die();
                        $list_sub_goods_arr = array_column($group_list, 'group_sub_commodity_id');
//                        var_dump($g_all_goods_id);
//                        var_dump($list_sub_goods_arr);
//                        die();
                        //0423版本在下面做了必选商品无库存的过滤--增加一个必选商品如果不匹配，过滤
//                        var_dump($g_all_goods_id);
//                        var_dump($list_sub_goods_arr);
//                        die();
                        if (count($g_all_goods_id) > count($list_sub_goods_arr)) {
                            unset($list[$k]);
                            continue;
                        }

                        if ($group_list) {

                            $ff_price = 0;
                            $ff_count = 0;
                            $list[$k]['is_have_car'] = 0;
                            foreach ($group_list as $g_l_v) {
                                if ($car_s_id || empty($g_l_v['relate_car_ids'])) {
                                    $list[$k]['is_have_car'] = 1;
                                }
//                                print_json($group_list,$card_rule_goods_info);
                                foreach ($card_rule_goods_info as $card_r_k=> $card_rule_v){
                                    if($card_rule_v['sub_goods_id'] == $g_l_v['group_sub_commodity_id'] ){
                                        $card_rule_goods_info[$card_r_k]['sku_id'] = explode(',',$g_l_v['set_sku_ids']);
                                    }
                                }
                                if (isset($g_com_count[$g_l_v['group_sub_commodity_id']]) && $g_com_count[$g_l_v['group_sub_commodity_id']] > 0) {
                                    $sub_count = $this->set_sku_model->getOne(['where' => ['id' => ['in', $g_l_v['sub_sku_ids']], "stock" => ['>', 0]], 'field' => "sum(stock) s_stock"]);
                                    if (!empty($sub_count['s_stock'])) {
                                        $ff_price += bcmul($g_l_v['ff_price'], $g_com_count[$g_l_v['group_sub_commodity_id']], 2);
                                        $ff_count += $sub_count['s_stock'];
                                    }
                                }
                            }
//                            die();
                            //                        echo $this->set_sku_model->getLastSql();
                            //                        print_json($group_list);
                            //                        $ff_price=0;
                            //                        echo $this->set_sku_model->getLastSql();die();
                            //                        dd(array_column(set_sku_model-$group_list, 'ff_price'));
                            //                        $ff_price = array_sum(array_column($group_list, 'ff_price'));
                            $list[$k]['final_price'] = $list[$k]['price'] = $list[$k]['current_price'] = $list[$k]['b_act_price'] = $v['final_price'] = sprintf("%.2f", $ff_price);
                            $list[$k]['count_stock'] = $ff_count;
                        }
                    } else {

                        if (!empty($v['relate_car_ids']) && empty($car_s_id)) {
                            $list[$k]['is_have_car'] = 0;
                        }
                        //如果有卡券关联规则 满足规则在这里写


//                        if($goods_un_card_arr) {
//                            $goods_un_card_arr = $this->_mz_card_group($goods_un_card_arr, $goods_card_arr, $v['commodity_set_id'], $gc_id_arr, [], 2,$channel_type);
//                            $list[$k]['card_list'] = $goods_un_card_arr;
//                        }
                    }
                    $goods_un_card_ids =[];
                    if($card_rule){
                        $goods_un_card_arr = $this->card_list_ok($card_rules,$card_rule_goods_info,$all_card,$v['commodity_set_id']);
                        $goods_un_card_ids  = array_values(array_unique(array_column($goods_un_card_arr,'id')));
                        $list[$k]['card_list'] = $goods_un_card_ids;
                        if($not_pp_card_ids){
                            $not_pp_card_ids =  array_diff($not_pp_card_ids,$goods_un_card_ids);
                        }

//                        $list[$k]['card_un_goods'] = $goods_card_rule[$v['commodity_set_id']];
                        foreach ($card_rule as $a_c_v){
                            if(isset($list[$k]['card_list'])){
                                if(!in_array($a_c_v['id'],$list[$k]['card_list'])){
                                    $not_pp_card_ids[]=$a_c_v['id'];
                                }
                            }

                        }
                    }
                    if($v['commodity_set_id']==9670){
//                        print_json($card_rules);
                    }
                    if($card_id){
                        if(!in_array($card_id,$goods_un_card_ids)){
                            unset($list[$k]);
                            continue;
                        }
                    }

//                    if($all_card){
//                        foreach ($all_card as $a_c_v){
//                            if(isset($list[$k]['card_list'])){
//                                if(!in_array($a_c_v['id'],$list[$k]['card_list'])){
//                                    $not_pp_card_ids[]=$a_c_v['id'];
//                                }
//                            }
//
//                        }
//                    }
//                    print_r($not_pp_card_ids);
//                    print_r($card_rule_goods_info);

                    $pp_card_ids = array_merge($pp_card_ids,$list[$k]['card_list']??[]);
//                        print_json($pp_card_ids);

                    if ($v['pre_dis']  && $v['is_pp'] == 1) {
                        $pre_arr = json_decode($v['pre_dis'], true);
                        if (isset($pre_arr[$this->channel_type][0])) {
                            $can_not_with_promotion = 1;
                            $pre_id = $pre_arr[$this->channel_type][0];
                            $pre_info = $pre_model->getOneByPk($pre_id);

                            if ($pre_info) {
                                $list[$k]['final_price'] = sprintf("%.2f", ($v['final_price'] - $pre_info['dedu_money'] + $pre_info['front_money']));
                            }
                        }
                    }
                    if ($v['group_dis']  && $v['is_pp'] == 1) {
                        //增加判断团购
                        $group_dis_arr = json_decode($v['group_dis'], true);
                        if (isset($group_dis_arr[$this->channel_type][0])) {
                            $can_not_with_promotion = 1;
                            $group_dis_id = $group_dis_arr[$this->channel_type][0];
                            $fight_sku_model = new DbFightGroupCommodity();
                            $where = ['fight_group_id' => $group_dis_id, 'commodity_id' => $v['commodity_id']];
                            $fight_sku_data = $fight_sku_model->getOne(['where' => $where, 'field' => 'sku_price']);

                            if ($fight_sku_data) {
                                $group_goods_sku_price_arr = json_decode($fight_sku_data['sku_price'], true);
                                arsort($group_goods_sku_price_arr);
                                foreach ($group_goods_sku_price_arr as $group_k => $group_v) {
                                    if (!in_array($group_k, $gc_id_arr)) {
                                        unset($group_goods_sku_price_arr[$group_k]);
                                    }
                                }
                                if ($group_goods_sku_price_arr) {
                                    $end_group_goods = end($group_goods_sku_price_arr);
                                    $end_group_goods_key = array_search($end_group_goods, $group_goods_sku_price_arr);

                                    $list[$k]['final_price'] = $end_group_goods;
                                    $price_group_sku_k = array_search($end_group_goods_key, $gc_id_arr);

                                    $list[$k]['price'] = explode(',', $v['gc_price'])[$price_group_sku_k];
                                }
                            }
                        }
                    }

                    $show_limit_label = 0;
                    //sy--增加显示限时购价格 必须匹配
                    if ($v['limit_dis'] && $v['is_pp'] == 1) {
                        $limit_dis_arr = json_decode($v['limit_dis'], true);
                        if (isset($limit_dis_arr[$this->channel_type][0])) {
                            $cheap_dis_id = $limit_dis_arr[$this->channel_type][0];
                            $membership = $segment_info['membership_level'];
                            $owner = $segment_info['brand_owner_label'];
                            $_l_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);

                            // 使用缓存获取限时购活动信息
                            $limit_info = $this->getActivityInfoWithCache('limit_discount', $cheap_dis_id, $membership, $owner, $limit_model);
                            // 使用缓存获取限时购商品信息
                            $limit_goods_info = $this->getActivityGoodsInfoWithCache('limit_discount', $cheap_dis_id, $v['commodity_id'], $limit_goods_model);
                            if ($limit_info && $limit_goods_info) {
                                $limit_info['sku_dis'] = $limit_goods_info['sku_dis'];
                                $limit_info['sku_dis_decoded'] = json_decode_assoc($limit_info['sku_dis']);
                                //应该要读取对应的活动最低价了
                                if ($v['is_grouped']) {
                                    if (in_array($limit_info['discount_type'], [1, 3])) {
                                        $dis_info = $this->getLimitDisByPrice($limit_info, 0, $v['final_price'], $commodity_dis_info);
                                        $list[$k]['current_price'] = $dis_info['current_price'];
                                        $list[$k]['final_price'] = $dis_info['dis_price'];
                                        $list[$k]['commodity_dis_act_user_segment'] = $dis_info['user_segment'];
                                    }
                                    $show_limit_label = 1;
                                } else {
                                    if ($limit_goods_info) {
                                        $min_price_sku_id_price = $gc_price_arr[0];
                                        $min_price_sku_id_old_price = $gc_price_arr[0];
                                        $min_price_sku_id_old_dis_price = $gc_price_arr[0];
                                        $have_limit_discount = 0;

                                        foreach (array_unique($gc_id_arr) as $key => $item_sku_id) {
                                            $dis_info = $this->getLimitDisByPrice($limit_info, $item_sku_id, $gc_price_arr[$key], $commodity_dis_info, $segment_info);

                                            if ($min_price_sku_id_price > $dis_info['dis_price']) {
                                                $min_price_sku_id_price = $dis_info['dis_price'];
                                                $min_price_sku_id_old_price = $gc_price_arr[$key];
                                                $min_price_sku_id_old_dis_price = $dis_info['current_price'];
                                            }

                                            if ($dis_info['have_sku_dis']) {
                                                $show_limit_label = 1;
                                                $have_limit_discount = 1;
                                            }
                                            $list[$k]['commodity_dis_act_user_segment'] = $dis_info['user_segment'];
                                        }
                                        if ($have_limit_discount) {
                                            $list[$k]['final_price'] = $min_price_sku_id_price;
                                            $list[$k]['price'] = $min_price_sku_id_old_price;
                                            $list[$k]['current_price'] = $min_price_sku_id_old_dis_price;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 判断秒杀
                    $show_seckill_label = 0;
                    $show_seckill_label_status = 0;
                    if ($v['seckill_dis'] && $v['is_pp'] == 1) {
                        $seckill_dis_arr = json_decode($v['seckill_dis'], true);
                        if (isset($seckill_dis_arr[$this->channel_type][0])) {
                            $seckill_dis_id = $seckill_dis_arr[$this->channel_type][0];
                            $membership = $segment_info['membership_level'];
                            $owner = $segment_info['brand_owner_label'];
                            $_l_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);

                            $seckill_model = new DbSeckill();
                            $seckill_goods_model = new DbSeckillCommodity();
                            $seckill_info = $seckill_model->where(['id' => $seckill_dis_id])->where($_l_where)->find();
                            $seckill_goods_info = $seckill_goods_model->getOne(['where' => ['seckill_id' => $seckill_dis_id, 'commodity_id' => $v['commodity_id']]]);
                            if ($seckill_info && $seckill_goods_info) {
                                $seckill_info['sku_dis'] = $seckill_goods_info['sku_dis'];
                                // 判断秒杀类型
                                $screening = '';
                                if ($seckill_info['seckill_type'] == 2) {
                                    $time = date('H:i:s');
                                    if ($time > $seckill_info['day_end_time']) {
                                        // 明天场次
                                        $screening = date("Y-m-d", strtotime("+1 day"));
                                    } else {
                                        // 当天场次
                                        $screening = date('Y-m-d');
                                    }
                                }
                                //应该要读取对应的活动最低价了
                                $miaosha_stock = $this->kill_count($seckill_dis_id, $v['commodity_id'], $screening);

                                $list[$k]['count_stock'] = min($list[$k]['count_stock'], $miaosha_stock);
                                //秒杀前购买
                                $list[$k]['limited_stock'] = min($list[$k]['count_stock'], $miaosha_stock);
                                if (in_array($seckill_info['act_status'], [1, 2])) {
                                    $show_seckill_label_status = $seckill_info['act_status'];
                                    if ($v['is_grouped']) {
                                        if (in_array($seckill_info['discount_type'], [1, 3])) {
                                            $dis_info = $this->getSecKillDisByPrice($seckill_info, 0, $v['final_price'], $commodity_dis_info);
                                            $list[$k]['current_price'] = $dis_info['current_price'];
                                            $list[$k]['final_price'] = $dis_info['dis_price'];
                                            $list[$k]['commodity_dis_act_user_segment'] = $dis_info['user_segment'];
                                        }
                                        $show_seckill_label = 1;
                                    } else {
                                        if ($seckill_goods_info) {
                                            $min_price_sku_id_price = $gc_price_arr[0];
                                            $min_price_sku_id_old_price = $gc_price_arr[0];
                                            $min_price_sku_id_old_dis_price = $gc_price_arr[0];
                                            $have_seckill_discount = 0;

                                            foreach (array_unique($gc_id_arr) as $key => $item_sku_id) {
                                                $dis_info = $this->getSecKillDisByPrice($seckill_info, $item_sku_id, $gc_price_arr[$key], $commodity_dis_info, $segment_info);

                                                if ($min_price_sku_id_price > $dis_info['dis_price']) {
                                                    $min_price_sku_id_price = $dis_info['dis_price'];
                                                    $min_price_sku_id_old_price = $gc_price_arr[$key];
                                                    $min_price_sku_id_old_dis_price = $dis_info['current_price'];
                                                }
                                                $show_seckill_label = 1;
                                                $have_seckill_discount = 1;
                                                $list[$k]['commodity_dis_act_user_segment'] = $dis_info['user_segment'];
                                            }
                                            if ($have_seckill_discount) {
                                                $list[$k]['final_price'] = $min_price_sku_id_price;
                                                $list[$k]['price'] = $min_price_sku_id_old_price;
                                                $list[$k]['current_price'] = $min_price_sku_id_old_dis_price;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $_l_where = "";
                    if ($segment_info['is_logged_in']) {
                        $membership = $segment_info['membership_level'];
                        $owner = $segment_info['brand_owner_label'];
                        $_l_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
                    }
                    // 判断买赠
                    $show_gift_label = 0;
                    if ($v['gift_dis']  && $v['is_pp'] == 1) {
                        $gift_dis_arr = json_decode($v['gift_dis'], true);
                        if (isset($gift_dis_arr[$this->channel_type][0])) {
                            $gift_dis_id = $gift_dis_arr[$this->channel_type][0];
                            $gift_info = $gift_model->where(['id' => $gift_dis_id])->where($_l_where)->find();
                            if ($gift_info) {
                                $limit_goods_info = $gift_commodity->getOne(['where' => ['gift_id' => $gift_dis_id, 'commodity_id' => $v['commodity_id'], 'is_gift' => 0]]);
                                if ($limit_goods_info['sku_price'] !== '[]') {
                                    $show_gift_label = 1;
                                }
                            }
                        }
                    }

                    $show_n_dis_label = 0;
                    if ($v['n_dis']  && $v['is_pp'] == 1) {
                        $n_dis_arr = json_decode($v['n_dis'], true);
                        if (isset($n_dis_arr[$this->channel_type][0])) {
                            $n_dis_id = $n_dis_arr[$this->channel_type][0];

                            // 使用缓存获取N件折扣活动信息
                            $n_dis_info = $this->getActivityInfoWithCache('n_discount', $n_dis_id, $membership, $owner, $n_dis_model);
                            if ($n_dis_info) {
                                $show_n_dis_label = 1;
                            }
                        }
                    }

                    $show_full_dis_label = 0;
                    if ($v['full_dis']  && $v['is_pp'] == 1) {
                        $full_dis_arr = json_decode($v['full_dis'], true);
                        if (isset($full_dis_arr[$this->channel_type][0])) {
                            $full_dis_id = $full_dis_arr[$this->channel_type][0];

                            // 使用缓存获取满减活动信息
                            $full_dis_info = $this->getActivityInfoWithCache('full_discount', $full_dis_id, $membership, $owner, $full_dis_model);
                            if ($full_dis_info) {
                                $show_full_dis_label = 1;
                            }
                        }
                    }

                    $list[$k]['act_title'] = $act_title;
                    $tag_name = [];
                    if ($this->channel_type == 'GWNET') {
                        $tag_column = $v['tag_gwnet'];
                    } else if ($this->channel_type == 'GWAPP') {
                        $tag_column = $v['tag_gwapp'];
                    } else if ($this->channel_type == 'GWSM') {
                        $tag_column = $v['tag'];
                    } else if ($this->channel_type == 'PZ1AAPP') {
                        $tag_column = $v['tag_pz1aapp'];
                    } else if ($this->channel_type == 'PZ1ASM') {
                        $tag_column = $v['tag_pz1asm'];
                    } else if ($this->channel_type == 'QCSM') {
                        $tag_column = $v['tag_qcsm'];
                    } else if ($this->channel_type == 'QCAPP') {
                        $tag_column = $v['tag_qcapp'];
                    }
                    $tag_zdy = [];
                    $tag_ac_info = [];
                    //测试说，即使商品上架没有配置标签，也要显示活动的标签名
                    if(isset($limit_info)){
                        $tag_ac_info[12] = $limit_info['tag']?$limit_info['tag']:DbCommodityFlat::tagArr(12);
                    }
                    if(isset($group_dis_arr[$this->channel_type][0])){
                        $group_model =  new DbFightGroup();
                        $group_f_info =  $group_model->getOneByPk($group_dis_arr[$this->channel_type][0]);
                        $tag_ac_info[13] = $group_f_info['tag']?$group_f_info['tag']:DbCommodityFlat::tagArr(13);
                    }
                    if(isset($full_dis_info)){
                        $tag_ac_info[11] = $full_dis_info['tag']?$full_dis_info['tag']:DbCommodityFlat::tagArr(11);
                    }
                    if(isset($n_dis_info)){
                        $tag_ac_info[14] = $n_dis_info['tag']?$n_dis_info['tag']:DbCommodityFlat::tagArr(14);
                    }
                    if(isset($pre_info)){
                        $tag_ac_info[15] = $pre_info['tag']?$pre_info['tag']:DbCommodityFlat::tagArr(15);
                    }
                    if(isset($seckill_info)){
                        $tag_ac_info[16] = $seckill_info['tag']?$seckill_info['tag']:DbCommodityFlat::tagArr(16);
                    }
                    if(isset($gift_info)){
                        $tag_ac_info[17] = $gift_info['tag']?$gift_info['tag']:DbCommodityFlat::tagArr(17);
                    }
                    if($v['cheap_dis']){
                        $cheap_dis_arr = json_decode($v['cheap_dis'], true);
                        if (isset($cheap_dis_arr[$this->channel_type][0])) {
                            $cheap_model =  new BuCheapSuitIndex();
                            $cheap_info = $cheap_model->getOneByPk($cheap_dis_arr[$this->channel_type][0]);
                            $tag_ac_info[10] = $cheap_info['tag']?$cheap_info['tag']:DbCommodityFlat::tagArr(10);
                        }
                    }
                    if($v['tag_zdy']){
                        //1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减9臻享服务包10秒杀 19-20 卡券21赠品券22商品属性
                        $tag_zdy = json_decode($v['tag_zdy'],1);
                        if($tag_zdy && $tag_zdy['act_type']==1){
                            if((!isset($limit_dis_arr[$this->channel_type][0]) || (isset($limit_dis_arr[$this->channel_type][0]) && $limit_dis_arr[$this->channel_type][0] !=$tag_zdy['act_id']))){
                                $tag_zdy = [];
                            }else{
                                $tag_zdy['tag_name'] = $tag_ac_info[12]??'';
                            }
                        }
                        if($tag_zdy && $tag_zdy['act_type']==2){
                            if((!isset($group_dis_arr[$this->channel_type][0]) || (isset($group_dis_arr[$this->channel_type][0]) && $group_dis_arr[$this->channel_type][0] !=$tag_zdy['act_id']))){
                                $tag_zdy = [];
                            }else{
                                $tag_zdy['tag_name'] = $tag_ac_info[13]??'';
                            }
                        }
                        if($tag_zdy && $tag_zdy['act_type']==3){
                            if((!isset($full_dis_arr[$this->channel_type][0]) || (isset($full_dis_arr[$this->channel_type][0]) && $full_dis_arr[$this->channel_type][0] !=$tag_zdy['act_id']))){
                                $tag_zdy = [];

                            }else{
                                $tag_zdy['tag_name'] = $tag_ac_info[11]??'';
                            }
                        }

                        if($tag_zdy && $tag_zdy['act_type']==5 ){
                            if(!$v['cheap_dis']){
                                $tag_zdy = [];
                            }else{
                                $cheap_dis_arr = json_decode($v['cheap_dis'], true);
                                if (isset($cheap_dis_arr[$this->channel_type][0])) {
                                    $cheap_dis_id = $cheap_dis_arr[$this->channel_type][0];
                                    if($cheap_dis_id!=$tag_zdy['act_id']){
                                        $tag_zdy = [];
                                    }else{
                                        $tag_zdy['tag_name'] = $tag_ac_info[10]??'';
                                    }
                                }else{
                                    $tag_zdy = [];
                                }
                            }
                        }
                        if($tag_zdy && $tag_zdy['act_type']==6 ){
                            if((!isset($n_dis_arr[$this->channel_type][0]) || (isset($n_dis_arr[$this->channel_type][0]) && $n_dis_arr[$this->channel_type][0] !=$tag_zdy['act_id']))){
                                $tag_zdy = [];

                            }else{
//                                $tag_zdy['tag_name'] =$n_dis_info['tag']?$n_dis_info['tag']:DbCommodityFlat::tagArr(14);
                                $tag_zdy['tag_name'] =$tag_ac_info[14]??'';
                            }
                        }
                        if($tag_zdy && $tag_zdy['act_type']==7){
                            if((!isset($pre_arr[$this->channel_type][0]) || (isset($pre_arr[$this->channel_type][0]) && $pre_arr[$this->channel_type][0] !=$tag_zdy['act_id']))){
                                $tag_zdy = [];

                            }else{
//                                $tag_zdy['tag_name'] =$pre_info['tag']?$pre_info['tag']:DbCommodityFlat::tagArr(15);
                                $tag_zdy['tag_name'] =$tag_ac_info[15]??'';
                            }
                        }
                        if($tag_zdy && $tag_zdy['act_type']==10){
                            if((!isset($seckill_dis_arr[$this->channel_type][0]) || (isset($seckill_dis_arr[$this->channel_type][0]) && $seckill_dis_arr[$this->channel_type][0] !=$tag_zdy['act_id']))){
                                $tag_zdy = [];

                            }else{
//                                $tag_zdy['tag_name'] =$seckill_info['tag']?$seckill_info['tag']:DbCommodityFlat::tagArr(16);
                                $tag_zdy['tag_name'] =$tag_ac_info[16]??'';
                            }
                        }
                        if($tag_zdy && $tag_zdy['act_type']==12){
                            if((!isset($gift_dis_arr[$this->channel_type][0]) || (isset($gift_dis_arr[$this->channel_type][0]) && $gift_dis_arr[$this->channel_type][0] !=$tag_zdy['act_id']))){
                                $tag_zdy = [];

                            }else{
//                                $tag_zdy['tag_name'] =$gift_info['tag']?$gift_info['tag']:DbCommodityFlat::tagArr(17);
                                $tag_zdy['tag_name'] =$tag_ac_info[17]??'';
                            }
                        }
                    }

//                    print_json($goods_un_card_ids);
                    $card_tag_arr=[];
                    if(isset($goods_un_card_ids) && $goods_un_card_ids){
                        //卡券部分，不上线

//                        $all_card_ids = $this->cardCanTag($goods_un_card_arr,$user,$this->channel_type);
                        $all_card_tag = $this->sort_card_list($goods_un_card_ids);
//                        $tag_card_arr = [];
//                        print_json($all_card_tag);
                        if($all_card_tag){
                            //19卡券1，20卡券2
                            foreach($all_card_tag as $all_k=>$all_v){
                                $kk_key = bcadd(19,$all_k);
                                $tag_name[$kk_key] = $all_v['tag_desc'];
                                $card_tag_arr[$all_v['id']] = $all_v['tag_desc'];
                                if($tag_column){
                                    $tag_column.=','.$kk_key;
                                }else{
                                    $tag_column.=$kk_key;
                                }
//                                $tag_column.=$kk_key.',';
                            }
                        }
                        $tag_column =  trim($tag_column,',');
                    }
                    if(isset($tag_zdy['act_type']) && in_array($tag_zdy['act_type'],[19,20])){
                        if(!$card_tag_arr){
                            $tag_zdy = [];
                        }else{
                            $tag_zdy['tag_name'] =current($card_tag_arr);
                        }

                    }
                    if(($gift_card_sku_arr || $gift_card_sku_class_arr) && !$v['is_grouped']){
                        if(array_intersect($gift_card_sku_arr,$d_sku_code_arr) || array_intersect($gift_card_sku_class_arr,$d_v_code_arr)){
                            if($tag_column){
                                $tag_column.=',21';
                            }else{
                                $tag_column.='21';
                            }
                        }
                    }
                    if ($tag_column  && $v['is_pp'] == 1) {

                        $tags = explode(',', $tag_column);
                        foreach ($tags as $kk => $vv) {
                            if($vv==16 && $show_seckill_label_status==1){
                                $vv=161;
                            }
                            if(isset($tag_ac_info[$vv])){
                                $tag_info = $tag_ac_info[$vv];
                            }else{
                                $tag_info = $flat::tagArr($vv);
                            }

                            if (empty($tag_info) || empty($vv)) {
                                continue;
                            }
                            if ($vv == 11 && $show_full_dis_label == 0) {
                                continue;
                            }
                            if ($vv == 14 && $show_n_dis_label == 0) {
                                continue;
                            }
                            if ($vv == 12 && $show_limit_label == 0 && $segment_info['is_logged_in'] == 1) {
                                continue;
                            }
                            if ($vv == 16 && $show_seckill_label == 0 && $segment_info['is_logged_in'] == 1) {
                                continue;
                            }
                            //有主品买赠券不显示买赠
                            if ($vv == 17 && ($show_gift_label == 0 || in_array("21",$tags))) {
                                continue;
                            }
                            $tag_name[$vv] = $tag_info;
                        }
                        $list[$k]['tag_column']=$tag_column;

                        // 23-06UI改版 tag_name排序
//                        【秒杀活动（进行中）】>【限时优惠】>【券标签（多个券标签按结束时间倒序排序）】>【买赠】>【满优惠】>【N件N折】>【多人拼团】>【优惠套装】>【预售活动】>【秒杀活动（未开始）】>【商品属性标签】
                        //赠品券》普通券  lr
                        //自定义表标签
                        $sort_arr = [16, 12,21,19,20,17, 11, 14, 13, 10,15,161];
                        if($tag_zdy){
                            $tag_zdy_arr = $flat::actToTag($tag_zdy['act_type']);
//                            $sort_arr = array_intersect($tag_zdy_arr,$sort_arr);
                            if($tag_zdy['act_type']==22){
                                $sort_arr = array_intersect($tag_zdy_arr,$sort_arr);
                            }else{
                                $sort_arr = [$tag_zdy_arr];
                                $tag_name[$tag_zdy_arr] = $tag_zdy['tag_name']??'';

//                                try{
//                                }catch (Exception $e){
//                                    print_json($tag_zdy);
//
//                                }
                            }
                        }

                        $tag_keys = array_keys($tag_name);
                        foreach ($sort_arr as $sort_key => $sort) {
                            if (!in_array($sort, $tag_keys)) {
                                unset($sort_arr[$sort_key]);
                            }
                        }
//                        print_json($tag_name);
                        $tag_name = array_unique(array_replace(array_flip($sort_arr), $tag_name));
                    }
                    $tag_name =  array_values($tag_name);

                    if (!empty($list[$k]['is_have_car']) && $commodity_dis_info && !$can_not_with_promotion && $v['is_pp']==1) {
                        $list[$k]['commodity_dis_user_segment'] = $commodity_dis_info['user_segment'];
                        $list[$k]['commodity_dis_label'] = get_user_segment_label($commodity_dis_info['user_segment']);
                        $list[$k]['commodity_dis_label_cn'] = get_user_segment_label_cn($list[$k]['commodity_dis_label']);

                        if (empty($show_limit_label) && empty($show_seckill_label)) {
                            $v['final_price'] = $this->getCommodityDisFinalPrice($commodity_dis_info, $list[$k]['final_price']);
                        }
                    }
                    if (!empty($list[$k]['commodity_dis_act_user_segment']) && $v['is_pp']==1) {
                        $list[$k]['commodity_dis_label'] = get_user_segment_label($list[$k]['commodity_dis_act_user_segment']);
                        $list[$k]['commodity_dis_label_cn'] = get_user_segment_label_cn($list[$k]['commodity_dis_label']);
                    }
                    //fix:价格修改为四舍五入到角然后补充2位0

                    if (in_array($v['dd_commodity_type'], [1, 3, 4, 41]) && $v['maintain_q']) {
                        $list[$k]['price'] = sprintf("%.2f", bcdiv($v['price'], $v['maintain_q'] / 10, 0));
                    }
                    $list[$k]['tag_name'] = $tag_name;
                    $list[$k]['price'] = formatNumber($v['price']);
                    if($v['final_price']<0){
                        $v['final_price']=0;
                    }
                    $list[$k]['final_price'] = formatNumber($v['final_price']);
                    $list[$k]['current_price'] = formatNumber($v['current_price']);
                    $list[$k]['max_price'] = $v['price'];
                    $list[$k]['max_final_price'] = $v['final_price'];
                    if ($v['max_point']) {
                        $one_m_f_p = sprintf("%.2f", ($v['final_price'] - $v['max_point'] / 10));
                        $one_m_p = sprintf("%.2f", ($v['price'] - $v['max_point'] / 10));
                        $list[$k]['max_price'] = $one_m_p < 0 ? 0 : $one_m_p;
                        $list[$k]['max_final_price'] = $one_m_f_p < 0 ? 0 : $one_m_f_p;
                    }
                    //                unset($list[$k]['gc_id']);
                    //                unset($list[$k]['gc_price']);
                    unset($list[$k]['relate_car_ids']);
                    unset($list[$k]['d_sku_code']);
//                    unset($list[$k]['gc_price']);
                    unset($list[$k]['ss_plist']);
                    unset($list[$k]['group_commodity_ids_info']);
                    unset($list[$k]['gc_old_price']);
//                    unset($list[$k]['gc_id']);//这两个不能去 别人有调用
                    unset($list[$k]['gc_price']);
//                    unset($list[$k]['gc_stock']);//这两个不能去 别人有调用
                    unset($list[$k]['gc_g_goods_set_id']);
//                    unset($list[$k]['gc_g_card_id']);
//                    unset($list[$k]['old_gc']);
//                    unset($list[$k]['gc_stock']);
//                    unset($list[$k]['card_id']);
                    if ($price_s) {
                        if ($list[$k]['final_price'] < $price_s) {
                            //                        dd(2);
                            unset($list[$k]);
                        }
                    }
                    if ($price_e && isset($list[$k])) {
                        if ($list[$k]['final_price'] > $price_e) {
                            unset($list[$k]);
                        }
                    }

                    // 判断是否是取送车服务包
                    $qsc_group = array_filter(explode(',', $v['qsc_group']));
                    if (!empty($qsc_group)) {
                        $list[$k]['commodity_dis_user_segment'] = 0;
                        $list[$k]['commodity_name'] = $v['qsc_group_name'];
                        $map = ['commodity_set_id' => ['in', $qsc_group], 'is_enable' => 1];
                        $price_arr = $set_sku_model->where($map)->column('price');
                        // 电子卡券售价的和*数量+取送车券售价
                        $final_price = bcadd(bcmul(array_sum($price_arr), $v['qsc_group_num'], 2), $v['b_act_price'], 2);
                        $list[$k]['price'] = $v['qsc_group_price'];
                        $list[$k]['final_price'] = $final_price;  // 最终售价
                        $list[$k]['max_price'] = $v['qsc_group_price'];
                        $list[$k]['max_final_price'] = $final_price;
                    }

                }
                //0423版本-必选商品无库存过滤
                foreach ($list as $kk => $vv) {
                    if (in_array($vv['commodity_id'], $g_must_delete)) {
                        unset($list[$kk]);
                    }

                }
                $all_list = $list->toArray();
                $can_get_card_list = [];
                //getcardlist已经过滤了 领取点位了
                if($get_card_list){
                    foreach ($get_card_list as $a_k=> $a_v){
//                        if($a_v['is_can_receive'] && $a_v['available_quantity']>0 && $a_v['available_count']>0 && in_array($a_v['card_id'],$pp_card_ids)){
//                            $can_get_card_list[]=$a_v;
//                        }

                        if(!in_array($a_v['card_id'],$not_pp_card_ids) || !$not_pp_card_ids){
                            $can_get_card_list[]=$a_v;
                        }
                    }
                }

                $all_list['data'] = array_values($all_list['data']);

                $all_list['user_info'] = $user_info;
                $all_list['get_card_list'] = $can_get_card_list;
                $all_list['goods_card_rule'] = $goods_card_rule;
                redis($user_redis_name, $all_list, 15);
            }


        }

        $all_list['search_tip'] = $search_tip;
        $all_list['user_vin'] = $user_vin;
        $all_list['xy_can_buy'] = $xy_can_buy;
        $all_list['wn_can_buy'] = $wn_can_buy;
        $all_list['dlr_code'] = $dlr_code;
        $all_list['dlr_name'] = $dlr_name;
        $all_list['maintain_times'] = $maintain_times;


        //        dd($all_list['data']);
        Logger::error($api_start_at . '-goodslistuntime', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type);
        return $this->re_msg($all_list);
        //        return $this->re_msg($list);
    }

    public function goodsMailPrice($requestData, $channel_type)
    {
        $goods_id = $requestData['goods_id'];
        $pros = $requestData['address'];
        $res = $this->_mail_price_by_goods($goods_id, $pros, 'mail_ajax', $channel_type);
        if ($res['error'] == 1) {
            return $this->re_msg($res['msg'], 415);
        } else {
            return $this->re_msg($res['data']);
        }
    }

    /**
     * 获取组合商品最低价
     */
    public function getGroupCommodityPrice($commodity_id, $car_18n)
    {
        $group_where = ['commodity_id' => $commodity_id, 'is_enable' => 1];
        $group_field = 'min(price) ff_price';
        if ($car_18n) {
            $group_where[] = ['exp', " (find_in_set('{$car_18n}',relate_car_18n) || relate_car_ids='') "];
        }

        $data = ['sum_min_money' => 0];
        $group_list = $this->set_sku_model->getList(['where' => $group_where, 'field' => $group_field, 'group' => 'group_sub_commodity_id', 'db-read' => true]);
        if ($group_list) {
            $ff_price = array_sum(array_column($group_list, 'ff_price'));
            $data['sum_min_money'] = round($ff_price, 2);
        }
        return $data;
    }
    //$from==1 默认，9 不走买赠信息
    public function detail($requestData, $user, $channel_type, $brand = 1,$from=1)
    {

        $api_start_at = microtime(true);
        if (isset($requestData['car_id']) && $requestData['car_id']) {
            $user['car_series_id'] = $requestData['car_id'];
        }
        $this->user = $user;
        $this->brand = $brand;
        $this->channel_type = $channel_type;
        $this->user_id = $user['id'];
        $this->unionid = $user['bind_unionid'];
        $id = $requestData['goods_id'];
        $group_id = $requestData['group_id'] ?? ''; //团购ID t_db_fight_group主键
        $group_order_code = $requestData['group_order_code'] ?? ''; //团购订单编号从别人分享的团进来才有
        $kilometer = $requestData['kilometer'] ?? ''; //公里数
        $lng = $requestData['lng'] ?? ''; //经度
        $lat = $requestData['lat'] ?? ''; //纬度
        $channel_type = $this->channel_type; //渠道来源 1官网 2小程序
        $dbdlrObj = new DbDlr();
        $shelves = $dbdlrObj->channel_to_shelves($channel_type);
        $r_set_sku_id = $requestData['r_set_sku_id'] ?? 0; //纬度

        $dlr_code = $requestData['dd_dlr_code'] ?? '';

        $get_dd_commodity_type = $requestData['get_dd_commodity_type'] ?? '';
        if ($get_dd_commodity_type == 1) {
            $dd_commodity = $this->com_model->getOneByPk($id);
            return $this->re_msg(['dd_commodity_type' => $dd_commodity['dd_commodity_type']]);
        }
        //获取上架信息再说
        $set_model =  new DbCommoditySet();
        $goods_where = ['is_enable'=>1,'commodity_id'=>$id];
        $goods_where[] = ['exp', " (find_in_set('{$this->channel_type}',up_down_channel_dlr)) "];
        $goods_set =  $set_model->getOne(['where'=>$goods_where]);
        if(!$goods_set){
            return $this->re_msg('商品已下架', 403);

        }
        if($goods_set['dlr_groups']){
            $this->user['group_id'] =  explode(',',$goods_set['dlr_groups']);
        }
        $nev_where =  $this->user['nev_where'];
        $dlr_where = $nev_where;
        $dlr_where['dlr_code'] = $dlr_code;

        $dlr_model = new DbDlr();
        $dlr = $dlr_model->getOne(['where' => $dlr_where]);
        if (!$dlr) {
            $dlr_code = '';
        } else {
            $dlr_name = $dlr['dlr_name'];
            $requestData['dlr_name'] = $dlr_name;
        }

        $f_b_info = $this->getFriendBaseInfo($this->user, $lng, $lat);

        if (!$dlr_code) {
            $dlr_code = $f_b_info['dlr_code'];
            $dlr_name = $f_b_info['dlr_name'];
            if (!$dlr_code) {
                $dlr_code = '';
            }
        }

        $is_gift = $requestData['is_gift'] ?? '';
        $gift_id = $requestData['gift_id'] ?? '';
        $source_special = $requestData['source_special'] ?? '';
        $gonghui_model = new AcGongHuiInfo();
        $gong_hui =  $gonghui_model->getOneByIds($id,$source_special);
        if(in_array($gong_hui,[1,2]) && $gong_hui!=3){
            return $this->re_msg('专题参数异常', 403);
        }

        $requestData['dlr_code'] =  $dlr_code;
        $goods = $this->getCommodityInfo($id, $channel_type, 0, 0, $group_id, 3, 0, $this->user, $dlr_code, 0, $kilometer, $is_gift, $gift_id,1,2,1,$r_set_sku_id);
//        print_json($id);
        $time = date('Y-m-d H:i:s');
        //        var_dump($goods);die();
        Logger::error('goodsdetailruntime0', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type . '-' . $id . '=' . http_build_query($requestData));

        if (!$goods) {
            return $this->re_msg('商品已下架', 403);
        } else {
            $goods_data = $goods['commodity_data'];
            $card_list = $goods['card_list'];
            $can_get_card_list = $goods['can_get_card_list'];
            $card_rules = $goods['card_rules'];
            $sku_list = $goods['sku_list'];
            $sku_ky_cards = [];

            $not_use_card_id_arr=[];
//            print_json($card_list);
            //4.判断是否有限时折扣
            $limit_info = [];
            $limit_wi_info = [];
            $limit = [];
            $can_number = 999; //之前是999
            $wi_can_number = 999; // 之前是999
            if (isset($goods_data['limit_dis_id']) && $goods_data['limit_dis_id'] > 0) {
                $limit = $this->_limit($goods_data['commodity_id'], $goods_data['limit_dis_id'], $this->user_id, $this->channel_type, 0, 0, 1, '', 0);
                $can_number = $limit['can_no'];
                $limit_info = $limit['limit_info'];
                if ($limit_info) {
                    $limit_info['stop_time'] = $limit['stop_time'];
                    if (!$limit_info['des']) {
                        $limit_info['des'] = "暂无描述";
                    }
                    $limit_info['description']  = $limit_info['des'];
                    $limit_info['commodity_dis_act_user_segment'] = $limit_info['user_segment'];
                    $limit_info['commodity_dis_label'] = get_user_segment_label($limit_info['user_segment']);
                    $limit_info['commodity_dis_label_cn'] = get_user_segment_label_cn($limit_info['commodity_dis_label']);
                }
            }
            if (isset($goods_data['limit_wi_dis_id']) && $goods_data['limit_wi_dis_id'] > 0) {
                $limit_wi = $this->_limit_wi($goods_data['commodity_id'], $goods_data['limit_wi_dis_id']);
                $wi_can_number = $limit_wi['can_no'];
                $limit_wi_info = $limit_wi['limit_info'];
                if ($limit_wi_info) {
                    $limit_wi_sku_dis = json_decode($limit_wi_info['sku_dis'], true);
                    if (!empty($limit_wi_sku_dis)) {
                        $limit_wi_info['discount'] = current($limit_wi_sku_dis);
                    }
                    $limit_wi_info['stop_time'] = $limit_wi['stop_time'];
                    if (!$limit_wi_info['des']) {
                        $limit_wi_info['des'] = "暂无描述";
                    }
                }
            }

            // 秒杀
            $seckill_info = [];
            if (isset($goods_data['seckill_dis_id']) && $goods_data['seckill_dis_id'] > 0) {

                $seckill = $this->_seckill($goods_data['commodity_id'], $goods_data['seckill_dis_id'], $this->user_id, $this->channel_type, 0, 0, 1, '', 0);

                $can_number = $seckill['can_no'];
                $seckill_info = $seckill['seckill_info'];
                if ($seckill_info) {
                    $service = new Seckill();
                    $re = $service->getSeckillTime($goods_data['seckill_dis_id']);
                    $seckill_info['next_start_time'] = $re['next_start_time'];
                    $seckill_info['next_end_time'] = $re['next_end_time'];
                    if (in_array($seckill_info['act_status'], [1, 2])) {
                        $seckill_info['miaosha_stock'] = $seckill['kill_count'];
                    }
                    $seckill_info['stop_time'] = $seckill['stop_time'];
                    if (!$seckill_info['des']) {
                        $seckill_info['des'] = "暂无描述";
                    }
                    $seckill_info['description'] = $seckill_info['des'];

                    $seckill_info['commodity_dis_act_user_segment'] = $seckill_info['user_segment'];
                    $seckill_info['commodity_dis_label'] = get_user_segment_label($seckill_info['user_segment']);
                    $seckill_info['commodity_dis_label_cn'] = get_user_segment_label_cn($seckill_info['commodity_dis_label']);
                }
            }


            //判断是否有买赠
            $gift_info = [];
            if (empty($gift_id) && $from==1) {
                if (isset($goods_data['gift_id']) && $goods_data['gift_id'] > 0) {
                    $gift = $this->_gift($goods_data['commodity_id'], $goods_data['gift_id'], $this->user_id);
//                    $can_number = $gift['can_no'];
                    $gift_info = $gift['gift_info'];
                    if (!empty($gift_info['gift_imgs_arr'])) {
                        $gift_array = $gift_info['gift_imgs_arr'];
                        $new_gift_array = [];
                        foreach ($gift_array as $key => $value) {
                            $new_gift_array[$key]['cover_image'] = $value['cover_image'];
                            $is_choose = $this->getCommodityInfo($value['commodity_id'], $channel_type, 0, 0, $group_id, 3, 0, $this->user, $dlr_code, '', '', '', '',1,1,1,0,'',[],0,0);
                            $new_gift_array[$key]['is_choose'] = isset($is_choose['commodity_data']) ? $is_choose['commodity_data']['is_mate'] : 0;
                        }
                        $gift_key = array_column($new_gift_array, 'is_choose');
                        array_multisort($gift_key, SORT_DESC, $new_gift_array);
                        $gift_info['gift_imgs_arr'] = $new_gift_array;
                        unset($gift_info['sku_price']);
                    }
                    if ($gift_info) {
                        $gift_info['commodity_dis_act_user_segment'] = $gift_info['user_segment'];
                        $gift_info['commodity_dis_label'] = get_user_segment_label($gift_info['user_segment']);
                        $gift_info['commodity_dis_label_cn'] = get_user_segment_label_cn($gift_info['commodity_dis_label']);
                    }

                }
            }

            //////////////////组合商品开始//////////////////////
            $e3sParePartTimeObj = new E3sParePartTime();
            $work_model = new E3sPartCarSeries();
            $dbCommodityObj = new DbCommodity();
            //1为组合商品，0为非组合商品
            $is_grouped_flag = $goods['commodity_data']['is_grouped'];
            $groups_data = [];

            $is_sp_associated = $goods['commodity_data']['is_sp_associated'] ?? 0;
            $sp_associated_data = [];
//            dd($goods_data['is_mate']);
            //规格匹配，但是如果没车型，那就是2
            if ($goods_data['is_mate'] == 1 && !$this->user['car_series_id'] && $goods_data['relate_car']) {
                $goods_data['is_mate'] = 2;
            }
            Logger::error('goodsdetailruntime1', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type . '-' . $id);
            $h_card_list = [];
            $commoditySet = new DbCommoditySet();
            if ($is_grouped_flag == 1) {
                $dbCommoditySetSku = new DbCommoditySetSku();
                $dbSpecValueObj = new DbSpecValue();
                $commodity_set_info = $commoditySet->where(['id' => $goods_data['commodity_set_id'], 'is_enable' => 1])->find();
                //                print_json($commoditySet->getLastSql());
                //                echo $commodity_set_info['group_commodity_ids_info'];die();
                $commodity_set_list = json_decode($commodity_set_info['group_commodity_ids_info'], true);
                $sub_commodity_list_arr = $this->set_sku_model->getSubCommidtySortList($commodity_set_info['id'], $this->user['car_series_id'], $f_b_info['car_offline_date']);

//                echo $this->set_sku_model->getLastSql();die();
//                print_json($sub_commodity_list_arr);

//                $sub_sp_v_list_arr=[];
//                if($sub_commodity_list_arr){
//                    $re_part_list = array_column($sub_commodity_list_arr,'sku_code');
//
//                    foreach($sub_commodity_list_arr as $sub_kk=>$sub_vv){

                //如果有被替换件则过滤

//                        if ($sub_vv['re_sku_code']) {
//                            unset($sub_commodity_list_arr[$sub_kk]);
//                        } else {
//                            //只过滤有备件号的
//                            if (!empty($sub_vv['sku_code'])) {
//                                $sub_sp_v_list_arr[$sub_vv['sp_value_list'] . $sub_vv['sku_commodity_id']][] = $sub_kk;
//                            }
//                        }
//                    }
                //同规格被同sp太多则过滤-备件时间模块
//                    foreach ($sub_sp_v_list_arr as $sp_kkk=> $sp_vvv){
//                        if(count($sp_vvv) > 1){
//                            //记录表并且过滤对应的ID
//                            $this->error_vin_goods(isset($goods['user_info']['vin'])?$goods['user_info']['vin']:$user['id'],['sp'=>$sp_kkk,'goods_id'=>$id],'d_g');
//                            foreach ($sp_vvv as $sp_vvv_val){
//                                unset ($sub_commodity_list_arr[$sp_vvv_val]);
//                            }
//                        }
//                    }
                //同规格被同sp太多则过滤-备件时间模块end

//                }
                $g_sub_sku_id_all = [];
                if (!$sub_commodity_list_arr) {
                    if (!$this->user['car_series_id']) {
                        $goods_data['is_mate'] = 2;
                    } else {
                        $goods_data['is_mate'] = 0;
                    }
                }else{
                    foreach($sub_commodity_list_arr as $sub_v){
                        $g_sub_sku_id_all[$sub_v['group_sub_commodity_id']][] = $sub_v['set_sku_id'];
                    }
                }
                //                echo $this->set_sku_model->getLastSql();die();
                $sku_cb_list_tmp = [];
                $sku_cb_list = [];
                $sub_com_min_price = 0;
                //                dd($commodity_set_info['group_commodity_ids_info']);
                $com_set_sub_c_id = [];
                $com_list_sub_c_id = [];
                $sub_commodity_list_map = [];
//                print_json($commodity_set_list);
                $sub_list_goods_id = array_column($sub_commodity_list_arr, 'group_sub_commodity_id');
                //改成下面判断库存》0才去计算价格
//                foreach ($commodity_set_list as $cc_v) {
////                    $sub_com_min_price += min(array_column($cc_v['sku_list'], 'price'));
//                }
                Logger::error('subgoods:1', ['gc' => count($sub_commodity_list_arr), 'sql' => $this->set_sku_model->getLastSql()]);
                //0423版本
                $group_sub_count_stock = [];
                $group_must_sub_count_stock = [];
                $group_sub_rel_stock = [];
                foreach ($commodity_set_list as $cc_k=> $cc_v) {
                    $sub_commodity_list_map[$cc_v['commodity_id']] = $cc_v;
                    //0423版本
                    //统计子商品的总库存
                    //增加一个判断子商品上架，不然就库存==0
                    $sub_set_info =$commoditySet ->getUpCommodity($cc_v['commodity_id'],$channel_type);
                    $sum_stock=0;
                    if($sub_set_info){
                        $sum_stock = $dbCommoditySetSku->getSumStock($cc_v);
                    }else{
//                        unset($commodity_set_list[$cc_k]);
//                        continue;
                    }
                    $group_sub_rel_stock[$cc_v['commodity_id']] = $sum_stock;
                    if ($cc_v['can_select'] == 1) {
                        if ($sum_stock > 0) {
                            $group_sub_count_stock[$cc_v['commodity_id']] = $sum_stock;
                            $sub_com_min_price += min(array_column($cc_v['sku_list'], 'price'));
                        } else {
                            //pz1a有子商品无库存就下架
                            if (in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                                $sub_commodity_list_arr = []; //有子商品无库存的，整个商品下架
                                break;
                            }
                        }
                    } else {
                        $count_sum = $sum_stock;
                        $group_must_sub_count_stock[$cc_v['commodity_id']] = $count_sum;
                        if ($count_sum == 0) {
//                            Logger::error('subgoods:2-stc',['gc'=>$cc_v,'sql'=>$dbCommoditySetSku->getLastSql()]);
                            $goods_data['count_stock'] = 0;
                            $sub_commodity_list_arr = []; //有子商品无库存的，整个商品下架
                            break;
                        } else {
                            $sub_com_min_price += min(array_column($cc_v['sku_list'], 'price'));
                        }
                    }

                    //有商品不匹配
                    if ($cc_v['machine_oil_type'] <> 1 and !in_array($cc_v['commodity_id'], $sub_list_goods_id) and $cc_v['can_select'] == 0) {
//                        Logger::error('subgoods:2-stcnotpp',['gc'=>$cc_v,'sgid'=>$sub_list_goods_id]);
                        $sub_commodity_list_arr = []; //有商品不匹配，整个商品下架
                        break;
                    }
                }
                //PZ1A还是要计算所有的--上方日产启辰的可能价格也会异常
                if (in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                    $sub_com_min_price = 0;
                    foreach ($commodity_set_list as $cc_v) {
                        $sub_com_min_price += min(array_column($cc_v['sku_list'], 'price'));
                    }
                }
                if ($goods_data['is_mate'] == 2) {
                    $sub_commodity_list_arr = [];
                }

                Logger::error('subgoods:2', ['gc' => count($sub_commodity_list_arr)]);

                if ($is_sp_associated) {
                    $filter_result = $this->commodityAssociateFilter($commodity_set_info['id'], $sub_commodity_list_arr, true);
                    $sub_commodity_list_arr = $filter_result['sub_commodity_list_arr'];
                    foreach ($filter_result['sp_associated_data'] as $assoc_item) {
                        $assoc_item_tmp = [];
                        // 添加正向联动
                        $assoc_item_tmp['sub_commodity_id'] = $assoc_item['sub_commodity_id'];
                        $assoc_item_tmp['sp_id'] = $assoc_item['sp_id'];
                        $assoc_item_tmp['sp_value_id'] = $assoc_item['sp_value_id'];
                        $assoc_item_tmp['assoc_sub_commodity_id'] = $assoc_item['assoc_sub_commodity_id'];
                        $assoc_item_tmp['assoc_sp_id'] = $assoc_item['assoc_sp_id'];
                        $assoc_item_tmp['assoc_sp_value_id'] = $assoc_item['assoc_sp_value_id'];
                        $assoc_item_tmp['assoc_sub_commodity_name'] = $sub_commodity_list_map[$assoc_item['assoc_sub_commodity_id']]['commodity_name'] ?? '';

                        $sp_associated_data[] = $assoc_item_tmp;
                        // 追加反向联动
                        $assoc_item_tmp['sub_commodity_id'] = $assoc_item['assoc_sub_commodity_id'];
                        $assoc_item_tmp['sp_id'] = $assoc_item['assoc_sp_id'];
                        $assoc_item_tmp['sp_value_id'] = $assoc_item['assoc_sp_value_id'];
                        $assoc_item_tmp['assoc_sub_commodity_id'] = $assoc_item['sub_commodity_id'];
                        $assoc_item_tmp['assoc_sp_id'] = $assoc_item['sp_id'];
                        $assoc_item_tmp['assoc_sp_value_id'] = $assoc_item['sp_value_id'];
                        $assoc_item_tmp['assoc_sub_commodity_name'] = $sub_commodity_list_map[$assoc_item['sub_commodity_id']]['commodity_name'] ?? '';
                        $sp_associated_data[] = $assoc_item_tmp;
                    }
                }

                $sub_commodity_list = [];
                $sub_commodity_id_arr = [];
                $sku_jj=[];

                if ($sub_commodity_list_arr) {
                    $sub_commodity_sort_arr = [];
                    $sub_commodity_continue_arr = [];
//                    print_json($sub_commodity_list_arr);
                    foreach ($sub_commodity_list_arr as $arr_v) {
                        $can_sub_list = 1;
                        if ($arr_v['relate_car_ids']) {
                            if (!$this->user['car_series_id']) {
                                $goods_data['is_mate'] = 2;
                                $sub_commodity_list_arr = [];
                                $can_sub_list=0;
//                                echo 2222;
//                                continue;
//                                break;//foreach 没有break 这个
                            }
                        }
                        $com_list_sub_c_id[] = $arr_v['group_sub_commodity_id'];
                        // echo $arr_v['group_sub_commodity_id']."====".$arr_v['group_sub_set_sku_id']."<br/>";
                        if (in_array($arr_v['group_sub_commodity_id'], $sub_commodity_continue_arr) && $sub_commodity_list[$arr_v['group_sub_commodity_id']]['price'] < $arr_v['price']) {
                            continue;
                        }
                       if($can_sub_list==1){
                           $sub_commodity_list[$arr_v['group_sub_commodity_id']] = $arr_v;
                           if ($arr_v['stock'] > 0) {
                               $sub_commodity_continue_arr[] = $arr_v['group_sub_commodity_id'];
                           }
                       }
                    }
                    if(!$sub_commodity_list_arr){
                        $sub_commodity_list=[];
                    }
//                    print_json($sub_commodity_list);
                    $sub_com_sku=[];
                    foreach ($sub_commodity_list as $sub_k=>$sub_v){
                        //有联动的在联动里处理
                        if(isset($g_sub_sku_id_all[$sub_k])){
                            $sub_commodity_list[$sub_k]['all_sku_ids'] = $g_sub_sku_id_all[$sub_k];
                            array_push($sku_jj, ...$sub_commodity_list[$sub_k]['all_sku_ids']);
//                            $sku_jj =  array_merge($sku_jj,$sub_v['all_sku_ids']);
//                            $sku_jj[]=  $sub_v['all_sku_ids'];
//                            array_push($sku_jj, ...$sub_v['all_sku_ids']);
                            $sub_com_sku[$sub_v['group_sub_commodity_id']] = $sub_commodity_list[$sub_k]['all_sku_ids'];
                        }
                        $sub_v_stock = $group_sub_rel_stock[$sub_v['group_sub_commodity_id']] ?? 0;
                        if($sub_v_stock>0){
                            $sub_commodity_id_arr[]=$sub_v['group_sub_commodity_id'];
                        }

                    }
//                    print_json(array_column($card_list,'id'));
//                    print_json($sku_jj);
                    //返回满足的卡券ID，以及卡券对应的组件
                    $goods_use_card_sku_arr=[];
                    $goods_use_card_id_arr=[];
//                    print_json($card_list);

                    if($card_list){
                        //这里应该要去掉chane_car==1的了
                        //但是领券列表又要包含chane_car==1

//                        print_json($sub_commodity_id_arr);
//                        print_json($sku_jj,$card_list);
                        $h_card_list_arr = $this->_detail_card($goods_data['commodity_set_id'],$sku_jj, $sub_commodity_id_arr,1,1,array_column($card_list,'id'),$sub_com_sku,[],$channel_type,$card_list);
//                        print_json($h_card_list_arr);

                        $h_card_list =$h_card_list_arr['card_list'];
                        $h_card_id = $h_card_list_arr['mz_card'];
                        $goods_use_card_sku_arr = $h_card_list_arr['goods_use_card_sku_arr'];
                        $list_goods_use_card_sku_arr = $h_card_list_arr['list_goods_use_card_sku_arr'];
                        $main_goods_card = $h_card_list_arr['goods_card'];//主商品是否配置了卡券
                        $goods_use_card_id_arr = $h_card_list_arr['goods_use_card_id_arr'];
                        foreach ($card_list as $c_k=> $c_v){
                            if(!in_array($c_v['id'],$h_card_id) && $c_v['set_sku_ids']){
                                $not_use_card_id_arr[]=$c_v['id'];//应该用这个去隔绝下面的卡券

                                unset($card_list[$c_k]);
                            }
                        }
                        //这里为了下面的卡券领取列表--用上面的not_use
                        $sku_ky_cards =  array_column($card_list,'id');
                    }

//                    print_json($sub_commodity_list);

                    //can_use_card 可用券类型，1可用券，2其他规格可用，0无券
//                    print_json($main_goods_card);
                    foreach ($sub_commodity_list as $sub_k=>$sub_v){
                        $card_key = $goods_data['commodity_set_id'].'_'.$sub_v['group_sub_commodity_id'];
                        $sub_goods_mz_card_sku =  $goods_use_card_sku_arr[$card_key]??[];
                        $sub_goods_mz_all_card_sku =  $main_goods_card[$goods_data['commodity_set_id']]??[];
                        $sub_goods_mz_card_id =  $goods_use_card_id_arr[$card_key]??[];
                        $can_use_card = 0;
                        if($sub_goods_mz_card_id){
                            $can_use_card = 2;
                            if(in_array($sub_v['set_sku_id'],$sub_goods_mz_card_sku)){
                                $can_use_card=1;
                            }
                        }
//                        print_json($card_list,$sub_goods_mz_card_sku);
                        //有卡券列表，但是没有满足的sku,这个时候才代表了没有设置
                        if($card_list && !$sub_goods_mz_all_card_sku){
                            $sub_commodity_list[$sub_k]['can_use_card_skuId'] = $sub_v['all_sku_ids'];
                            $sub_commodity_list[$sub_k]['can_use_card'] = 1;
                        }else{
                            $sub_commodity_list[$sub_k]['can_use_card_skuId'] = $sub_goods_mz_card_sku;
                            $sub_commodity_list[$sub_k]['can_use_card'] = $can_use_card;
                        }


//                        $sub_commodity_list[]=$sub_v['group_sub_commodity_id'];
                    }

                }


                Logger::error('subgoods:3', ['gc' => count($sub_commodity_list_arr)]);

//                if($sub_commodity_list_arr){
//                    if(array_diff(array_unique($com_set_sub_c_id),array_unique($com_list_sub_c_id))){
//                        $goods_data['is_mate']=0;
//                        $sub_commodity_list_arr=[];
//                        Logger::error('goodsNotAll'.$id);
//                    }
//                }
                Logger::error('goodslistnotsub', ['id' => $id, 'price' => $sub_com_min_price]);
                if (!$sub_commodity_list_arr) {
                    $goods_data['discount_price_range_end'] = $goods_data['original_price_range_start'] = $goods_data['original_price_range_end'] = $goods_data['discount_price_range_start'] = sprintf("%.2f", $sub_com_min_price);
                    if ($goods_data['ac_dis_type']) {
                        if ($goods_data['ac_dis_type'] == 1) {
                            $limit_dis = $goods_data['ac_dis_count'] / 10;
                            //限时购更改价格
                            $limit_sub_price = round($sub_com_min_price * $limit_dis, 2); //商品优惠金额
                        } elseif ($goods_data['ac_dis_type'] == 3) {
                            $limit_sub_price = $goods_data['ac_dis_count'];
                        } else {
                            $limit_sub_price = price_positive_fmt($sub_com_min_price - $goods_data['ac_dis_count']);
                        }
                        $goods_data['limit_original_price_range_start'] = $goods_data['limit_original_price_range_end'] = sprintf("%.2f", $sub_com_min_price);
                        $goods_data['limit_discount_price_range_start'] = $goods_data['limit_discount_price_range_end'] = $limit_sub_price;
                    }

                } else {
                    $sort_k = [];
//                    print_json($commodity_set_list);
                    foreach ($sub_commodity_list as $k => $item) {
                        foreach ($commodity_set_list as $s_k => $setitem) {
                            if ($item['group_sub_commodity_id'] &&$setitem['commodity_id'] == $item['group_sub_commodity_id']) { //是组合子商品
                                //0423版本
                                $sub_commodity_list[$k]['count_stock'] = $group_sub_count_stock[$setitem['commodity_id']] ?? 0;
                                if ($sub_commodity_list[$k]['count_stock'] == 0) $sub_commodity_list[$k]['count_stock'] = $group_must_sub_count_stock[$setitem['commodity_id']] ?? 0;
                                if (!empty($group_must_sub_count_stock)) {
                                    $goods['commodity_data']['count_stock'] = min($group_must_sub_count_stock);
                                } else if (!empty($group_sub_count_stock)) {
                                    $goods['commodity_data']['count_stock'] = min($group_sub_count_stock);
                                } else {
                                    $goods['commodity_data']['count_stock'] = 0;
                                }

                                if ($sub_commodity_list[$k]['count_stock'] == 0) {
                                    $sub_commodity_list[$k]['canot_buy'] = 1;
                                } else {
                                    $sub_commodity_list[$k]['canot_buy'] = $dbCommoditySetSku->commodityStatus($shelves, $setitem['commodity_id']);
                                }
//                                echo $setitem['commodity_id'].'--'.$s_k."<br/>";

                                $sort_k[$k] = $s_k;
                                $sub_commodity_list[$k]['is_mate'] = 1;
                                $sub_commodity_list[$k]['relate_car'] = 0;
                                if ($item['relate_car_ids']) {
                                    $sub_commodity_list[$k]['relate_car'] = 1;
                                    if (!$this->user['car_series_id']) {
                                        $sub_commodity_list[$k]['is_mate'] = 2;
                                    }
                                }
                                if (!$item['image']) {
                                    $sub_commodity_list[$k]['image'] = $item['cover_image'];
                                }
                                $sub_commodity_list[$k]['initial_num'] = $setitem['initial_num'];
                                $sub_commodity_list[$k]['user_can_des'] = $setitem['user_can_des'];
                                if (isset($seckill_info['dis_type'])) {
                                    if ($seckill_info['dis_type'] == 3) {
                                        $sub_commodity_list[$k]['user_can_des'] = 0;//一口价不可以修改数量
                                    }
                                }

                                $sub_commodity_list[$k]['can_select'] = $setitem['can_select']; //can_select 为1的时候可以取消勾选
                                //                            if($setitem['machine_oil_type'] > 0){ //机油商品
                                //                                //此处为 6L 的车
                                //                                if($setitem['machine_oil_type'] == 1){
                                //                                    $sub_commodity_list[$k]['initial_num'] = 2;
                                //                                }
                                //                            }
                                $sub_commodity_list[$k]['work_hour_free'] = 0;
                                $sub_commodity_list[$k]['work_time_code'] = '';
                                $sub_commodity_list[$k]['work_time_number'] = 0;
                                $sub_commodity_list[$k]['work_time_price'] = 0;

                                if (!empty($item['relate_car_work_hour'])) {
                                    //获取工时编码
                                    $relate_car_work_hour = json_decode($item['relate_car_work_hour'], true);

                                    foreach ($relate_car_work_hour as $ki => $itemhous) {
                                        if (in_array($this->user['car_18n'], $itemhous)) {
                                            $sub_commodity_list[$k]['work_time_code'] = $ki;
                                            $part_time_info = $work_model->getOneByPk($ki);
                                            if ($part_time_info) {
                                                if ($goods_data['work_hour_type']) {
                                                    $sub_commodity_list[$k]['work_time_number'] = $part_time_info['wi_qty'];
                                                }
                                                $sub_commodity_list[$k]['work_time_code'] = $part_time_info['wi_code'];
                                            }
                                            //获取工时价格
                                            //                                        $widata['dlr_code'] = $dlr_code;
                                            //                                        $widata['car_config_code'] = $this->user['car_18n'];
                                            //                                        $widata['repair_type'] = $item['work_hour_type'];
                                            //                                        $price_ret = $dbCommoditySetSku->getE3sPrice($widata);
                                            $sub_commodity_list[$k]['work_time_price'] = $goods_data['work_time_price'];

                                            break;
                                        }
                                    }
                                }
                                if ($setitem['machine_oil_type'] == 1) { //机油商品
                                    //此处为 6L 的车  油形要跟着车走--暂时还没有做，等VV确定
                                    $sub_commodity_list[$k]['initial_num'] = $user['18_oil_type'] - 4;
                                    //1L工时都==0
                                    $sub_commodity_list[$k]['work_hour_free'] = 0;
                                    $sub_commodity_list[$k]['work_time_code'] = '';
                                    $sub_commodity_list[$k]['work_time_number'] = 0;
                                    $sub_commodity_list[$k]['work_time_price'] = 0;
                                }
                                unset($sub_commodity_list[$k]['relate_car_work_hour']);
                            }
                        }
                        if ($item['sp_value_list']) {
                            $sp_value_list = $this->sp_model->getAllList(['a.id' => ['in', $item['sp_value_list']]]);
                            $sku_value = '';
                            $sp_name = "";
                            if ($sp_value_list) {
                                foreach ($sp_value_list as $vv) {
                                    $sku_value .= $vv['sp_name'] . ":" . $vv['sp_value_name'] . ", ";
                                    $sp_name .= $vv['sp_name'] . " ";
                                }
                                $sub_commodity_list[$k]['sp_value_name'] = trim($sku_value, ', ');
                                $sub_commodity_list[$k]['sp_name'] = $sub_commodity_list[$k]['sp_name_title'] = $sp_name;
                            }
                        }
                    }
//                    print_json($sub_commodity_list);

                }

                Logger::error('subgoods:4', ['gc' => count($sub_commodity_list_arr)]);

                //这里再一次判断
                if (!$sub_commodity_list_arr) {
                    if (!$this->user['car_series_id']) {
                        $goods_data['is_mate'] = 2;
                    } else {
                        $goods_data['is_mate'] = 0;
                    }

                    $goods_data['commodity_dis_user_segment'] = 0;
                    $goods_data['commodity_dis_act_user_segment'] = 0;
                    $goods_data['commodity_dis_label'] = '';
                    $goods_data['commodity_dis_label_cn'] = '';
                }

                if ($sub_commodity_list) {
//                    var_dump($sort_k);
//                    print_json($sub_commodity_list_arr);
                    if($sub_commodity_list_arr){
                        array_multisort($sort_k, SORT_ASC, $sub_commodity_list); //重新排序
                    }
                    foreach ($sub_commodity_list as $k => $v) {
                        if ($v['initial_num'] == 0) {
                            unset($sub_commodity_list[$k]);
                            // 级联的规则也要移除
                            foreach ($sp_associated_data as $sp_associated_key => $sp_associated_val) {
                                if ($sp_associated_val['sub_commodity_id'] == $v['group_sub_commodity_id'] ||
                                    $sp_associated_val['assoc_sub_commodity_id'] == $v['group_sub_commodity_id']) {
                                    unset($sp_associated_data[$sp_associated_key]);
                                }
                            }
                        }
                    }
                    $commodity_dis_info = $this->getCommoditySegmentDiscount($goods_data['commodity_id']);
                    $groups_data['commodity_dis_info'] = $commodity_dis_info;
                    foreach ($sub_commodity_list as $k => $v) {
                        $sub_commodity_list[$k]['old_price'] = $sub_commodity_list[$k]['price'];
                        $sub_commodity_list[$k]['gift_card_count'] = [];
                        unset($sub_commodity_list[$k]['relate_car_ids']);
                    }
                    //总价
                    $groups_data['sub_commodity_list'] = array_values($sub_commodity_list);
                }
            }else{
                $set_sku_ids = array_column($sku_list,'sku_id');
                $goods_info[]=['goods_id' => $goods_data['commodity_set_id'], 'sub_goods_id' => '', 'sku_id' => $set_sku_ids, 'oil_type' => 0];
                if($card_list){
                    $goods_card = $this->card_list_ok($card_rules,$goods_info,$card_list,$goods_data['commodity_set_id']);

                    $sku_ky_cards = array_column($goods_card,'id');
                    //车型不匹配不显示
                    foreach ($card_list as $v){
                        if(!in_array($v['id'],$sku_ky_cards) || !$sku_ky_cards){
                            $not_use_card_id_arr[]=$v['id'];
                        }
                    }
                }

//                print_json($not_use_card_id_arr);

            }

            if ($goods_data['is_mate'] != 1) {
                $goods_data['seckill_dis'] = $goods_data['limit_dis'] = '';//如果不匹配时候没有子商品时候直接没有秒杀+限时
                $goods_data['seckill_info'] = $goods_data['limit_info'] = $seckill_info = $limit_info = [];//如果不匹配时候没有子商品时候直接没有秒杀+限时
            }
            if ($goods_data['is_mate'] != 1) {
                $goods_data['commodity_dis_label'] = '';
                $goods_data['commodity_dis_label_cn'] = '';
            }
            $goods_data['groups_data'] = $groups_data;
            unset($goods_data['group_commodity_ids_info']);
            ////////////////////////组合商品结束//////////////////////////
            Logger::error('goodsdetailruntime2', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type . '-' . $id);

            $canot_buy = ['status' => 0, 'word' => '']; //不能购买文案\状态
            $hide_numer = 0;
            $buy_word = "立即购买";
            $car_info = $this->_getCarer($this->user['bind_unionid'], $this->user['member_id'], $this->user['one_id'], $this->channel_type);
            if (empty($car_info)) {
                $car_info = [];
            }
            //获取用户基本信息
            $userInfo = $f_b_info;
            $car_info['brand'] = $userInfo['brand'] ?? '';
            $car_info['have_brand_car'] = $userInfo['have_brand_car'] ?? 0;

            $car_point_dh_goods = array_merge($this->jk_goods, $this->pk_goods);
            $is_not_cart = 0; //不能放入购物车 0可以1 不可以

            //金银卡专享,只可以兑换一次 积分兑换已下架
            //            if ($id == $this->jk_one_point_good) {
            //                if (!$car_info) {
            //                    return $this->re_msg('需要车主才能购买', 402);
            //                }
            //                if (!in_array($car_info['card_degree_name'], $this->car_vip)) {
            //                    return $this->re_msg('需要金银卡车主才能购买', 402);
            //                }
            //                //修改成新活动可以重新拉取卡劵
            //                $signCount = AcSignCount::where('is_enable', 1)->where('userid', $this->user_id)
            //                    ->where('dlr_code', $this->channel_type)
            //                    ->where('created_date', '>', '2020-12-04 00:00:00')
            //                    ->where('is_get_card', 1)->find();
            //                if (!empty($signCount)) {
            //                    return $this->re_msg('该商品仅可以兑换一次', 411);
            //                }
            //                $buy_word = "立即兑换";
            //            }
            // 积分兑换已下架
            //            if (in_array($id, $car_point_dh_goods)) {
            //                $is_not_cart = 1;
            //                if (!$car_info) {
            //                    return $this->re_msg('需要车主才能购买', 402);
            //                } else {
            //                    if (in_array($car_info['card_degree_name'], $this->car_vip)) {
            //                        if (in_array($id, $this->pk_goods)) {
            //                            $pk_id = $this->jk_goods[array_search($id, $this->pk_goods)];
            //                            return $this->re_msg('等级不匹配', 402);
            //                        }
            //                    } else {
            //                        if (in_array($id, $this->jk_goods)) {
            //                            $pk_id = $this->pk_goods[array_search($id, $this->jk_goods)];
            //                            return $this->re_msg('等级不匹配', 402);
            //                        }
            //                    }
            //                }
            //                $buy_word = "立即兑换";
            //            }
            $goods_data = $goods['commodity_data'];
            //todo ccs套餐判断用户  不能加购物车
            if ($goods_data['commodity_class'] == 4) {
                $vin = $car_info['vin'] ?? '';
                if (empty($vin)) {
                    return $this->re_msg('需要车主才能购买', 402);
                }
                $Ccs = new Ccs();
                $ccs_info = $Ccs->page_info($this->unionid, $car_info['vin']);
                if (isset($ccs_info['userIdentity'])) {
                    if (in_array($ccs_info['userIdentity'], [1, 2])) {
                        return $this->re_msg('需要车主才能购买', 402);
                    }
                    if ($ccs_info['userIdentity'] == 4 && in_array($id, $this->sj_ccs())) {
                        $canot_buy['status'] = 1;
                        $canot_buy['word'] = "暂无购买";
                    }
                }
                $is_not_cart = 1;
            }
            //电子卡券不能放入购物车  不能选数量+充电也不能
            if (in_array($goods_data['commodity_class'], [2, 3, 7]) || $goods_data['is_integral_shop'] == 2) {
                $is_not_cart = 1;
                $hide_numer = 1;
            }
            if (in_array($goods_data['dd_commodity_type'], [1, 3, 4,12])) {
                $hide_numer = 1;
            }
            //众筹信息
            $crowdfund_info = [];

            //增加商品分类中文名
            if ($goods_data['comm_type_id']) {
                $goods_type_info = redis('detail_goods_type' . $goods_data['comm_type_id']);
                if (empty($goods_type_info)) {
                    $goods_type_model = new DbCommodityType();
                    $goods_type_info = $goods_type_model->alias('a')->field('a.comm_type_name,b.id comm_type_two_id,b.comm_parent_id comm_type_one_id')->join('t_db_commodity_type b', 'a.comm_parent_id=b.id')->where(['a.id' => $goods_data['comm_type_id'], 'a.is_enable' => 1])->find();
                    redis('detail_goods_type' . $goods_data['comm_type_id'], $goods_type_info, 60);
                }
                $goods_data['commodity_type'] = isset($goods_type_info['comm_type_name']) ? $goods_type_info['comm_type_name'] : '';
                $goods_data['comm_type_two_id'] = isset($goods_type_info['comm_type_two_id']) ? $goods_type_info['comm_type_two_id'] : '';
                $goods_data['comm_type_one_id'] = isset($goods_type_info['comm_type_one_id']) ? $goods_type_info['comm_type_one_id'] : '';
            }

            //mail_method 1到店2快递3都有
            if ($goods_data['is_mail'] == 1 && $goods_data['is_store'] == 1) {
                $goods_data['mail_method'] = 3;
            } elseif ($goods_data['is_mail'] == 1 && $goods_data['is_store'] == 0) {
                $goods_data['mail_method'] = 2; //快递
            } else {
                $goods_data['mail_method'] = 1; //到店
            }
            if ($goods_data['mail_type']) {
                $goods_data['mail_method'] = $goods_data['mail_type'];
            }
            $goods_data['is_card_available'] = 1; //这个字段不做判断了，通过t_db_commodity_card 了，强制可领取

            //n_dis,group_dis,pre_sale,suit_dis,full_dis
            //1.判断是否有优惠套装
            $suit_result = [];
            if (isset($goods_data['suit_dis_id'])) {
                if ($goods_data['suit_dis_id']) {
                    $suit_result = $this->suitList(['suit_ids' => $goods_data['suit_dis_id'], 'commodity_ids' => $goods_data['commodity_id']], $user, $this->channel_type);
                    if ($suit_result['msg']) {
                        $suit_result = $suit_result['msg'];
                        if (isset($suit_result['list'])) {
                            $suit_count = $suit_result['suit_count'];
                            $suit_result = isset($suit_result['list']) ? $suit_result['list'][0] : [];
                            $suit_result['suit_count'] = $suit_count;
                        }

                    }


                }
            }

            //2.判断是否有满优惠
            $full_list = [];

            //            var_dump($goods_data['full_dis_id']);die();
            if (isset($goods_data['full_dis_id']) && $goods_data['full_dis_id'] > 0) {
                $full_list = $this->_full($goods_data['full_dis_id'], $goods_data['commodity_id'], $this->channel_type);
            }

            // 判断是否有满减优惠-工时
            $full_wi_list = [];
            if (isset($goods_data['full_wi_dis_id']) && $goods_data['full_wi_dis_id'] > 0) {
                $full_wi_list = $this->_full_wi($goods_data['full_wi_dis_id'], $goods_data['commodity_id'], $this->channel_type);
            }

            //3.判断是否有团购
            $g_info = [];
            if (isset($goods_data['group_dis_id'][0])) {
                $g_info = $this->_group($id, $goods_data['group_dis_id'][0], $goods_data['commodity_id']);
            }


            //5.判断是否N件N折
            $n_dis_info = [];
            if (isset($goods_data['n_dis_id']) && $goods_data['n_dis_id'] > 0) {

                $n_dis_info = $this->_nn($goods_data['commodity_id']);
            }

            //6.获取用户相关信息 --暂时不要
            //            $wxInfo = $this->getWxInfo($this->openid, $this->channel_type);

            //7.判断是否预购
            $pre_sale = [];
            if (isset($goods_data['pre_sale_id'][0])) {
                $pre_sale = $this->_pre_sale($id, $goods_data['pre_sale_id'][0]);
                if ($pre_sale) {
                    if ($pre_sale['pre_status'] == 1) {
                        $can_number = 1;
                        //根据预售能购买数量，《0就不能购买了，之前是每次都能购买的
                        if ($pre_sale['can_buy'] <= 0) {
                            $can_number = 0;
                            $canot_buy = ['status' => 1, 'word' => '已售罄'];
                        }
                        $hide_numer = 1;
                        //                    $buy_word   = sprintf("立即支付定金￥%s", $pre_sale['front_money']);
                        $is_not_cart = 1;
                        $pre_sale['pre_djs'] = 1;
                        $pre_sale['yd_times'] = sprintf("%s~%s", date('Y.m.d', strtotime($pre_sale['front_s_time'])), date('m.d', strtotime($pre_sale['front_e_time'])));
                        $pre_sale['wk_times'] = sprintf("%s~%s", date('Y.m.d', strtotime($pre_sale['balance_s_time'])), date('m.d', strtotime($pre_sale['balance_e_time'])));
                    } else {
                        //不在预售定金时间内不显示预售信息
                        $pre_sale = [];
                        $goods_data['pre_sale_id'] = '';
                    }
                }
            }
            //先去掉规格排序  数据发现后台没有做排序了

//            $sku_arr = array();
//            if ($sku_list) {
//                //处理排序
//                foreach ($sku_list as $k => $v) {
//                    $k_arr = explode(',', $k);
//                    sort($k_arr);
//                    $k_s = implode(',', $k_arr);
//                    $sku_arr[$k_s] = $v;
//                }
//                $sku_list = $sku_arr;
//            }
            //            var_dump($sku_list);
            $sp_list = $goods['sp_list'];
            //处理规则只有一行的
            if (count($sp_list) == 1 && $is_grouped_flag == 0) {
                foreach ($sp_list as $k => $v) {
                    if ($sp_list[$k]['sp_value_list']) {
                        foreach ($sp_list[$k]['sp_value_list'] as $kk => $vv) {
                            if ($sku_list[$vv['sp_value_id']]['stock'] < 1) {
                                unset($sp_list[$k]['sp_value_list'][$kk]);
                            }
                        }
                        $sp_list[$k]['sp_value_list'] = array_values($sp_list[$k]['sp_value_list']);
                        if (count($sp_list[$k]['sp_value_list']) == 0) {
                            unset($sp_list[$k]);
                            $canot_buy = ['status' => 1, 'word' => '无库存'];
                        }
                    }
                }
            }


            //团购splist
            $group_info = [];
            $sku_list_group = [];
            $sp_list_group = [];
            $group_act_list = [];

            if ($group_id) {
                if (!$group_order_code) {
                    $where = ['a.id' => $group_id, 'b.commodity_id' => $id, 'a.is_enable' => 1, 'a.start_time' => ["<=", $time], 'a.end_time' => [">=", $time]];
                } else {
                    $where = ['a.id' => $group_id, 'b.commodity_id' => $id, 'a.is_enable' => 1];
                }

                $group_model = new DbFightGroup();
                $group_info = $group_model->getGroupInfo(['where' => $where, 'field' => "a.id,a.title,a.start_time,a.end_time,a.people_number,a.purchase_number,a.buy_hour,a.rule,b.commodity_id,b.lowest_price"]);

                if (!$group_info) {
                    return $this->re_msg('团购未开始或已结束', 414);
                } else {
                    $is_not_cart = 1;
                    $ac_group_model = new AcGroup();
                    $openid_ac_number = $ac_group_model->getOne(['where' => ['user_id' => $this->user_id, 'group_id' => $group_id, 'commodity_id' => $id, 'status' => ['in', '2,3,4,5']], 'field' => "sum(number) number"]); //,'commodity_id'=>$id,2022-05-15 14:36:19 ljw，rt
                    $can_number = $group_info['purchase_number'] - $openid_ac_number['number'];
                    if (!$group_info['purchase_number']) {
                        $can_number = 999;
                    }
                    $ac_group_list_where = ['group_id' => $group_id, 'commodity_id' => $id, 'dlr_code' => $this->channel_type, 'group_start_time' => ['>', date('Y-m-d H:i:s', strtotime(sprintf("%s -%s hours", $time, $group_info['buy_hour'])))], 'status' => ['in', '3'], 'user_id' => ['<>', $this->user_id]];

                    //参与
                    if ($group_order_code) {
                        $ac_group_list_where['group_code'] = $group_order_code;

                        //状态:1下单，2待支付，3已支付,4待发货,5已发货,6已退款,7已取消  已下单的不计入
                        $f_group_order = $ac_group_model->getOne(['where' => ['group_code' => $group_order_code, 'dlr_code' => $this->channel_type, 'commodity_id' => $id, 'status' => ['in', '4,5']]]); //已发货，待发货的
                        //                        var_dump($ac_group_model->getLastSql());die();
                        $group_order = $ac_group_model->getList(['where' => ['group_code' => $group_order_code, 'commodity_id' => $id, 'status' => ['in', '2,3']]]); //团购是否存在
                        $openid_group_code_ac = $ac_group_model->getOne(['where' => ['group_code' => $group_order_code, 'user_id' => $this->user_id, 'status' => ['in', '3,4,5']]]);
                        if (!$group_order || $f_group_order || $openid_group_code_ac) {
                            return $this->re_msg('该团购不可参团', 414);
                        } else {
                            if (strtotime(sprintf("%s+ %s hours", $group_order[0]['group_start_time'], $group_info['buy_hour'])) < time()) {
                                return $this->re_msg('团购未开始或已结束', 414);
                            } elseif (count($group_order) >= $group_info['people_number']) {
                                return $this->re_msg('该团满员了', 414);
                            }
                        }
                    }

                    $group_act_list = $ac_group_model->getGroupList(['where' =>$ac_group_list_where, 'group' => "group_code", 'field' => "count(id) count,openid,user_id,group_code,group_start_time,nickname,headimg", 'limit' => 2, 'order' => 'status asc , group_start_time asc']); //status 从2,3改成3已支付,未支付不参与拼团
                    //                        if($test==1){
                    //                          echo $ac_group_model->getLastSql();
                    //                        }
                    if ($group_act_list) {
                        foreach ($group_act_list as $k => $v) {
                            $numbers = $group_info['people_number'] - $v['count'];

                            if ($numbers < 1) {
                                unset($group_act_list[$k]);
                            } else {
                                $group_act_list[$k]['number'] = $numbers;
                                //                                    $group_act_list[$k]['nickname'] = isset($wxInfo['nickname']) ? $wxInfo['nickname'] : '';
                                //                                    $group_act_list[$k]['pic']      = isset($wxInfo['headimgurl']) ? $wxInfo['headimgurl'] : config('view_replace_str.__STATIC__') . "index/images/head_img.jpg";
                                $group_act_list[$k]['time'] = $this->time_diff(time(), strtotime(sprintf("%s +%s hours", $v['group_start_time'], $group_info['buy_hour'])));
                                $group_act_list[$k]['end_time'] = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s hours", $v['group_start_time'], $group_info['buy_hour'])));
                            }
                        }
                    }
                }
                $sku_list_group = $goods['fight_sku_list'];
                $sp_list_group = $goods['fight_sp_list'];
                $sku_arr = [];
                if ($sku_list_group) {
                    //处理排序
                    foreach ($sku_list_group as $k => $v) {
                        $k_arr = explode(',', $k);
                        sort($k_arr);
                        $k_s = implode(',', $k_arr);
                        $sku_arr[$k_s] = $v;
                    }
                    $sku_list_group = $sku_arr;
                    $group_low_price = min(array_column($sku_list_group, 'price')); //团购最低价
                    $group_info['lowest_price'] = $group_low_price;
                }
                if (count($sp_list_group) == 1) {
                    foreach ($sp_list_group as $k => $v) {
                        if ($sp_list_group[$k]['sp_value_list']) {
                            foreach ($sp_list_group[$k]['sp_value_list'] as $kk => $vv) {
                                if ($sku_list_group[$vv['sp_value_id']]['stock'] < 1) {
                                    unset($sp_list_group[$k]['sp_value_list'][$kk]);
                                }
                            }
                            $sp_list_group[$k]['sp_value_list'] = array_values($sp_list_group[$k]['sp_value_list']);
                        }
                    }
                    $sku_list_group = array_values($sku_list_group);
                }
                $sp_list_group = array_values($sp_list_group);
            }

            $sku_image = $goods_data['sku_image'];

            if ($goods_data['count_stock'] < 1 && $group_id) {
                //                return $this->re_msg('商品库存不足', 414);
            }

            //            if ($goods_data['car_series_id']) {
            //                $car_series = explode(',', $goods_data['car_series_id']);
            //                if (!$this->user['car_series_id']) {
            //                    $goods_data['is_mate'] = 0;
            //                } else {
            //                    if (!in_array($this->user['car_series_id'], $car_series)) {
            //                        $goods_data['is_mate'] = 0;
            //                    } else {
            //                        $goods_data['is_mate'] = 1;
            //                    }
            //                }
            //
            //            }

            //H5端自动匹配所有车型  官微规则---曾志青
            //            if (!$this->openid) {
            //                $goods_data['is_mate'] = 1;
            //            }

            $car_name = '';
            if (isset($car_info['name'])) {
                $car_name = sprintf("(%s)", $car_info['name'] . '--' . $car_info['vin']);
            }

            $phone = isset($car_info['mobile']) ? $car_info['mobile'] : '';
            $name = isset($car_info['name']) ? $car_info['name'] : '';
            if ($this->user['phone']) {
                $phone = $this->user['phone'];
            }
            if ($this->user['name']) {
                $name = $this->user['name'];
            }
            //            $_data = array(
            //                'openid' => $this->openid,
            //                'user_id' => $this->user_id,
            //                'commodity_id' => $id,
            //                'dlr_code' => $this->channel_type,
            //                'mothod' => 1,
            //                'source' => $this->source,
            //                'vin' => isset($car_info['vin']) ? $car_info['vin'] : '',
            //                'license_plate' => isset($car_info['car_no']) ? $car_info['car_no'] : '',
            //                'name' => $name,
            //                'phone' => $phone,
            //                'car_series_id' => $this->user['car_series_id'],
            //            );
            //            browing($_data);//插入访问记录

            $user_collection = $this->_checkCollection($id, $this->user_id, $this->brand);
            $goods_data['collected'] = 0;
            if ($user_collection) {
                //已经收藏
                $goods_data['collected'] = 1;
            }

            $can_use_card = 0;
            //todo 卡券改成通过专营店+商品编码获取卡券
            if ($goods_data['pay_style'] == 3) {
                $q_hz = $goods_data['q_hz'] = "积分";
                $q_qz = $goods_data['q_qz'] = "";
                $bs = $goods_data['bs'] = 10;
                if ($this->brand <> 3) {
                    $is_not_cart = 1;
                }
                //                $is_not_cart = 1;
            } else {
                $q_hz = $goods_data['q_hz'] = "";
                $q_qz = $goods_data['q_qz'] = "￥";
                $bs = $goods_data['bs'] = 1;
            }
            if ($pre_sale) {
                $q_hz = $goods_data['q_hz'] = "";
                $goods_data['discount_price_range_start'] = $goods_data['discount_price_range_start'] - $pre_sale['yh_money'];
                $goods_data['discount_price_range_end'] = $goods_data['discount_price_range_end'] - $pre_sale['yh_money'];
                //                    $goods_data['original_price_range_start'] = $goods_data['original_price_range_start'] - $pre_sale['yh_money'];
                //                    $goods_data['original_price_range_end']   = $goods_data['original_price_range_end'] - $pre_sale['yh_money'];

            }

            //            $goods_data['price_t']                    = sprintf("<div class=\"prc\">%s<span id=\"prc-pdt\">%s</span>~%s<span id=\"prc-pdt\">%s</span></div>", $q_qz, $goods_data['discount_price_range_start'] * $bs . $q_hz, $q_qz, $goods_data['discount_price_range_end'] * $bs . $q_hz);
            $goods_data['discount_price_range_start'] = sprintf("%.2f", $goods_data['discount_price_range_start']);
            $goods_data['discount_price_range_end'] = sprintf("%.2f", $goods_data['discount_price_range_end']);
            $goods_data['original_price_range_start'] = sprintf("%.2f", $goods_data['original_price_range_start']);
            $goods_data['original_price_range_end'] = sprintf("%.2f", $goods_data['original_price_range_end']);
            //            $goods_data['original_price_range_end']   = round($goods_data['original_price_range_end']);//可能会没有小数点
            //现价
            $jf_zk_point = ""; //积分折扣

            //            if ($goods_data['discount_price_range_start'] == $goods_data['discount_price_range_end']) {
            //                $d_price               = sprintf("<small>%s</small><strong>%s</strong><del></del>", $q_qz, $goods_data['discount_price_range_start'] * $bs . $q_hz);
            //                $goods_data['price_t'] = sprintf("<div class=\"prc\">%s<span id=\"prc-pdt\">%s</span></div>", $q_qz, $goods_data['discount_price_range_start'] * $bs . $q_hz);
            //                if ($goods_data['point_discount'] > 0) {
            //                    $jf_zk_point = sprintf("<span class=\"sign-mj\">全额积分价</span> %s积分", $goods_data['discount_price_range_start'] * $goods_data['point_discount']);
            //                }
            //            } else {
            //                $d_price = sprintf("<small>%s</small><strong>%s</strong><small>~%s</small><strong>%s</strong>", $q_qz, $goods_data['discount_price_range_start'] * $bs . $q_hz, $q_qz, $goods_data['discount_price_range_end'] * $bs . $q_hz);
            //                if ($goods_data['point_discount'] > 0) {
            //                    $jf_zk_point = sprintf("<span class=\"sign-mj\">全额积分价</span> %s~%s积分", $goods_data['discount_price_range_start'] * $goods_data['point_discount'], $goods_data['discount_price_range_end'] * $goods_data['point_discount']);
            //                }
            //            }
            //            if ($goods_data['original_price_range_start'] == $goods_data['original_price_range_end']) {
            //                $o_price = sprintf("<del>%s%s</del>", $q_qz, $goods_data['original_price_range_start'] * $bs . $q_hz);
            //            } else {
            //                $o_price = sprintf("<del>%s%s~%s%s</del>", $q_qz, $goods_data['original_price_range_start'] * $bs . $q_hz, $q_qz, $goods_data['original_price_range_end'] * $bs . $q_hz);
            //            }

            //            if ($goods_data['discount_price_range_start'] == $goods_data['original_price_range_start'] && $goods_data['discount_price_range_end'] == $goods_data['original_price_range_end']) {
            //                $goods_data['price'] = $d_price;
            //            } else {
            //                $goods_data['price'] = $d_price . $o_price;
            //            }
            //            $goods_data['zk_point_html'] = $jf_zk_point;
            //            var_dump($goods_data['price']);die();
            //            $goods_data['price'] .= $jf_zk_point;//加入积分折扣展示
            if ($goods_data['discount_price_range_start'] == 99999) {
                $goods_data['price'] = "价格待公布";
            }
            $integral = '';
            $dlr_integral = '';
            $carer_pv_point = isset($car_info['pv_point']) ? $car_info['pv_point'] : 0;
            //            if ($goods_data['factory_points']) {
            //                $integral = sprintf('<div class="bar1"><span class="cls">厂家积分</span>
            //                <span class="wording-right">当前积分<em>%s</em></span></div>', $carer_pv_point);
            //            }
            //添加车主能使用积分，支付方式=3的时候生效
            if ($goods_data['pay_style'] == 3) {
                $goods_data['carer_pv_point'] = $carer_pv_point;
            } else {
                $goods_data['carer_pv_point'] = 9999999;
            }
            $times = '';
            //            if ($goods_data['favourable_detail']) {
            //                $times = sprintf("<span class='cars'>%s</span>", $goods_data['favourable_detail']);
            //            }
            //9.客服信息
            //            $c_service = $this->_getCustomer();//客服唯一一个20170915 曾毅
            if (isset($car_info['name'])) {
                $c_nickname = sprintf("%s-%s.%s", $car_info['name'], $car_info['vin'], $car_info['car_series_name'] . $this->user_id);
            } else {
                $c_nickname = "访客" . $this->user_id;
            }
            $c_service = config('xi_kf_url') . sprintf("nissan-h5-human.html?hysc_good_id=%s&hysc_channel=%s&hysc_unionid=%s&hysc_guestNickname=%s", $id, $this->channel_type, $this->user['member_id'], urlencode($c_nickname)); //
            //https://xiaoiqd.dongfeng-nissan.com.cn/static/html-qichenApp/nissan-h5-human.html
            if (in_array($channel_type, ['QCAPP', 'QCSM'])) {
                if ($channel_type == 'QCAPP') {
                    $custome = 'qchysc_app';
                    $platform = 'web';
                    $channel_type_name = '启辰APP';
                } else {
                    $custome = 'qchysc_xcx';
                    $platform = 'weixin';
                    $channel_type_name = '启辰小程序';
                }
                //https://xiqd-test.dongfeng-nissan.com.cn/static/html-qichen/nissan-h5-human.html?aiccCustomInfo={"userId":"23211px","guestNickname":"王五","hysc_good_id":"38294","hysc_channel":"GWSM","hysc_unionid":"123456","brand_type":"qc"}&userDims={"platform":"web","channel": "pro_","channelName":"自定义","custome":"qchysc_app"}
                $type_name = "nissan-h5-human.html?aiccCustomInfo=" . urlencode(sprintf('{"userId":"%s","guestNickname":"%s","hysc_good_id":"%s","hysc_channel":"%s","hysc_unionid":"%s","brand_type":"qc"}', $this->user_id, $c_nickname, $id, $channel_type, $this->user['member_id'])) . "&userDims=" . urlencode(sprintf('{"platform":"%s","channel":"pro_","channelName":"%s商城","custome":"%s"}', $platform, $channel_type_name, $custome));
            } else {
                $type_name = "nissan-h5-human.html?aiccCustomInfo=" . urlencode(sprintf('{"userId":"%s","guestNickname":"%s"}', $this->user_id, $c_nickname)) . "&userDims=" . urlencode('{"platform":"web","channel":"pro_","channelName":"启辰小程序商城","custome":"qchysc_xcx"}');
            }

            $qc_c_service = config('xi_kf_qc_url') . $type_name; //
            $x_service_no_url = sprintf("hysc_good_id=%s&hysc_channel=%s&hysc_unionid=%s&hysc_guestNickname=%s&hysc_comeFrom=rchysc", $id, $this->channel_type, $this->user['member_id'], urlencode($c_nickname)); //

            //            $xn_kf            = config('xn_siteid');
            //            $params           = input('get.');
            //            $params['source'] = 2;
            //            $share_url = url('', $params, true, true);
            $shop_cart_model = new BuShoppingCart();
            $shop_cart_count = $shop_cart_model->getShopCartCount($this->user_id, $brand, $channel_type);
            $ls_ss_cout = redis('shop_cart_count-');
            if ($ls_ss_cout) {
                $shop_cart_count = $ls_ss_cout; //用于测试
            }
            if ($goods_data['count_stock'] < 1) {
                $canot_buy['status'] = 1;
                $canot_buy['word'] = "已售罄";
            }
            if ($canot_buy['status'] == 1 && $seckill_info) {
                $canot_buy['word'] = "已抢光";

                if ($canot_buy['status'] == 1 && $goods_data['count_stock'] > 0 && in_array($seckill_info['act_status'], [1, 2])) {
                    $canot_buy['word'] = "立即购买";
                }
            }
            if ($goods_data['listing_type'] == 2) {
                $crowdfund_info = $this->_crowdfund_info(0, $id, 1, $this->user_id);
                if ($crowdfund_info) {
                    $can_number = ($crowdfund_info['purchase_num'] - $crowdfund_info['user_number']);
                    //项目成功    `plan_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '进度状态:0未开始;1进行中-未达标;2进行中-已达标;3项目失败-待处理;4项目成功-自动;5项目成功-人为;6项目失败-自动;7项目失败-人为;',
                    if ($crowdfund_info['act_status'] == 3) {
                        if (in_array($crowdfund_info['plan_status'], [4, 5])) {
                            $canot_buy = ['status' => 1, 'word' => '项目成功']; //不能购买文案\状态
                        } else {
                            $canot_buy = ['status' => 1, 'word' => '项目结束']; //不能购买文案\状态
                        }
                    }
                    if ($crowdfund_info['act_status'] == 1) {
                        $canot_buy = ['status' => 1, 'word' => '暂未开始']; //不能购买文案\状态
                    }
                } else {
                    $canot_buy = ['status' => 1, 'word' => '未关联项目']; //不能购买文案\状态
                }
            }



            if ($goods_data['mail_type'] == 4) {
                $canot_buy['status'] = 1;
                $canot_buy['word'] = $goods_data['mail_show_word'];
            }
            $json_data = json_encode(array('sku_list' => $sku_list, 'sku_image' => $sku_image, 'sku_list_group' => $sku_list_group), JSON_UNESCAPED_UNICODE ^ JSON_UNESCAPED_SLASHES);
            redis('net-api-goods-sku-json-is_suit0' . $id . $this->channel_type, $json_data, 3600 * 5);
            $net_comment = new NetComment();
            $comment_res = $net_comment->goodsComment(['goods_id' => $id, 'pageSize' => 1], $this->user, $this->channel_type, $this->brand);
            $comment = $comment_res['msg'];
            $comment_count = $net_comment->getCount(['goods_id' => $id], $this->brand);
            $com_de_cout = redis('detail_comment_count-');
            if ($com_de_cout) {
                $comment_count = $com_de_cout; //用于测试--
            }
            $limit_com_model = new DbLimitDiscountCommodity();
            $limit_dis_enents = $limit_com_model->getDisEvents($goods_data['commodity_set_id']);

            $seckill_com_model = new DbSeckillCommodity();
            $seckill_dis_enents = $seckill_com_model->getDisEvents($goods_data['commodity_set_id']);
            if (isset($seckill_info['act_status']) && $seckill_info['act_status'] == 2) {
                // 已达限购数量 或 秒杀库存为0  可以加入购物车
                if ($can_number == 0 || $seckill_info['miaosha_stock'] == 0) {
//                    $is_not_cart = 0;//本身就是0了的
                } else {
                    $is_not_cart = 1; // 未达限购条件 不能加入购物车
                }
                // 秒杀库存和可购买数量都为0， 前端可以修改数量
                if ($can_number == 0 && $seckill_info['miaosha_stock'] == 0) {
                    $can_number = 0;
                    $hide_numer = 1;
                }
            }
            // 活动开始前可以加入购物车
            if (isset($seckill_info['act_status']) && $seckill_info['act_status'] == 1) {
                $buy_word = '原价购买';
//                $is_not_cart = 0;
            }

            $qr_code = '';
            // 官网 && 到店 dd_commodity_type  非0  9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券
            if (($channel_type == 'GWNET') && ($goods_data['dd_commodity_type'] != 0)) {
                $data = [
                    'commodityId' => $id
                ];
                $page = config('qr_code.rc')['detail'];
                $qr_params = [
                    'param' => json_encode($data),
                    'code_type' => 6,
                    'width' => 600,
                    'page' => $page,
                ];
                $rs = CarLive::create('car_live')->getSmallCodeImg($qr_params);
                if ($rs['code'] == 10000) {
                    $qr_code = $rs['data']['qrcode'];
                }
            }

            $des_id = 0;
            //商品分类-说明
            $des_data = (new DbCommodityDesType())
                ->where(['commodity_type' => $goods_data['comm_type_id'], 'is_enable' => 1, 'display_channel' => $goods_data['shelves_type']])
                ->field('id,commodity_type,display_channel, des_id')
                ->select();

            if ($des_data) {
                foreach ($des_data as $type) {
                    $des_id = $type['des_id'];
                    $types_arr[] = $type['commodity_type'];
                    $display_channel[] = $type['display_channel'];
                    $display_channel_str = implode(',', array_unique($display_channel));
                    $commodity_type_str = implode(',', array_unique($types_arr));
                }
                $des_type_data = (new DbCommodityDes())
                    ->where(['id' => $des_id, 'is_enable' => 1])
                    ->field('id, title, display_area, is_display, des')
                    ->find();
                $des_type_data['commodity_type'] = $commodity_type_str ?? '';
                $des_type_data['display_channel'] = $display_channel_str ?? '';
            }
            if (isset($goods_data['qsc_groups_data']) && !empty($goods_data['qsc_groups_data'])) {
                $is_not_cart = 1; // 不能加入购物车
            }

            //移动积分 不能放入购物车  不能选数量
            if (isset($goods_data['is_integral_shop']) && $goods_data['is_integral_shop'] == 2) {
                $is_not_cart = 1;
                $hide_numer = 1;
            }
            if ($source_special) {
                $is_not_cart = 1;
            }


            $expand_model = new DbCommodityExpand();
            $poster_show_channel = DbDlr::$channel_tre[$this->channel_type] ?? '';
            if ($poster_show_channel == '01') {
                $poster_list = $expand_model->getList(['where' => ['commodity_id' => $id, 'poster_show_channel' => 1, 'is_enable' => 1], 'field' => 'poster_image,poster_jump_type,poster_type_value']);
            } elseif ($poster_show_channel == '05') {
                $poster_list = $expand_model->getList(['where' => ['commodity_id' => $id, 'poster_show_channel' => 2, 'is_enable' => 1], 'field' => 'poster_image,poster_jump_type,poster_type_value']);
            }

            $card_list_tmp = [];
            // 0423版本 没登录不进入计算，不然会有很多卡券内粗要爆
            //sku卡券版本，新增没有登录要判断卡券是否要判断人群  && $user['id']
            $all_card_id=[];
//                        print_json($card_list,$can_get_card_list);

            $card_list = $can_get_card_list;
//            $not_use_card_id_arr
            if($card_list){
                foreach ($card_list as $k=>$v){
                    if(in_array($v['card_id'],$not_use_card_id_arr)){
                        unset($card_list[$k]);
                    }
//                    $all_card_id[]= $v['id'];
                }

            }
            if(!$card_list){
                $goods_data['not_article_work'] = '';
            }else{
                $card_list =  array_values($card_list);
            }

//            $can_user_card_goods_redis_name = $this->user_id.'can_user_card_goods_redis_name-';
//            redis($can_user_card_goods_redis_name.$id,implode(',',$all_card_id),7200);

            if ($crowdfund_info) {
                $is_not_cart = 1;
            }

            // 活动
            $cap_info          = [
                'is_activity_commodity' => 0,
                'is_activity_user'      => 0,
                'order_id'              => '',
                'order_code'            => '',
            ];
            //没有登录不进行判断
            if($this->user_id) {

                $capActivity = config('cap_activity');
                foreach ($capActivity as $key => $item) {
                    $capCommodityIdArr = array_column($item['data'], 'commodity_id');
                    // 当前商品在活动中
                    $path = $item['path'];
                    if (in_array($goods_data['commodity_id'], $capCommodityIdArr)) {
                        $cap_info['is_activity_commodity'] = 1;
                        $capCardIdArr = array_column($item['data'], 'quick_win_card_id', 'commodity_id');
                        $capCardId    = $capCardIdArr[$goods_data['commodity_id']] ?? '';
                        $result       = CarLive::create('car_live')->getCapUser($this->user['member_id'], $goods_data['commodity_id'], $capCardId, $path);
                        $is_not_cart = 1; // 不能加入购物车
                        $hide_numer  = 1; // 不能修改数量
                        if (empty($result)) {
                            $cap_info['is_activity_user'] = 0;
                            $canot_buy = ['status' => 1, 'word' => '立即购买']; //不能购买文案
                        } else {
                            $cap_info['is_activity_user'] = 1;
                            // 查询当前用户是否下单这个商品
                            $map                   = [
                                'b.user_id'      => $this->user_id,
                                'a.commodity_id' => $goods_data['commodity_id'],
                                'b.order_status' => ['not in', [3, 18]]
                            ];
                            $field                 = 'b.id, b.order_code';
                            $order_commodity_model = new BuOrderCommodity();
                            $order_info            = $order_commodity_model->alias('a')
                                ->join('t_bu_order b', 'a.order_code=b.order_code')
                                ->where($map)
                                ->field($field)
                                ->find();
                            if (!empty($order_info)) {
                                $canot_buy = ['status' => 1, 'word' => '立即购买']; //不能购买文案

                                $cap_info['order_id']   = $order_info['id'];
                                $cap_info['order_code'] = $order_info['order_code'];
                            }
                        }
                        // 是活动商品的话就跳出循环
                        break ;
                    }
                }
            }

//            $capActivity0 = $capActivity[0];
//            $capActivity1 = $capActivity[1];
//            $capCommodityIdArr = array_column($capActivity, 'commodity_id');
//            $capCommodityIdArr0 = array_column($capActivity0, 'commodity_id');
//            $capCommodityIdArr1 = array_column($capActivity1, 'commodity_id');
//            if (!empty($capActivity)) {
//                //没有登录不进行判断
//                if($this->user_id){
//                    $is_cap_commodity = 0;
//                    $result = [];
//                    if (in_array($goods_data['commodity_id'], $capCommodityIdArr0)) {
//                        $is_cap_commodity = 1;
//                        $capCardIdArr = array_column($capActivity0, 'quick_win_card_id', 'commodity_id');
//                        $capCardId    = $capCardIdArr[$goods_data['commodity_id']] ?? '';
//                        $result       = CarLive::create('car_live')->getCapUser($this->user['member_id'], $goods_data['commodity_id'], $capCardId);
//                    }
//                    if (in_array($goods_data['commodity_id'], $capCommodityIdArr1)) {
//                        $is_cap_commodity = 1;
//                        $capCardIdArr = array_column($capActivity1, 'quick_win_card_id', 'commodity_id');
//                        $capCardId    = $capCardIdArr[$goods_data['commodity_id']] ?? '';
//                        $result       = CarLive::create('car_live')->getCapUser1($this->user['member_id'], $goods_data['commodity_id'], $capCardId);
//                    }
//                    // cap 活动商品才进行判断
//                    if ($is_cap_commodity == 1) {
//                        $is_not_cart = 1; // 不能加入购物车
//                        $hide_numer  = 1; // 不能修改数量
//                        $cap_info['is_activity_commodity'] = 1;
//
//                        if (empty($result)) {
//                            $cap_info['is_activity_user'] = 0;
//                            $canot_buy                    = ['status' => 1, 'word' => '立即购买']; //不能购买文案
//                        } else {
//                            $cap_info['is_activity_user'] = 1;
//                            // 查询当前用户是否下单这个商品
//                            $map                   = [
//                                'b.user_id'      => $this->user_id,
//                                'a.commodity_id' => $goods_data['commodity_id'],
//                                'b.order_status' => ['not in', [3, 18]]
//                            ];
//                            $field                 = 'b.id, b.order_code';
//                            $order_commodity_model = new BuOrderCommodity();
//                            $order_info            = $order_commodity_model->alias('a')
//                                ->join('t_bu_order b', 'a.order_code=b.order_code')
//                                ->where($map)
//                                ->field($field)
//                                ->find();
//                            if (!empty($order_info)) {
//                                $canot_buy = ['status' => 1, 'word' => '立即购买']; //不能购买文案
//
//                                $cap_info['order_id']   = $order_info['id'];
//                                $cap_info['order_code'] = $order_info['order_code'];
//                            }
//                        }
//                    }
//
//                }
//            }

            if($source_special){
                if($gong_hui!=3){
                    $goods_data['trade_union']=1;
                    $order_model =  new BuOrder();

                    $gong_hui_where = ['a.order_status'=>['not in',[1,3,8,18]],'b.source_special'=>['in',$source_special],'a.user_id'=>$this->user_id];

//                    $gong_hui_info =  $gonghui_model->getOneByIds($id,$source_special,1);
//                    $gong_hui_where = ['a.order_status'=>['not in',[1,3,8,18]],'b.source_special'=>['in',$source_special],'a.user_id'=>$this->user_id,'a.created_date'=>['between',[$gong_hui_info['start_at'],$gong_hui_info['end_at']]]];
                    $gong_hui_had = $order_model->getGoodsSum(['where'=>$gong_hui_where]);
                    if($gong_hui_had){
                        $goods_data['trade_union_have']=1;
                    }
                    $ac_gong_hui_model =  new AcHaveTradeList();
                    $ac_have_trade = $ac_gong_hui_model->getByUserSp($user['id'],$source_special);
                    if(!$ac_have_trade){
                        $goods_data['trade_union_no_user']=1;
                    }
                    $ac_phone_list_model =  new AcPhonelist();
                    $have_where = [
                        'phone'=>$ac_have_trade['phone'],
                        'type'=>$source_special,
                    ];
                    $have =  $ac_phone_list_model->getOne(['where'=>$have_where]);
                    if(!$have){
                        $goods_data['trade_union_no_user']=1;

                    }
                }

            }
            if($can_number<1){
                $msing =0;
                if(isset($seckill_info['act_status'])){
                    if($seckill_info['act_status']==2){
                        $msing=1;
                    }
                }
                if(!$msing){
                    $canot_buy = ['status' => 1, 'word' => '已超过限购数量']; //不能购买文案\状态
                }
            }

            $params = [
                'dlr_code' => $dlr_code,
                'dlr_name' => $dlr_name,
                'hide_numer' => $hide_numer,
                'buy_word' => $buy_word, //购买按钮词
                'stop_time' => isset($limit['stop_time']) ? $limit['stop_time'] : '', //购买截至时间
                'canot_buy' => $canot_buy, //不能购买状态跟文案
                'is_not_cart' => $is_not_cart, //不能放入购物车
                //                'can_use_card'     => $can_use_card,//是否能使用卡券
                'integral' => $integral, //积分
                //                'xn_kf'            => $xn_kf,//小能客服配置
                'c_service' => $c_service, //客服ID
                'qc_c_service' => $qc_c_service, //启辰客服
                'x_service_no_url' => $x_service_no_url, //小i小程序原生卡夫参数
                'car_info' => $car_info, //车主信息
                'json_data' => $json_data, //sku_list等数据
                'sp_list' => $sp_list, //规格列表
                'suit' => $suit_result, //套装
                'sku_list_group' => $sku_list_group,
                'group_order_code' => $group_order_code,
                'group_info' => $group_info,
                'sp_list_group' => $sp_list_group,
                'group_id' => $group_id,
                'group_act_list' => $group_act_list,
                'can_number' => min($can_number, $wi_can_number),
                'wi_can_number' => $wi_can_number,
                'sku_image' => $sku_image,
                'goods' => $goods_data,
                'full_list' => $full_list,
                'full_wi_list' => $full_wi_list,
                'n_dis_info' => $n_dis_info, //N件N折优惠信息
                'limit_info' => $limit_info, //限时优惠信息
                'seckill_info' => $seckill_info, // 秒杀信息
                'limit_wi_info' => $limit_wi_info, // 限时优惠信息-工时
                'g_info' => $g_info, //团购信息
                'crowdfund_info' => $crowdfund_info, //团购信息
                'card_list' => $card_list,
                'shop_cart_count' => $shop_cart_count,
                'pre_sale' => $pre_sale,
                'act_code' => "commodity-" . $this->channel_type . '-' . $id . '-' . time(), //2021-05-11 10:30:47 可领多张LTY
                'comment' => $comment,
                'comment_count' => $comment_count,
                'ap_time' => $time,
                'limit_dis_enents' => $limit_dis_enents, //限时购预告
                'seckill_dis_enents' => $seckill_dis_enents, //限时购预告
                'gift_info' => $gift_info, //买赠
                'user' => $this->user,
                'qr_code' => $qr_code,
                // 组合商品规格联动
                'is_sp_associated' => $is_sp_associated,
                'sp_associated_data' => $sp_associated_data,
                'des_info' => $des_type_data ?? '',
                'poster_show_type' => $goods_data['poster_show_type'],
                'poster_list' => $poster_list ?? [],
                'h_card_list' => $h_card_list ?? [],
                'cap_info' => $cap_info
            ];
            Logger::error('goodsdetailruntime', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $user['id'] . '-' . $this->channel_type . '-' . $id);

            return $this->re_msg($params);
        }
    }

    protected function commodityAssociateFilter($commodity_set_id, $sub_commodity_list_arr, $is_associated_price = false)
    {
        // $commodity_set_id 查询 $sp_associated_data
        $spec_union_model = new DbCommoditySpecUnion();
        $sp_associated_data = $spec_union_model->getList(['where' => ['group_commodity_set_id' => $commodity_set_id]]);
        // 单规格商品，满足条件：sp_value_id == sp_value_list
        $temp_sub_commodities = [];
        $sp_sku_ids = [];
        foreach ($sub_commodity_list_arr as $sub_commodity) {
            $temp_sub_commodities[$sub_commodity['group_sub_commodity_id']]['set_sku'][$sub_commodity['sp_value_list']] = $sub_commodity;
            $sp_sku_ids[$sub_commodity['group_sub_commodity_id']][$sub_commodity['sp_value_list']][] = $sub_commodity['set_sku_id'];
        }

        $sp_associated_items = [];
        $disabled_set_sku_ids = [];
        $sub_commodity_associated_min_price = [];

        foreach ($sp_associated_data as $item) {
            // 子商品剩余规格中，有设置规格联动。若规格联动的2个规格值，被车型过滤了一个，则2个商品的2个规格值都不返回，过滤。
            if (!isset($temp_sub_commodities[$item['sub_commodity_id']]['set_sku'][$item['sp_value_id']]) ||
                !isset($temp_sub_commodities[$item['assoc_sub_commodity_id']]['set_sku'][$item['assoc_sp_value_id']])) {

                isset($temp_sub_commodities[$item['sub_commodity_id']]['set_sku'][$item['sp_value_id']])
                && array_push($disabled_set_sku_ids, ...$sp_sku_ids[$item['sub_commodity_id']][$item['sp_value_id']]);
                isset($temp_sub_commodities[$item['assoc_sub_commodity_id']]['set_sku'][$item['assoc_sp_value_id']])
                && array_push($disabled_set_sku_ids, ...$sp_sku_ids[$item['assoc_sub_commodity_id']][$item['assoc_sp_value_id']]);
            } else {

                $sub_commodity = $temp_sub_commodities[$item['sub_commodity_id']]['set_sku'][$item['sp_value_id']];
                $assoc_sub_commodity = $temp_sub_commodities[$item['assoc_sub_commodity_id']]['set_sku'][$item['assoc_sp_value_id']];

                // 计算两个商品所有联动规格的组合后，最低价格
                if (!isset($sub_commodity_associated_min_price[$item['sub_commodity_id']]) ||
                    $sub_commodity_associated_min_price[$item['sub_commodity_id']]['sum_price'] > ($sub_commodity['price'] + $assoc_sub_commodity['price'])) {
                    $sub_commodity_associated_min_price[$item['sub_commodity_id']]['sub_commodity_id'] = $item['sub_commodity_id'];
                    $sub_commodity_associated_min_price[$item['sub_commodity_id']]['assoc_sub_commodity_id'] = $item['assoc_sub_commodity_id'];
                    $sub_commodity_associated_min_price[$item['sub_commodity_id']]['a_sp_value_id'] = $item['sp_value_id'];
                    $sub_commodity_associated_min_price[$item['sub_commodity_id']]['b_sp_value_id'] = $item['assoc_sp_value_id'];
                    $sub_commodity_associated_min_price[$item['sub_commodity_id']]['sum_price'] = ($sub_commodity['price'] + $assoc_sub_commodity['price']);
                }
            }

            $sp_associated_items[$item['sub_commodity_id']]['sub_commodity_id'] = $item['sub_commodity_id'];
            $sp_associated_items[$item['sub_commodity_id']]['assoc_sub_commodity_id'] = $item['assoc_sub_commodity_id'];
            $sp_associated_items[$item['sub_commodity_id']]['sp_value_ids'][] = $item['sp_value_id'];
            $sp_associated_items[$item['sub_commodity_id']]['assoc_sp_value_ids'][] = $item['assoc_sp_value_id'];
        }

        $sub_commodity_list_arr = array_filter($sub_commodity_list_arr, function ($sub_commodity) use ($disabled_set_sku_ids) {
            return !in_array($sub_commodity['set_sku_id'], $disabled_set_sku_ids);
        });

        // 子商品剩余规格中，规格联动的商品1个存在无关联关系的规格，1个都是关联关系的规格，则须过滤掉无关联关系的规格。
        foreach ($sp_associated_items as $associated_item) {
            if (empty($temp_sub_commodities[$associated_item['sub_commodity_id']]) || empty($temp_sub_commodities[$associated_item['assoc_sub_commodity_id']])) {
                continue;
            }
            $set_sku_sp_val_ids = array_keys($temp_sub_commodities[$associated_item['sub_commodity_id']]['set_sku']);
            $assoc_set_sku_sp_val_ids = array_keys($temp_sub_commodities[$associated_item['assoc_sub_commodity_id']]['set_sku']);

            $conf_set_sku_sp_val_ids = $sp_associated_items[$associated_item['sub_commodity_id']]['sp_value_ids'];
            $assoc_conf_set_sku_sp_val_ids = $sp_associated_items[$associated_item['sub_commodity_id']]['assoc_sp_value_ids'];

            $diff_ids = [];
            // 两个子商品的规格相对配置的联动规格，计算差值
            $a_diff_ids = array_diff($set_sku_sp_val_ids, $conf_set_sku_sp_val_ids);
            $b_diff_ids = array_diff($assoc_set_sku_sp_val_ids, $assoc_conf_set_sku_sp_val_ids);

            if (count($a_diff_ids) > 0 && count($b_diff_ids) == 0) {
                $diff_ids = $a_diff_ids;
            }
            if (count($a_diff_ids) == 0 && count($b_diff_ids) > 0) {
                $diff_ids = $b_diff_ids;
            }

            // 过滤无关联关系的规格
            $diff_ids && $sub_commodity_list_arr = array_filter($sub_commodity_list_arr, function ($sub_commodity) use ($diff_ids, $associated_item) {
                if (in_array($sub_commodity['group_sub_commodity_id'], [$associated_item['sub_commodity_id'], $associated_item['assoc_sub_commodity_id']])) {
                    if (in_array($sub_commodity['sp_value_list'], $diff_ids)) {
                        return false;
                    }
                }
                return true;
            });
        }
        $sub_sku_id_arr =[];
        foreach ($sub_commodity_list_arr as $v){
            $sub_sku_id_arr[$v['group_sub_commodity_id']][]=$v['set_sku_id'];
        }
        foreach ($sub_commodity_list_arr as $k=>$v){
            $sub_commodity_list_arr[$k]['all_sku_ids']=$sub_sku_id_arr[$v['group_sub_commodity_id']];
        }

        if ($is_associated_price) {
            // 只返回组合最低价格的规格联动组合
            foreach ($sub_commodity_associated_min_price as $sub_commodity_assoc) {
                $sub_commodity_list_arr = array_filter($sub_commodity_list_arr, function ($sub_commodity) use ($sub_commodity_assoc) {
                    return !in_array($sub_commodity['group_sub_commodity_id'], [$sub_commodity_assoc['sub_commodity_id'], $sub_commodity_assoc['assoc_sub_commodity_id']]);
                });
                $sub_commodity_list_arr[] = $temp_sub_commodities[$sub_commodity_assoc['sub_commodity_id']]['set_sku'][$sub_commodity_assoc['a_sp_value_id']];
                $sub_commodity_list_arr[] = $temp_sub_commodities[$sub_commodity_assoc['assoc_sub_commodity_id']]['set_sku'][$sub_commodity_assoc['b_sp_value_id']];
            }
        }
        return ['sub_commodity_list_arr' => $sub_commodity_list_arr, 'sp_associated_data' => $sp_associated_data];
    }

    public function suit($requestData, $user, $channel_type, $brand = '')
    {
        $suit_arr = $this->suitList($requestData, $user, $channel_type, $brand);
        return $suit_arr;
        //直接用下面方法就可以了;
        $this->user = $user;
        $this->channel_type = $channel_type;
        $this->user_id = $user['id'];
        $this->unionid = $user['bind_unionid'];
        $id = $requestData['goods_id']; //商品ID

        $prefix_key = md5(serialize($requestData));

        $spec_arr = redis(config('cache_prefix.spec_value'));
        if (empty($spec_arr)) {
            $spec_arr = [];
            $spec_model = new DbSpecValue();
            $spec = $spec_model->getList();
            foreach ($spec as $v) {
                $spec_arr[$v['id']] = $v['sp_value_name'];
            }
            redis(config('cache_prefix.spec_value'), $spec_arr, 86400);
        }

        $flat_key = $prefix_key . '-id-' . $id . $this->channel_type;
        $flat_info = redis($flat_key);
        if (empty($flat_info) || getRedisLock($flat_key . '-lock', 4)) {
            $flat_model = new DbCommodityFlat();
            $flat_where = ['commodity_id' => $id]; //shelves_type上架渠道5ni，6pz , 'shelves_type' => 5
            $flat_where[] = ['exp', " (find_in_set('{$this->channel_type}',up_down_channel_dlr)) "];
            $flat_info = $flat_model->getOne(['where' => $flat_where]);
            redis($prefix_key . '-id-' . $id . $this->channel_type, $flat_info, 6);
        }

        $suit_arr = [];
        if ($flat_info) {

            $cache_key = md5($prefix_key . $flat_info['cheap_dis'] . $flat_info['last_updated_date']) . $channel_type;
            $suit_arr = redis($cache_key);

            if (empty($suit_arr) || getRedisLock($cache_key . '-lock', mt_rand(600, 1200))) {
                $suit_arr = [];
                $suit_model = new BuCheapSuitIndex();
                if ($flat_info['cheap_dis']) {
                    $cheap_arr = json_decode($flat_info['cheap_dis'], true);
                    //                var_dump($flat_info['cheap_dis']);die();
                    $suit_ids = isset($cheap_arr[$this->channel_type]) ? $cheap_arr[$this->channel_type] : '';
                    $limit_goods_model = new DbLimitDiscountCommodity();
                    if ($suit_ids) {
                        $where = ["a.id" => ['in', $suit_ids], 'd.stock' => ['>', 0], 'g.count_stock' => ['>', 0]];
                        $where[] = ["exp", " (find_in_set('{$this->channel_type}',flat.up_down_channel_dlr)) "];
                        $suit_list = $suit_model->alias('a')->join("t_bu_cheap_suit_sub b", "a.id=b.index_id")->join("t_db_commodity_flat flat", "flat.commodity_set_id=b.commodity_set_id")->join(" t_db_commodity_set_sku d ", " b.sku = d.id")->join(" t_db_commodity_sku e ", "d.commodity_sku_id = e.id ")->join("t_db_commodity_set g ", " g.id = b.commodity_set_id")->where($where)->field("a.name,b.index_id,b.commodity_id,b.sku,b.price,flat.cover_image,flat.commodity_name,e.sp_value_list,b.commodity_set_id,d.price old_price,d.stock,g.count_stock,flat.limit_dis,g.is_mail,g.is_store,g.mail_show_word,g.mail_type")->group('b.index_id,b.commodity_id')->select();
                        //                        echo $suit_model->getLastSql();die();
                        if ($suit_list) {
                            foreach ($suit_list as $k => $v) {
                                $sp_name = '';
                                if ($v['limit_dis']) {
                                    if (isset($v['limit_dis'][$this->channel_type])) {
                                        $limit_info = $limit_goods_model->getOne(['where' => ['limit_discount_id' => $v['limit_dis'][$this->channel_type], 'commodity_set_id' => $v['commodity_set_id']]]);
                                        if ($limit_info) {
                                            $sku_price_arr = json_decode($limit_info['sku_price'], true);
                                            $suit_list[$k]['old_price'] = $v['old_price'] = $sku_price_arr[$v['sku']];
                                        }
                                    }
                                }
                                if ($v['sp_value_list']) {
                                    $sp_list = explode(',', $v['sp_value_list']);
                                    foreach ($sp_list as $vvv) {
                                        $sp_name .= $spec_arr[$vvv] . ',';
                                    }
                                    $v['sp_name'] = trim($sp_name, ',');
                                } else {
                                    $v['sp_name'] = '';
                                }
                                //mail_method 1到店2快递3都有--变成后台配置了
                                if ($v['is_mail'] == 1 && $v['is_store'] == 1) {
                                    $v['mail_method'] = 3;
                                } elseif ($v['is_mail'] == 1 && $v['is_store'] == 0) {
                                    $v['mail_method'] = 2;
                                } else {
                                    $v['mail_method'] = 1;
                                }
                                if ($v['mail_type']) {
                                    $v['mail_method'] = $v['mail_type'];
                                }
                                //                            $v['sp_name'] = $suit_list[$k]['sp_name'];

                                $suit_arr[$v['index_id']]['list'][] = $v;
                                $suit_arr[$v['index_id']]['suit_id'] = $v['index_id'];
                                $suit_arr[$v['index_id']]['suit_name'] = $v['name'];
                            }
                        }
                    }
                }
                $suit_arr = array_values(empty($suit_arr) ? [] : $suit_arr);
                redis($cache_key, $suit_arr, mt_rand(3600, 7200));
            }
        }
        return $this->re_msg($suit_arr);
    }


    /**
     * 换算分成金额
     * @param int $divided_into
     * @param int $install_fee
     */
    public static function getDividedrice($price = 0, $divided_into = 0, $install_fee = 0)
    {
        $value = ($price - $install_fee) * ($divided_into / 100);
        return round($value, 2);
    }


    //获取分类ID下所有子类ID
    private function _goods_type_arr($type_id, $type = 0)
    {
        $type_level = 3;
        if ($type == 1) {
            $goods_type_key = 'cache_goods_type_arr' . $this->channel_type . $type_id;
            $return = redis($goods_type_key);

            $type_model = new DbHomeType();
            $model = new DbHomeTypeCommodity();
            $type_info = $type_model->getOneByPk($type_id);
            if($type_info['category_level']==2){
                $three_type = $type_model->getList(['where'=>['comm_parent_id'=>$type_id,'is_enable'=>1]]);
                $three_type_id_arr = array_column($three_type,'id');
            }
            if ($type_info['category_level'] == $type_level) {
                $three_type_id_arr = [$type_id];
            }
            if($three_type_id_arr){
                $res = $model->where(['new_comm_type_id' => ['in',$three_type_id_arr], 'is_enable' => 1])
                    ->field('old_comm_type_id,comm_id')->order('sort')->select();
                $com_type_id = [];
                foreach ($res as $val) {
                    if ($val['old_comm_type_id'] != 0) {
                        $commodity_id = Db::name('db_commodity_flat')
                            ->where(['comm_type_id' => $val['old_comm_type_id'],
                                'up_down_channel_dlr' => array('like', "%" . $this->channel_type . "%")])
                            ->order("last_updated_date asc")
                            ->column('commodity_id');
                        $com_type_id = array_merge($com_type_id, $commodity_id);
                    }
                    if ($val['comm_id'] != 0) {
                        array_push($com_type_id, $val['comm_id']);
                    }
                }
                redis($goods_type_key, $com_type_id, 80);
                return $com_type_id;
            }

            return $return;
        } else {
            $goods_type_key = 'cache_goods_type_arr' . $type_id;
            $return = redis($goods_type_key);

            if (empty($return) || getRedisLock($goods_type_key . '-lock', 60)) {
                $type_model = new DbCommodityType();
                $type_info = $type_model->getOneByPk($type_id);
                if ($type_info['level'] == $type_level) {
                    redis($goods_type_key, $type_id, 80);
                    return $type_id;
                }
                if ($type_level - $type_info['level'] == 1) {
                    $type_two_info = $type_model->getColumn(['where' => ['comm_parent_id' => $type_id], "column" => "id"]);
                    redis($goods_type_key, $type_two_info, 80);
                    return array_values($type_two_info);
                }
                if ($type_level - $type_info['level'] == 2) {
                    $type_3_info = $type_model->alias("a")->join("t_db_commodity_type b", "a.id=b.comm_parent_id")->join("t_db_commodity_type c", "b.id=c.comm_parent_id")->where(['a.id' => $type_id])->column("c.id");
                    redis($goods_type_key, $type_3_info, 80);
                    return array_values($type_3_info);
                }
            }
        }


        return $return;
    }

    /**
     * 获取小I客服
     * @param $user
     * @param $channel_type
     */
    public function XiKf($user, $channel_type)
    {
        $this->user = $user;
        $this->channel_type = $channel_type;
        $user_id = $user['id'] ?? '';
        $unionid = $user['bind_unionid'] ?? '';
        $member_id = $user['member_id'] ?? '';
        $one_id = $user['one_id'] ?? '';
        $car_info = $this->_getCarer($unionid, $member_id, $one_id, $this->channel_type);

        if ($car_info) {
            $c_nickname = sprintf("%s-%s.%s", $car_info['name'], $car_info['vin'], $car_info['car_series_name'] . $user_id);
        } else {
            $c_nickname = "访客" . $user_id;
        }
        if (in_array($channel_type, ['QCSM', 'QCAPP'])) {
            if ($channel_type == 'QCAPP') {
                $custome = 'qchysc_app';
                $platform = 'web';
                $channel_type_name = '启辰APP';
            } else {
                $custome = 'qchysc_xcx';
                $platform = 'weixin';
                $channel_type_name = '启辰小程序';
            }
            $con_url = config('xi_kf_qc_url');
            $type_name = "nissan-h5-human.html?aiccCustomInfo=" . urlencode(sprintf('{"userId":"%s","guestNickname":"%s","hysc_good_id":"%s","hysc_channel":"%s","hysc_unionid":"%s","brand_type":"qc"}', $user_id, $c_nickname, 000, $channel_type, $member_id)) . "&userDims=" . urlencode(sprintf('{"platform":"%s","channel":"pro_","channelName":"%s商城","custome":"%s"}', $platform, $channel_type_name, $custome));
        } else {
            $con_url = config('xi_kf_url');
            $type_name = sprintf("nissan-h5-human.html?hysc_good_id=%s&hysc_channel=%s&hysc_unionid=%s&hysc_guestNickname=%s", '000', $this->channel_type, $member_id, $c_nickname); //

        }
        $c_service = $con_url . $type_name; //
        $x_service_no_url = sprintf("hysc_good_id=%s&hysc_channel=%s&hysc_unionid=%s&hysc_guestNickname=%s&hysc_comeFrom=rchysc", '000', $this->channel_type, $member_id, urlencode($c_nickname)); //
        $data = ['h5' => $c_service, 'sm' => $x_service_no_url];
        return $this->re_msg($data);
    }

    //启辰积分兑换专区商品详情
    public function qc_integral_detail($requestData, $user, $channel_type, $brand = 1)
    {
        $this->user = $user;
        $this->brand = $brand;
        $this->channel_type = $channel_type;
        $this->user_id = $user['id'];
        $this->unionid = $user['bind_unionid'];
        $id = $requestData['goods_id'];
        $lng = $requestData['lng'] ?? ''; //经度
        $lat = $requestData['lat'] ?? ''; //纬度
        $channel_type = $this->channel_type; //渠道来源 1官网 2小程序
        $dlr_model = new DbDlr();
        $goods = $this->getCommodityInfo($id, $channel_type, 0, 0, '', 3, 0, $this->user);
        $time = date('Y-m-d H:i:s');
        //        var_dump($goods);die();
        if (!$goods) {
            return $this->re_msg('商品已下架', 403);
        } else {
            $goods_data = $goods['commodity_data'];

            //4.判断是否有限时折扣
            $limit_info = [];
            $limit = [];
            $can_number = 999; //之前是999
            //////////////////组合商品开始//////////////////////
            $e3sParePartTimeObj = new E3sParePartTime();
            $work_model = new E3sPartCarSeries();
            $dbCommodityObj = new DbCommodity();
            //1为组合商品，0为非组合商品
            $is_grouped_flag = $goods['commodity_data']['is_grouped'];
            $groups_data = [];
            if ($is_grouped_flag == 1) {
                $commoditySet = new DbCommoditySet();
                $dbCommoditySetSku = new DbCommoditySetSku();
                $dbSpecValueObj = new DbSpecValue();
                $commodity_set_info = $commoditySet->where(['commodity_id' => $id, 'is_enable' => 1])->find();
                //                print_json($commoditySet->getLastSql());
                //                echo $commodity_set_info['group_commodity_ids_info'];die();
                $commodity_set_list = json_decode($commodity_set_info['group_commodity_ids_info'], true);
                $sub_commodity_list_arr = $this->set_sku_model->getSubCommidtyList($id, $this->user['car_series_id']);
                //                echo $this->set_sku_model->getLastSql();die();
                $sku_cb_list_tmp = [];
                $sku_cb_list = [];
                $sub_com_min_price = 0;
                //                dd($commodity_set_info['group_commodity_ids_info']);
                $com_set_sub_c_id = [];
                $com_list_sub_c_id = [];
                foreach ($commodity_set_list as $cc_v) {
                    $sub_com_min_price += min(array_column($cc_v['sku_list'], 'price'));
                    $sub_com_set_sku_id = array_column($cc_v['sku_list'], 'group_sub_set_sku_id');
                    $com_set_sub_c_id[] = $cc_v['commodity_id'];
                    $sub_com_info = $this->set_sku_model->getList(['where' => ['id' => ['in', $sub_com_set_sku_id], 'stock' => ['>', 0]]]);

                    if (!$sub_com_info) {
                        $sub_commodity_list_arr = []; //有子商品无库存的，整个商品下架
                        break;
                    }
                }
                if ($goods_data['is_mate'] == 2) {
                    $sub_commodity_list_arr = [];
                }
                $sub_commodity_list = [];

                if ($sub_commodity_list_arr) {
                    foreach ($sub_commodity_list_arr as $arr_v) {

                        if ($arr_v['relate_car_ids']) {
                            if (!$this->user['car_series_id']) {
                                $goods_data['is_mate'] = 2;
                                $sub_commodity_list_arr = [];
                                break;
                            }
                        }
                        $com_list_sub_c_id[] = $arr_v['group_sub_commodity_id'];
                        $sub_commodity_list[$arr_v['group_sub_commodity_id']] = $arr_v;
                    }
                }

                //                if($sub_commodity_list_arr){
                //                    if(array_diff(array_unique($com_set_sub_c_id),array_unique($com_list_sub_c_id))){
                //                        $goods_data['is_mate']=0;
                //                        $sub_commodity_list_arr=[];
                //                        Logger::error('goodsNotAll'.$id);
                //                    }
                //                }
                if (!$sub_commodity_list_arr) {
                    if (!$this->user['car_series_id']) {
                        $goods_data['is_mate'] = 2;
                    } else {
                        $goods_data['is_mate'] = 0;
                    }
                    $goods_data['discount_price_range_end'] = $goods_data['original_price_range_start'] = $goods_data['original_price_range_end'] = $goods_data['discount_price_range_start'] = sprintf("%.2f", $sub_com_min_price);

                    if ($limit_info) {

                        if (in_array($limit_info['discount_type'], [1, 3])) {
                            if ($limit_info['dis_type'] == 1) {
                                $limit_dis = $limit_info['discount'] / 10;
                                //限时购更改价格
                                $limit_sub_price = round($sub_com_min_price * $limit_dis, 2); //商品优惠金额
                            } else {
                                $limit_sub_price = $sub_com_min_price['final_price'] - $limit_info['discount'];
                            }
                        }
                        $goods_data['limit_original_price_range_start'] = $goods_data['limit_original_price_range_end'] = sprintf("%.2f", $sub_com_min_price);
                        $goods_data['limit_discount_price_range_start'] = $goods_data['limit_discount_price_range_end'] = $limit_sub_price;
                    }
                } else {
                    //
                    //                    foreach ($sub_commodity_list_arr as $arr_v){
                    //                        if($arr_v['relate_car_ids']){
                    //                            if(!$this->user['car_series_id']){
                    //                                $goods_data['is_mate']=2;
                    //                            }
                    //                        }
                    //                        $sub_commodity_list[$arr_v['group_sub_commodity_id']]=$arr_v;
                    //                    }
                    $sort_k = [];
                    foreach ($sub_commodity_list as $k => $item) {
                        foreach ($commodity_set_list as $s_k => $setitem) {
                            if ($setitem['commodity_id'] == $item['group_sub_commodity_id']) { //是组合子商品
                                $sort_k[$k] = $s_k;
                                $sub_commodity_list[$k]['is_mate'] = 1;
                                $sub_commodity_list[$k]['relate_car'] = 0;
                                if ($item['relate_car_ids']) {
                                    $sub_commodity_list[$k]['relate_car'] = 1;
                                    if (!$this->user['car_series_id']) {
                                        $sub_commodity_list[$k]['is_mate'] = 2;
                                    }
                                }
                                if (!$item['image']) {
                                    if (empty($item['activity_image'])) {
                                        $sub_commodity_list[$k]['image'] = $item['cover_image'];
                                    } else {
                                        $sub_commodity_list[$k]['image'] = $item['activity_image'];
                                    }
                                }
                                $sub_commodity_list[$k]['initial_num'] = $setitem['initial_num'];
                                $sub_commodity_list[$k]['user_can_des'] = $setitem['user_can_des'];
                                $sub_commodity_list[$k]['can_select'] = $setitem['can_select']; //can_select 为1的时候可以取消勾选
                                //                            if($setitem['machine_oil_type'] > 0){ //机油商品
                                //                                //此处为 6L 的车
                                //                                if($setitem['machine_oil_type'] == 1){
                                //                                    $sub_commodity_list[$k]['initial_num'] = 2;
                                //                                }
                                //                            }
                                $sub_commodity_list[$k]['work_hour_free'] = 0;
                                $sub_commodity_list[$k]['work_time_code'] = '';
                                $sub_commodity_list[$k]['work_time_number'] = 0;
                                $sub_commodity_list[$k]['work_time_price'] = 0;

                                if (!empty($item['relate_car_work_hour'])) {
                                    //获取工时编码
                                    $relate_car_work_hour = json_decode($item['relate_car_work_hour'], true);

                                    foreach ($relate_car_work_hour as $ki => $itemhous) {
                                        if (in_array($this->user['car_18n'], $itemhous)) {
                                            $sub_commodity_list[$k]['work_time_code'] = $ki;
                                            $part_time_info = $work_model->getOneByPk($ki);
                                            if ($goods_data['work_hour_type']) {
                                                $sub_commodity_list[$k]['work_time_number'] = $part_time_info['wi_qty'];
                                            }
                                            $sub_commodity_list[$k]['work_time_code'] = $part_time_info['wi_code'];
                                            //获取工时价格
                                            //                                        $widata['dlr_code'] = $dlr_code;
                                            //                                        $widata['car_config_code'] = $this->user['car_18n'];
                                            //                                        $widata['repair_type'] = $item['work_hour_type'];
                                            //                                        $price_ret = $dbCommoditySetSku->getE3sPrice($widata);
                                            $sub_commodity_list[$k]['work_time_price'] = $goods_data['work_time_price'];

                                            break;
                                        }
                                    }
                                }
                                if ($setitem['machine_oil_type'] == 1) { //机油商品
                                    //此处为 6L 的车  油形要跟着车走--暂时还没有做，等VV确定
                                    $sub_commodity_list[$k]['initial_num'] = $user['18_oil_type'] - 4;
                                    //1L工时都==0
                                    $sub_commodity_list[$k]['work_hour_free'] = 0;
                                    $sub_commodity_list[$k]['work_time_code'] = '';
                                    $sub_commodity_list[$k]['work_time_number'] = 0;
                                    $sub_commodity_list[$k]['work_time_price'] = 0;
                                }
                                unset($sub_commodity_list[$k]['relate_car_work_hour']);
                            }
                        }
                        if ($item['sp_value_list']) {
                            $sp_value_list = $this->sp_model->getAllList(['a.id' => ['in', $item['sp_value_list']]]);
                            $sku_value = '';
                            if ($sp_value_list) {
                                foreach ($sp_value_list as $vv) {
                                    $sku_value .= $vv['sp_name'] . ":" . $vv['sp_value_name'] . ", ";
                                }
                                $sub_commodity_list[$k]['sp_value_name'] = trim($sku_value, ', ');
                            }
                        }
                    }
                }
                if ($sub_commodity_list) {
                    //                    print_json($commodity_set_list);
                    array_multisort($sort_k, SORT_ASC, $sub_commodity_list); //重新排序
                    foreach ($sub_commodity_list as $k => $v) {
                        if ($v['initial_num'] == 0) {
                            unset($sub_commodity_list[$k]);
                        }
                        unset($sub_commodity_list[$k]['relate_car_ids']);
                    }
                    //总价
                    $groups_data['sub_commodity_list'] = array_values($sub_commodity_list);
                }
            }

            $goods_data['groups_data'] = $groups_data;
            ////////////////////////组合商品结束//////////////////////////

            $canot_buy = ['status' => 0, 'word' => '']; //不能购买文案\状态
            $hide_numer = 0;
            $buy_word = "立即购买";
            $car_info = $this->_getCarer($this->user['bind_unionid'], $this->user['member_id'], $this->user['one_id'], $this->channel_type);
            //            var_dump($car_info);die();

            //获取用户基本信息
            $userInfo = $this->getFriendBaseInfo($user);
            $car_info['brand'] = $userInfo['brand'] ?? '';
            $car_info['have_brand_car'] = $userInfo['have_brand_car'] ?? 0;

            $is_not_cart = 1; //不能放入购物车 0可以1 不可以

            //积分兑换专享,只可以兑换一次
            $integral_describe = [];
            $goods_data = $goods['commodity_data'];

            $old_price = 0;
            if ($goods['commodity_data']['is_integral_shop'] == 1) {
                if (!$car_info) {
                    return $this->re_msg('需要车主才能购买', 402);
                }
                $hide_numer = 1;
                $buy_word = "立即兑换";
                $is_not_cart = 1;
                $integ_res = Db::name('db_commodity')->alias('a')
                    ->join('t_db_commodity_sku b', 'a.id = b.commodity_id')
                    ->join('t_db_commodity_set c', 'c.commodity_id = a.id')
                    ->join('t_db_commodity_set_sku d', 'd.commodity_sku_id = b.id')
                    ->where(['b.is_enable' => 1, 'd.is_enable' => 1, 'a.id' => $id])
                    ->field('b.sp_value_list,d.price,b.price old_price')
                    ->order('d.price desc')
                    ->select();
                foreach ($integ_res as $val) {
                    if (strstr($val['sp_value_list'], '3930')) {
                        $integral_describe['jk'] = $val['price'];
                        if (in_array($car_info['card_degree_name'], ['会员金卡', '会员金卡(VIP)', '会员金卡(启辰VIP)', '会员金卡(启辰)'])) {
                            $old_price = $val['old_price'];
                        }
                    }
                    if (strstr($val['sp_value_list'], '3931')) {
                        $integral_describe['yk'] = $val['price'];
                        if (in_array($car_info['card_degree_name'], ['会员银卡', '会员银卡VIP', '会员银卡(启辰)'])) {
                            $old_price = $val['old_price'];
                        }
                    }
                    if (strstr($val['sp_value_list'], '3932')) {
                        $integral_describe['pk'] = $val['price'];
                        if (in_array($car_info['card_degree_name'], ['会员普卡', '会员普卡(启辰)', '员工卡(启辰)', '员工卡'])) {
                            $old_price = $val['old_price'];
                        }
                    }
                }
            }
            if (in_array($car_info['card_degree_name'], ['会员金卡', '会员金卡(VIP)', '会员金卡(启辰VIP)', '会员金卡(启辰)'])) {
                $goods_data['discount_price_range_start'] = $integral_describe['jk'];
                $goods_data['discount_price_range_end'] = $integral_describe['jk'];
                $goods_data['original_price_range_start'] = number_format($integral_describe['jk'] / 0.6, '1');
                $goods_data['original_price_range_end'] = number_format($integral_describe['jk'] / 0.6, '1');
            }
            if (in_array($car_info['card_degree_name'], ['会员银卡', '会员银卡VIP', '会员银卡(启辰)', '会员银卡(启辰VIP)'])) {
                $goods_data['discount_price_range_start'] = $integral_describe['yk'];
                $goods_data['discount_price_range_end'] = $integral_describe['yk'];
                $goods_data['original_price_range_start'] = number_format($integral_describe['yk'] / 0.7, '1');
                $goods_data['original_price_range_end'] = number_format($integral_describe['yk'] / 0.7, '1');
            }
            if (in_array($car_info['card_degree_name'], ['会员普卡', '会员普卡(启辰)', '员工卡(启辰)', '员工卡'])) {
                $goods_data['discount_price_range_start'] = $integral_describe['pk'];
                $goods_data['discount_price_range_end'] = $integral_describe['pk'];
                $goods_data['original_price_range_start'] = number_format($integral_describe['pk'] / 0.8, '1');
                $goods_data['original_price_range_end'] = number_format($integral_describe['pk'] / 0.8, '1');
            }

            //todo ccs套餐判断用户  不能加购物车
            if ($goods_data['commodity_class'] == 4) {
                if (!$car_info) {
                    return $this->re_msg('需要车主才能购买', 402);
                }
                if (!$car_info['vin']) {
                    return $this->re_msg('需要车主才能购买!', 402);
                }
                $Ccs = new Ccs();
                $ccs_info = $Ccs->page_info($this->unionid, $car_info['vin']);
                if (isset($ccs_info['userIdentity'])) {
                    if (in_array($ccs_info['userIdentity'], [1, 2])) {
                        return $this->re_msg('需要车主才能购买', 402);
                    }
                    if ($ccs_info['userIdentity'] == 4 && in_array($id, $this->sj_ccs())) {
                        $canot_buy['status'] = 1;
                        $canot_buy['word'] = "暂无购买";
                    }
                }
                $is_not_cart = 1;
            }
            //电子卡券/移动积分 不能放入购物车  不能选数量
            if (in_array($goods_data['commodity_class'], [2, 3]) || $goods_data['is_integral_shop'] == 2) {
                $is_not_cart = 1;
                $hide_numer = 1;
            }
            //增加商品分类中文名
            if ($goods_data['comm_type_id']) {
                $goods_type_info = redis('detail_goods_type' . $goods_data['comm_type_id']);
                if (empty($goods_type_info)) {
                    $goods_type_model = new DbCommodityType();
                    $goods_type_info = $goods_type_model->alias('a')->field('a.comm_type_name,b.id comm_type_two_id,b.comm_parent_id comm_type_one_id')->join('t_db_commodity_type b', 'a.comm_parent_id=b.id')->where(['a.id' => $goods_data['comm_type_id'], 'a.is_enable' => 1])->find();
                    redis('detail_goods_type' . $goods_data['comm_type_id'], $goods_type_info, 60);
                }
                $goods_data['commodity_type'] = isset($goods_type_info['comm_type_name']) ? $goods_type_info['comm_type_name'] : '';
                $goods_data['comm_type_two_id'] = isset($goods_type_info['comm_type_two_id']) ? $goods_type_info['comm_type_two_id'] : '';
                $goods_data['comm_type_one_id'] = isset($goods_type_info['comm_type_one_id']) ? $goods_type_info['comm_type_one_id'] : '';
            }

            //mail_method 1到店2快递3都有
            if ($goods_data['is_mail'] == 1 && $goods_data['is_store'] == 1) {
                $goods_data['mail_method'] = 3;
            } elseif ($goods_data['is_mail'] == 1 && $goods_data['is_store'] == 0) {
                $goods_data['mail_method'] = 2; //快递
            } else {
                $goods_data['mail_method'] = 1; //到店
            }
            if ($goods_data['mail_type']) {
                $goods_data['mail_method'] = $goods_data['mail_type'];
            }
            $goods_data['is_card_available'] = 1; //这个字段不做判断了，通过t_db_commodity_card 了，强制可领取


            //5.判断是否N件N折
            $n_dis_info = [];
            if (isset($goods_data['n_dis_id']) && $goods_data['n_dis_id'] > 0) {
                $n_dis_info = $this->_nn($goods_data['commodity_id']);
            }
            $sku_list = $goods['sku_list'];
            $sku_arr = array();
            if ($sku_list) {
                //处理排序
                foreach ($sku_list as $k => $v) {
                    $k_arr = explode(',', $k);
                    sort($k_arr);
                    $k_s = implode(',', $k_arr);
                    $sku_arr[$k_s] = $v;
                }
                $sku_list = $sku_arr;
            }
            //            var_dump($sku_list);
            $sp_list = $goods['sp_list'];
            //处理规则只有一行的
            if (count($sp_list) == 1) {
                foreach ($sp_list as $k => $v) {
                    if ($sp_list[$k]['sp_value_list']) {
                        foreach ($sp_list[$k]['sp_value_list'] as $kk => $vv) {
                            if ($sku_list[$vv['sp_value_id']]['stock'] < 1) {
                                unset($sp_list[$k]['sp_value_list'][$kk]);
                            }
                        }
                        $sp_list[$k]['sp_value_list'] = array_values($sp_list[$k]['sp_value_list']);
                        if (count($sp_list[$k]['sp_value_list']) == 0) {
                            unset($sp_list[$k]);
                            $canot_buy = ['status' => 1, 'word' => '无库存'];
                        }
                    }
                }
            }


            $sku_image = $goods_data['sku_image'];


            //            if ($goods_data['car_series_id']) {
            //                $car_series = explode(',', $goods_data['car_series_id']);
            //                if (!$this->user['car_series_id']) {
            //                    $goods_data['is_mate'] = 0;
            //                } else {
            //                    if (!in_array($this->user['car_series_id'], $car_series)) {
            //                        $goods_data['is_mate'] = 0;
            //                    } else {
            //                        $goods_data['is_mate'] = 1;
            //                    }
            //                }
            //
            //            }

            $user_collection = $this->_checkCollection($id, $this->user_id, $this->brand);
            $goods_data['collected'] = 0;
            if ($user_collection) {
                //已经收藏
                $goods_data['collected'] = 1;
            }


            //            $goods_data['price_t']                    = sprintf("<div class=\"prc\">%s<span id=\"prc-pdt\">%s</span>~%s<span id=\"prc-pdt\">%s</span></div>", $q_qz, $goods_data['discount_price_range_start'] * $bs . $q_hz, $q_qz, $goods_data['discount_price_range_end'] * $bs . $q_hz);
            $goods_data['discount_price_range_start'] = sprintf("%.2f", $goods_data['discount_price_range_start']);
            $goods_data['discount_price_range_end'] = sprintf("%.2f", $goods_data['discount_price_range_end']);
            $goods_data['original_price_range_start'] = sprintf("%.2f", $goods_data['original_price_range_start']);
            $goods_data['original_price_range_end'] = sprintf("%.2f", $goods_data['original_price_range_end']);
            //            $goods_data['original_price_range_end']   = round($goods_data['original_price_range_end']);//可能会没有小数点
            //现价
            if ($goods_data['discount_price_range_start'] == 99999) {
                $goods_data['price'] = "价格待公布";
            }
            $carer_pv_point = isset($car_info['pv_point']) ? $car_info['pv_point'] : 0;
            //添加车主能使用积分，支付方式=3的时候生效
            if ($goods_data['pay_style'] == 3) {
                $goods_data['carer_pv_point'] = $carer_pv_point;
            } else {
                return $this->re_msg('支付方式不正确', 403);
            }
            if (isset($car_info['name'])) {
                $c_nickname = sprintf("%s-%s.%s", $car_info['name'], $car_info['vin'], $car_info['car_series_name'] . $this->user_id);
            } else {
                $c_nickname = "访客" . $this->user_id;
            }
            $c_service = config('xi_kf_url') . sprintf("nissan-h5-human.html?hysc_good_id=%s&hysc_channel=%s&hysc_unionid=%s&hysc_guestNickname=%s", $id, $this->channel_type, $this->channel_type == 'GWSM' ? $this->unionid : $this->user['member_id'], urlencode($c_nickname)); //
            //https://xiaoiqd.dongfeng-nissan.com.cn/static/html-qichenApp/nissan-h5-human.html
            if ($channel_type == 'QCAPP') {
                $type_name = "nissan-h5-human.html?aiccCustomInfo=" . urlencode(sprintf('{"userId":"%s","guestNickname":"%s"}', $this->user_id, $c_nickname)) . "&userDims=" . urlencode('{"platform":"web","channel":"pro_","channelName":"启辰APP商城","custome":"qchysc_app"}');
            } else {
                $type_name = "nissan-h5-human.html?aiccCustomInfo=" . urlencode(sprintf('{"userId":"%s","guestNickname":"%s"}', $this->user_id, $c_nickname)) . "&userDims=" . urlencode('{"platform":"web","channel":"pro_","channelName":"启辰小程序商城","custome":"qchysc_xcx"}');
            }

            $qc_c_service = config('xi_kf_qc_url') . $type_name; //
            $x_service_no_url = sprintf("hysc_good_id=%s&hysc_channel=%s&hysc_unionid=%s&hysc_guestNickname=%s&hysc_comeFrom=rchysc", $id, $this->channel_type, $this->channel_type == 'GWSM' ? $this->unionid : $this->user['member_id'], urlencode($c_nickname)); //

            if ($goods_data['count_stock'] < 1) {
                $canot_buy['status'] = 1;
                $canot_buy['word'] = "已售罄";
            }
            $json_data = json_encode_cn(array('sku_list' => $sku_list, 'sku_image' => $sku_image));
            redis('net-api-goods-sku-json-is_suit0' . $id . $this->channel_type, $json_data, 3600 * 5);

            $params = [
                'hide_numer' => $hide_numer,
                'buy_word' => $buy_word, //购买按钮词
                'canot_buy' => $canot_buy, //不能购买状态跟文案
                'is_not_cart' => $is_not_cart, //不能放入购物车
                //                'xn_kf'            => $xn_kf,//小能客服配置
                'c_service' => $c_service, //客服ID
                'qc_c_service' => $qc_c_service, //启辰客服
                'x_service_no_url' => $x_service_no_url, //小i小程序原生卡夫参数
                'car_info' => $car_info, //车主信息
                'json_data' => $json_data, //sku_list等数据
                'sp_list' => $sp_list, //规格列表
                'can_number' => $can_number,
                'sku_image' => $sku_image,
                'goods' => $goods_data,
                'ap_time' => $time,
                'user' => $this->user,
                'integral_describe' => $integral_describe,
            ];

            return $this->re_msg($params);
        }
    }

    /**
     * 套餐列表
     * @param $requestData
     * @param $user
     * @param $channel_type
     * @param $brand
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function suitList($requestData, $user, $channel_type)
    {
        $suit_ids = $requestData['suit_ids'] ?? '';
        $goods_ids = $requestData['commodity_ids'] ?? '';
        $sku_ids = $requestData['sku_ids'] ?? '';
        $lng = $requestData['lng'] ?? '';
        $lat = $requestData['lat'] ?? '';
        $kilometer = $requestData['kilometer'] ?? '';
        $dd_dlr_code = $requestData['dd_dlr_code'] ?? '';
        $sku_id_arr = [];
        $sku_id_price_arr = [];
        $have_goods_id = '';
        if ($goods_ids) {
            $goods_id_arr = explode(',', $goods_ids);
            $have_goods_id = $goods_id_arr[0];
        }
        $gg_to_sku = [];
        if ($sku_ids) {
            $sku_id_arr = explode(',', $sku_ids);
            foreach ($goods_id_arr as $kk => $gg) {
                $gg_to_sku[$gg] = $sku_id_arr[$kk];
            }
        }
        if (!$suit_ids) {
            //没套餐ID时候通过商品ID去获取，正常都要有套餐ID
            $flat_model = new DbCommodityFlat();
            $flat_where = ['commodity_id' => $goods_ids]; //shelves_type上架渠道5ni，6pz , 'shelves_type' => 5
            $flat_where[] = ['exp', " (find_in_set('{$channel_type}',up_down_channel_dlr)) "];
            $flat_info = $flat_model->getOne(['where' => $flat_where, 'field' => 'cheap_dis']);
            if ($flat_info) {
                if ($flat_info['cheap_dis']) {
                    $cheap_arr = json_decode($flat_info['cheap_dis'], 1);
                    $suit_ids = isset($cheap_arr[$channel_type]) ? $cheap_arr[$channel_type] : '';
                    $goods_ids = '';
                }
            }
        }
        $suit_index_model = new BuCheapSuitIndex();
        $suit = $suit_index_model->getSuitList($goods_ids, $suit_ids, $channel_type);
        $row['need_dlr'] = 0;
        $row['need_18n'] = 0;
        $suit_list = $suit['list'];
        $suit_count = $suit['res_count'];
        if ($suit_list) {
            $goods_list = $suit['goods_list'];
            $suit_sku_list = $suit['sku_list'];
            $goods_count = count($goods_list);
            $goods_params = [
                'commodity_ids' => implode(',', $goods_list),
                'dd_dlr_code' => $dd_dlr_code,
                'sku_ids' => implode(',', $suit_sku_list),//这里传了上架skuid了
                'pageSize' => $goods_count,
                'kilometer' => $kilometer,
                'suit_list' => 1,
            ];
            $set_sku_model = new DbCommoditySetSku();
            $sp_list = $set_sku_model->alias('a')
                ->join('t_db_commodity_sku b', 'a.commodity_sku_id=b.id')
                ->join('t_db_commodity_set c', "c.id=a.commodity_set_id")
                ->where(['a.id' => ['in', $suit_sku_list]])
                ->field('a.id,b.sp_value_list,b.image,a.price,a.stock,b.sku_code,b.relate_car_18n,c.is_store,c.pay_style,c.commodity_id')->select();
            //commodity_id,gc_id,gc_stock,pay_style,is_store,dd_commodity_type
            $goods_list = $this->goodsList($goods_params, $user, $channel_type);
            $user_info = $this->getFriendBaseInfo($user, $lng, $lat);
            $user_vin = $user_info['vin'];
            $row['user_info'] = $user_info;
            $row['user_vin'] = $user_vin;
//            $list_goods_id_arr = [];
            if ($goods_list['code'] == 200) {
                $new_goods_list = $goods_list['msg']['data'];

//                $list_goods_id_arr = array_column($new_goods_list,'commodity_id');
                $row['xy_can_buy'] = $goods_list['msg']['xy_can_buy'];
                $row['wn_can_buy'] = $goods_list['msg']['wn_can_buy'];
                $row['dlr_code'] = $goods_list['msg']['dlr_code'];
                $row['dlr_name'] = $goods_list['msg']['dlr_name'];
                $row['maintain_times'] = $goods_list['msg']['maintain_times'];
            }
            $sku_to_sp = [];
            $sku_to_price = [];
            $sp_commodity_list = [];
            foreach ($sp_list as $v) {
                $sku_to_sp[$v['id']] = $v['sp_value_list'];
                $sku_to_price[$v['id']] = $v['price'];
                $sp_commodity_list[$v['commodity_id']]['commodity_id'] = $v['commodity_id'];
                $sp_commodity_list[$v['commodity_id']]['is_pp'] = 0;
                $sp_commodity_list[$v['commodity_id']]['gc_id'][] = $v['id'];
                $sp_commodity_list[$v['commodity_id']]['gc_stock'][] = $v['stock'];
                $sp_commodity_list[$v['commodity_id']]['pay_style'] = $v['pay_style'];
                $sp_commodity_list[$v['commodity_id']]['is_store'] = $v['is_store'];
//                $sp_commodity_list[$v['commodity_id']]['relate_car_ids'][] = $v['relate_car_ids'];
            }
//            print_json($new_goods_list);
            foreach ($sp_commodity_list as $sp_kk => $sp_gg) {
                foreach ($new_goods_list as $n_g_v) {
                    if ($sp_kk = $n_g_v['commodity_id']) {
                        $n_g_v['gc_id'] = explode(',', $n_g_v['gc_id']);
                        $n_g_v['gc_stock'] = explode(',', $n_g_v['gc_stock']);
                        $sp_commodity_list[$sp_kk] = $n_g_v;
                    }
                }
            }
            foreach ($suit_list as $k => $v) {
                //  `dis_type` '1自由配置2满减3限时折扣44限时立减5一口价',
                $suit_list[$k]['suit_all_old_price'] = 0;
                $suit_list[$k]['suit_price'] = 0;
                $suit_list[$k]['suit_id'] = $k;
                $suit_list[$k]['suit_yh'] = 0;
                $suit_list[$k]['goods_count'] = count($v['suit_list']);
                $suit_list[$k]['need_dlr'] = 0;//需要专营店
                $suit_list[$k]['need_18n'] = 0;//需要18位
                $suit_list[$k]['can_refund'] = 0;//能退
                $suit_list[$k]['have_content'] = 0;//有内容
                $suit_list[$k]['all_use_point'] = 1;//整个使用积分
                $suit_list[$k]['need_ki'] = 0;//需要公里数

                foreach ($v['suit_list'] as $kk => $vv) {
                    $list_dis_type = 0;
                    $suit_list[$k]['suit_name'] = $vv['name'];
                    //todo 计算最低价格的那一条sku给commodityid--已完成
                    foreach ($sp_commodity_list as $gl) {
                        if ($vv['commodity_id'] == $gl['commodity_id']) {
                            //todo 需要判断该规格是否有库存
                            $suit_list[$k]['suit_list'][$kk]['can_buy'] = 1;
                            $suit_list[$k]['suit_list'][$kk]['can_buy_word'] = '';
                            $suit_sku_info = json_decode($vv['sku_json'], true);
                            if ($vv['can_refund'] == 1) {
                                $suit_list[$k]['can_refund'] = 1;
                            }
                            //改成全部到店类型--TZL
//                            if($gl['relate_car_ids']==1){
//                                $row['need_18n']=1;
//                            }
                            if ($vv['detail_content']) {
                                $suit_list[$k]['have_content'] = 1;
                            }

                            $one_count_stock = 0;
                            $gl_gc_id = $gl['gc_id'];
                            $gl_gc_stock = $gl['gc_stock'];

                            foreach ($gl_gc_id as $gl_kk => $gl_vvv) {
                                if ($gl_gc_stock[$gl_kk] <= 0) {
                                    unset($gl_gc_id[$gl_kk]);
                                } else {
                                    $one_count_stock += $gl_gc_stock[$gl_kk];
                                }
                            }
                            arsort($suit_sku_info);
                            $suit_sku_id = array_keys($suit_sku_info);
                            $gt_sku = array_intersect($suit_sku_id, $gl_gc_id);

//                            if($vv['commodity_id']==39156){
//                                var_dump($gl_gc_id);
//                                var_dump($gt_sku);
//                                dd($suit_sku_id);
//
//                            }
//
                            $suit_list[$k]['suit_list'][$kk]['is_pp'] = $gl['is_pp'];
                            if ($gl['is_pp'] == 0) {
                                $suit_list[$k]['suit_list'][$kk]['can_buy'] = 0;
                                $suit_list[$k]['suit_list'][$kk]['can_buy_word'] = '当前车型无适用商品规格';//个完全没有适用的
                            }
                            if (!$gt_sku) {
                                $gt_sku = $suit_sku_id;
                                $suit_list[$k]['suit_list'][$kk]['can_buy'] = 0;
                                $suit_list[$k]['suit_list'][$kk]['can_buy_word'] = '当前车型无适用商品规格';//还查一个完全没有适用的
                            }
                            $suit_one_sku_id = end($gt_sku);
                            if (isset($gg_to_sku[$vv['commodity_id']])) {
                                $suit_one_sku_id = $gg_to_sku[$vv['commodity_id']];
                            }
                            $suit_one_sku_price = $suit_sku_info[$suit_one_sku_id];

                            $list_dis_type = $vv['dis_type'];
                            if ($vv['dis_type'] == 3) {
                                $suit_one_sku_price = bcmul($suit_one_sku_price, bcdiv($vv['discount'], 10, 4), 2);
                            }
                            $sku_id_price_arr[$suit_one_sku_id] = $suit_one_sku_price;
//                            if($vv['dis_type']==4){
//                                $suit_one_sku_price =bcsub($suit_one_sku_price,$vv['discount'],2);
//                            }
                            $suit_list[$k]['suit_list'][$kk]['old_price'] = $sku_to_price[$suit_one_sku_id];
                            $suit_list[$k]['suit_list'][$kk]['price'] = $suit_one_sku_price;
                            $suit_list[$k]['suit_list'][$kk]['pay_style'] = $gl['pay_style'];
                            $suit_list[$k]['suit_list'][$kk]['sku_id'] = $suit_one_sku_id;
                            $suit_list[$k]['suit_all_old_price'] += $sku_to_price[$suit_one_sku_id];
                            $suit_list[$k]['suit_price'] += $suit_one_sku_price;
                            if ($gl['pay_style'] <> 3) {
                                $suit_list[$k]['all_use_point'] = 0;//只要有一个不能使用积分，那就都不用积分
                            }
                            $suit_list[$k]['suit_yh'] += bcsub($sku_to_price[$suit_one_sku_id], $suit_one_sku_price, 2);
                            $suit_list[$k]['full_info'] = $vv['full_info'];
                            $suit_list[$k]['discount'] = $vv['discount'];
                            $suit_list[$k]['suit_list'][$kk]['count_stock'] = $one_count_stock;


//                            if(in_array($vv['dd_commodity_type'],[1,3,4,6,9]) || $vv['commodity_class']==6){
                            if ($vv['dd_commodity_type'] <> 0 || $vv['commodity_class'] == 6) {
                                $row['need_18n'] = 1;
                                $suit_list[$k]['need_18n'] = 1;
//                                dd($row['wn_can_buy']);
                                if (in_array($vv['dd_commodity_type'], [3, 4]) || $vv['commodity_class'] == 6) {
                                    $suit_list[$k]['need_car'] = 1;//需要车主
                                    if (($vv['dd_commodity_type'] == 3 && $row['xy_can_buy'] == 0)) {
                                        $suit_list[$k]['suit_list'][$kk]['can_buy'] = 0;
                                        $suit_list[$k]['suit_list'][$kk]['can_buy_word'] = '当前车型不适合该套餐产品`';
                                    }
                                    if (($vv['dd_commodity_type'] == 4 && $row['wn_can_buy'] == 0)) {
                                        $suit_list[$k]['suit_list'][$kk]['can_buy'] = 0;
                                        $suit_list[$k]['suit_list'][$kk]['can_buy_word'] = '当前车型不适合该套餐产品';
                                    }
                                }
                                if ($vv['dd_commodity_type'] == 3) {
                                    $suit_list[$k]['need_ki'] = 1;//需要公里数
                                }

                            }
                            $suit_list[$k]['suit_list'][$kk]['mail_method'] = 2;//快递
                            if ($gl['is_store'] == 1) {
                                $suit_list[$k]['need_dlr'] = 1;//需要店
                                $row['need_dlr'] = 1;
                                $suit_list[$k]['suit_list'][$kk]['mail_method'] = 1;//1到店
                            }

                            if ($one_count_stock <= 0) {
                                $suit_list[$k]['suit_list'][$kk]['can_buy'] = 0;
                                $suit_list[$k]['suit_list'][$kk]['can_buy_word'] = '无货';
                            }
                            $one_sp_value = isset($sku_to_sp[$suit_one_sku_id]) ? $sku_to_sp[$suit_one_sku_id] : '';
                            $one_sp_value_name = '';
                            if ($one_sp_value) {
                                $sp_value_list = $this->sp_model->getAllList(['a.id' => ['in', $one_sp_value]]);
                                $sku_value = '';
                                if ($sp_value_list) {
                                    foreach ($sp_value_list as $spv) {
                                        $sku_value .= $spv['sp_name'] . ":" . $spv['sp_value_name'] . ", ";
                                    }
                                    $one_sp_value_name = trim($sku_value, ', ');
                                }
                            }
                            $suit_list[$k]['suit_list'][$kk]['sp_value_list'] = $one_sp_value;
                            $suit_list[$k]['suit_list'][$kk]['sp_name'] = $one_sp_value_name;
                        }
                    }
                    $suit_list[$k]['dis_type'] = $list_dis_type;
                }
                if ($list_dis_type == 2) {
                    $full_dis_info = json_decode($suit_list[$k]['full_info'], true);
                    $suit_list[$k]['suit_yh'] = 0;
                    foreach ($full_dis_info as $f_v) {
                        if ($suit_list[$k]['suit_price'] >= $f_v[0]) {
                            $suit_list[$k]['suit_price'] -= $f_v[1];
                            $suit_list[$k]['suit_yh'] = $f_v[1];
                            break;
                        }
                    }
                }
                if ($list_dis_type == 4) {
                    $suit_list[$k]['suit_price'] -= $suit_list[$k]['discount'];
                    $suit_list[$k]['suit_yh'] = $suit_list[$k]['discount'];
                }
                if ($list_dis_type == 5) {
                    $suit_list[$k]['suit_yh'] = $suit_list[$k]['suit_price'] - $suit_list[$k]['discount'];
                    $suit_list[$k]['suit_price'] = $suit_list[$k]['discount'];
                }
                $toEnd = $suit_list[$k]['goods_count'];
                $one_suit_goods_l_money = 0;
                //分摊到每一个价格~
                if ($suit_list[$k]['suit_price'] < 0) {
                    $suit_list[$k]['suit_price'] = 0;
                }
                $suit_list[$k]['suit_price'] = sprintf("%.2f", round($suit_list[$k]['suit_price'], 2));
                $suit_list[$k]['suit_yh'] = sprintf("%.2f", round($suit_list[$k]['suit_yh'], 2));
                $have_goods_arr = [];
                $have_goods_id = [];
                foreach ($suit_list[$k]['suit_list'] as $s_k => $s_v) {
                    if ($s_v['dis_type'] <> 1) {
                        if (0 === --$toEnd) {
                            $suit_list[$k]['suit_list'][$s_k]['price'] = $sku_id_price_arr[$s_v['sku_id']] = bcsub($suit_list[$k]['suit_price'], $one_suit_goods_l_money, 2);
                        } else {
                            $one_rel_price = bcdiv(bcmul($suit_list[$k]['suit_price'], $s_v['old_price']), $suit_list[$k]['suit_all_old_price'], 2);
                            $suit_list[$k]['suit_list'][$s_k]['price'] = round($one_rel_price, 2);
                            $sku_id_price_arr[$s_v['sku_id']] = $one_rel_price;
                            $one_suit_goods_l_money += $one_rel_price;
                        }
                    }
                    //重训排序该商品  && !in_array($s_v['commodity_id'],$have_goods_id)
                    if ($s_v['commodity_id'] == $have_goods_id) {
                        $have_goods_arr[] = $suit_list[$k]['suit_list'][$s_k];
                        $have_goods_id[] = $s_v['commodity_id'];
                        unset($suit_list[$k]['suit_list'][$s_k]);
                    }
                }
//                $new_suit_list = collection($suit_list[$k]['suit_list'])->toArray();
                //把那个商品放最前面了
                $suit_list[$k]['suit_list'] = array_merge($have_goods_arr, $suit_list[$k]['suit_list']);
                //  `dis_type` '1自由配置2满减3限时折扣44限时立减5一口价',
            }
            $row['list'] = array_values($suit_list);
        }
        $row['suit_info'] = $suit['suit_info'];
        $row['suit_count'] = $suit_count;
        $row['sku_id_price'] = $sku_id_price_arr;
        $row['ap_time'] = date('Y-m-d H:i:s');

        return $this->re_msg($row);


    }

    /**
     * 获取商品详情
     * */
    public function getSkuDetailContent($commodity_id, $sku_id)
    {
        $expand_model = new DbCommodityExpand();
        $commodity_model = new DbCommodity();
        $set_sku_model = new DbCommoditySetSku();
        $commodity_info = $commodity_model->getOne(['where' => ['id' => $commodity_id]]);
        if (empty($commodity_info)) {
            return $this->setResponseError('商品id有误')->send();
        }
        $set_sku_info = $set_sku_model->getOne(['where' => ['id' => $sku_id]]);
        $return_data = [
            'commodity_id' => $commodity_id,
            'sku_id' => $sku_id,
            'commodity_sku_detail' => $commodity_info['detail_content'],
        ];

        $params = [
            'where' => [
                'commodity_id' => $commodity_id,
                'expand_type' => 2
            ],
            'field' => 'commodity_sku_detail',
        ];
        if ($commodity_info['detail_type'] == 2) {
            $params['where']['commodity_sku_id'] = $set_sku_info['commodity_sku_id'];
        }
        $sku_detail = $expand_model->getOne($params);
        if (!empty($sku_detail)) {
            $return_data['commodity_sku_detail'] = $sku_detail['commodity_sku_detail'];
        }
        return $return_data;
    }

    public function bestCardJs($requestData, $user, $channel_type)
    {
        $set_sku_ids = $requestData['set_sku_id'] ?? '';
        $old_price = $requestData['old_price'];
        $activity_price = $requestData['activity_price'] ?? 0;
        $member_price = $requestData['member_price'] ?? 0;
        $activity_id = $requestData['activity_id'] ?? 0;
        $activity_type = $requestData['activity_type'] ?? 0;
        $commodity_id = $requestData['commodity_id'] ?? 0;
        $dd_dlr_code = $requestData['dd_dlr_code'] ?? 0;
        $count = $requestData['count'] ?? 1;
        $sku_json = $requestData['sku_json'] ??'';
//        $goods_card_where = [
//            "b.commodity_set_id" => ['in', $commodity_id],
//        ];
//        if ($is_get == 1) {
//            $goods_card_where['b.is_can_receive'] = 1;
//        }
        //领取卡券增加渠道  20220110 CP&TCP
//        $goods_card_where[] = ['exp', sprintf("FIND_IN_SET('%s', a.up_down_channel_dlr)", $channel_type)];

//        $goods_card = $this->_commodityCard($goods_card_where, "a.*,b.is_can_receive");
        $flat_model = new DbCommodityFlat();
        $flat_where = ['commodity_id' => ['in', $commodity_id]];
        $flat_where[] = ['exp', sprintf("FIND_IN_SET('%s', up_down_channel_dlr)", $channel_type)];
        $commodity_info = $flat_model->getOne(['where' => $flat_where]);

        //非组合商品，计算上架价格吧
        if ($commodity_info['is_grouped'] == 0) {
            $set_sku_model = new DbCommoditySetSku();
            $sku_info = $set_sku_model->getOneByPk($set_sku_ids);
            if ($sku_info) {
                $old_price = $sku_info['price'];
            }
        }
        $card_get_use = $this->card_get_use($commodity_info['commodity_set_id'],[],$user,$channel_type,[],'',1,[],$dd_dlr_code,'bestcard','',99,1);//use_gift_card 使用赠品券带上1，那么就不会查出7的
        $all_card = $card_get_use['all_card'];//获取卡券列表
        $card_rules = $card_get_use['goods_card_rule'];//获取卡券列表


        //特殊卡券应该还用计算可用+可领数
        $card_ky_kl = [];//用于特殊卡券的数量
        if($all_card){
            foreach ($all_card as $k_v=> $al_v){
                if(($al_v['change_car']==1 && !$al_v['can_use_card'])|| !$al_v['can_card'] ){//
                    unset($all_card[$k_v]);//
                    continue;
                }
                if($al_v['car_series_id_str']){
                    if(!in_array($user['car_series_id'],explode(',',$al_v['car_series_id_str']))){
                        unset($all_card[$k_v]);//
                        continue;
                    }
                }
                if($al_v['can_use_card']){
                    $card_ky_kl[$al_v['id']] = bcadd($al_v['all']??0,$al_v['available_quantity']);

                }
            }
        }

//        print_json($card_get_use,$all_card);

        $goods_info = [];
        if($sku_json){
            foreach($sku_json as $s_v){
                $s_flat = $flat_model->getOne(['commodity_id'=> $s_v['group_sub_commodity_id'],'is_enable'=>1]);
                $goods_info[]=['goods_id' => $commodity_info['commodity_set_id'], 'sub_goods_id' => $s_v['group_sub_commodity_id'], 'sku_id' => [$s_v['sku_id']], 'oil_type' => $s_flat['machine_oil_type']];
            }
//            $sku_jj =  array_column($sku_json,"sku_id");
//            $goods_card = $this->_goods_can_get_card($commodity_info['commodity_set_id'], $user['id'], $channel_type, 0,$sku_jj);
//            $sub_goods_id_arr =  array_column($sku_json,"group_sub_commodity_id");
//            $detail_card = $this->_detail_card($commodity_info['commodity_set_id'],$sku_jj,$sub_goods_id_arr,1,0,[],[],[],$channel_type);
//            $card_ids =  $detail_card['mz_card'];
//            foreach ($goods_card as $kk=>$card_v){
//                if(!in_array($card_v['id'],$card_ids)){
//                    unset($goods_card[$kk]);
//                }
//            }
        }else{
            $goods_info[]=['goods_id' => $commodity_info['commodity_set_id'], 'sub_goods_id' => '', 'sku_id' => [$set_sku_ids], 'oil_type' => 0];
//            $goods_card = $this->_goods_can_get_card($commodity_info['commodity_set_id'], $user['id'], $channel_type, 0,$set_sku_ids);
        }
        $goods_card = $this->card_list_ok($card_rules,$goods_info,$all_card,$commodity_info['commodity_set_id']);
        $goods_un_card_ids  = array_column($goods_card,'id');
        $card_ids = $goods_un_card_ids;

        $have_card = 0;
        $yh_af_price = $old_price;

        $member_price = 0;
        //众筹不计算会员价
        $crowd_dis_id = 0;
        if ($commodity_info['crowdfund_dis']) {
            $crowd_dis_arr = json_decode($commodity_info['crowdfund_dis'], true);

            $crowd_dis_id = isset($crowd_dis_arr[$channel_type][0]) ? $crowd_dis_arr[$channel_type][0] : 0;
        }
        if (!$crowd_dis_id) {
            $commodity_dis_info = $this->getCommoditySegmentDiscount($commodity_id);
            if ($commodity_dis_info) {
                // 上架价格 计算 商品会员折扣价
                $member_price = $this->getCommodityDisFinalPrice($commodity_dis_info, $old_price);
            }
        }

        $have_member_price = 0;
        if ($member_price > 0) {

            $yh_af_price = $member_price * $count;
            $have_member_price = 1;
        }
        if ($activity_price > 0 && $activity_price < $member_price) {
            $yh_af_price = $activity_price * $count;
        }
        $user_le_card_js = [];
        $ac_card_id = [];
        $card_can_count = [];
        if ($goods_card || $card_ids) {
            $card_id_arr = [];
            $card_can_get = [];
            $card_r_id = [];
//            print_json($goods_card);
            foreach ($goods_card as $v) {
                $card_id_arr[] = $v['id'];
                //available_count   NV 的才有可领取数量PZ的直接不返回了 增加了一个库存数
                //这一块上面有计算了，应该不需要再精算了
                if ($v['is_can_receive'] == 1 && $v['available_quantity'] > 0 && $v['available_count'] > 0) {
                    $card_can_get[] = $v['id'];
                }
                if($v['can_use_card']){
                    $card_r_id[]=$v['id'];
                }
                $card_can_count[$v['id']] =$v['all_cc']??0;
            }
//            //可使用卡券，然后与可领取卡券取并集，
//            $card_r_model = new BuCardReceiveRecord();
//            $card_r_where = ['user_id' => $user['id'], 'card_id' => ['in', $card_id_arr], 'is_enable' => 1, 'status' => 1];
////            $card_r_where[] =  ['exp',['validity_date_start'=>[['ELT',date('Y-m-d H:i:s')],['exp','is null'],'or'],'validity_date_end'=>[['EGT',date('Y-m-d H:i:s')],['exp','is null'],'or']]];
//            $card_r_where[] = ['exp', sprintf("(validity_date_start<='%s' || validity_date_start is null ||  validity_date_start='') and (validity_date_end>='%s' ||  validity_date_end is null || validity_date_end='' ) ", date('Y-m-d H:i:s'), date('Y-m-d H:i:s'))];
//            $card_r = $card_r_model->getList(['where' => $card_r_where]);
//            if ($card_r) {
//                $card_r_id = array_column($card_r, 'card_id');
//            }
//            var_dump($card_r_model->getLastSql());
//            print_json($card_can_get);

            $can_card_all = array_unique(array_merge($card_can_get, $card_r_id));//原价卡券ID.
            Logger::error('cardbestcard', ['goods' => $commodity_id, 'card_can_get' => $card_can_get, 'card_r_id' => $card_r_id]);
            $user_le_card_js = $this->canGetCards($user, $can_card_all, $channel_type);//会员卡券ID，应该是以这个进行计算
            //得出可以实际使用的卡券
            //根据活动计算是否要过滤卡券，也就是卡券模块的计算偏重
            //秒杀与限时折扣是传价格，只要价格<>原价，就判断了是参与活动了，不需要去判断SKU
            $rel_card_ids = '';
            $can_use_card = 0;
            $old_card_yh = $this->_cardJsBest($user_le_card_js, $old_price, $count,$card_can_count);
            $yh_af_price = bcsub($old_price * $count, $old_card_yh, 2);
            $member_all_yh = 0;
            if ($old_card_yh) {
                $have_card = 1;
            }
            if ($member_price) {
                $member_card_yh = $this->_cardJsBest($user_le_card_js, $member_price, $count,$card_can_count);
                $member_all_yh = bcadd(bcsub($old_price, $member_price, 2) * $count, $member_card_yh, 2);
                if ($member_all_yh > $old_card_yh) {
                    $yh_af_price = bcsub($old_price * $count, $member_all_yh, 2);
                    if ($member_card_yh) {
                        $have_card = 1;
                        $have_member_price = 1;
                    }
                }
            }
            $ac_card_yh = 0;
            $ac_all_yh = 0;
        }
        if (!empty($activity_id)) {
            $now_date = date("Y-m-d H:i:s");
            //有会员价，则活动价是通过会员价进行计算
            $old_price_1 = $old_price;
            $have_member_price = 0;
            if ($member_price) {
                $old_price = $member_price;
            }
            $have_act_price = 0;
            //限时活动
            if ($activity_type == 1 || $activity_type == 8) {
                $limitObj = new DbLimitDiscount();
                $limit_where = ['a.id' => $activity_id, 'a.start_time' => ['<=', $now_date], 'a.end_time' => ['>=', $now_date]];
                $limit_where[] = ['exp', " (find_in_set('{$channel_type}',a.up_down_channel_dlr)) "];
                $activity_info = $limitObj->getGroupInfo(['where' => $limit_where, 'field' => 'b.sku_dis,a.id,a.rel_card_ids,a.card_available,a.user_segment']);
                if ($commodity_info['is_grouped'] || $activity_price <> $old_price) {
                    $have_act_price = 1;
                    $rel_card_ids = $activity_info['rel_card_ids'];//有活动价
                    $can_use_card = $activity_info['card_available'];//判断能否用券
                    if ($activity_info['user_segment']) {
                        $have_member_price = 1;
                    }
                }
            }
            if ($activity_type == 11) {
                $crowdObj = new DbCrowdfund();
                $crowd_where = ['a.id' => $activity_id, 'a.start_time' => ['<=', $now_date], 'a.end_time' => ['>=', $now_date]];
                $crowd_where[] = ['exp', " (find_in_set('{$channel_type}',a.up_down_channel)) "];
                $activity_info = $crowdObj->getAllCrowdfund(['where' => $crowd_where, 'field' => 'b.sku_dis,a.id,a.ticket_ids,a.is_use_ticket', 'group' => 'a.id']);
                if (!empty($activity_info)) {
                    if ($commodity_info['crowdfund_dis'] || $activity_price <> $old_price) {
                        $rel_card_ids = $activity_info[0]['ticket_ids'];//有活动价
                        $can_use_card = $activity_info[0]['is_use_ticket'];//判断能否用券
                    }
                }
            }

            if ($activity_type == 3) {
                $fullObj = new DbFullDiscount();
                $full_where = ['id' => $activity_id, 'start_time' => ['<=', $now_date], 'end_time' => ['>=', $now_date]];
                $full_where[] = ['exp', " (find_in_set('{$channel_type}',up_down_channel_dlr)) "];
                $activity_info = $fullObj->where($full_where)->find();

                if ($activity_info) {
                    $full_sum_price = round(($old_price * $count), 2);
                    $full_sum_price_1 = round(($old_price_1 * $count), 2);
                    $full_discount_rules_list = json_decode($activity_info['full_discount_rules'], true);
                    if ($activity_info['user_segment']) {
                        $segment_info = get_user_segment_info();
                        $membership = $segment_info['membership_level'];
                        $owner = $segment_info['brand_owner_label'];
                        if ($activity_info['user_segment'] == 1) {
                            $full_discount_rules_list = $full_discount_rules_list[$membership];
                        } else {
                            $full_discount_rules_list = $full_discount_rules_list[$owner];
                        }
                    }
                    $activity_price = 0;
                    foreach ($full_discount_rules_list as $full_discount_rules_item) {
                        if ($full_sum_price >= $full_discount_rules_item[0]) {
                            $have_act_price = 1;
                            $activity_price = round($full_sum_price - $full_discount_rules_item[1], 2) / $count;
                            break;
                        }
                    }
                    //判断原价的满减是否优于会员价的满减

                    foreach ($full_discount_rules_list as $full_discount_rules_item) {
                        if ($full_sum_price_1 >= $full_discount_rules_item[0]) {
                            $activity_price_1 = round($full_sum_price_1 - $full_discount_rules_item[1], 2) / $count;
                            break;
                        }
                    }
                    if (isset($activity_price_1) && ($activity_price_1 < $activity_price || $activity_price == 0)) {
                        $activity_price = $activity_price_1;
                        $have_member_price = 0;
                    }

                    if ($activity_price) {
                        if ($activity_info['user_segment']) {
                            $have_member_price = 1;
                        }
                        $rel_card_ids = $activity_info['rel_card_ids'];
                        $can_use_card = $activity_info['card_available'];
                    }
                }

            }
            if ($activity_type == 6) {
                $ndisObj = new DbNDiscount();
                $n_dis_info_model = new DbNDiscountInfo();
                $n_dis_where = ['id' => $activity_id, 'start_time' => ['<=', $now_date], 'end_time' => ['>=', $now_date]];
                $n_dis_where[] = ['exp', " (find_in_set('{$channel_type}',up_down_channel_dlr)) "];
                $activity_info = $ndisObj->where($n_dis_where)->find();
                //加入用户登记
                $n_dis_where = ['n_id' => $activity_id];
                if ($activity_info['user_segment']) {
                    $segment_info = get_user_segment_info();
                    $membership = $segment_info['membership_level'];
                    $owner = $segment_info['brand_owner_label'];
                    if ($activity_info['user_segment'] == 1) {
                        $n_dis_where['segment_label'] = $membership;
                    } else {
                        $n_dis_where['segment_label'] = $owner;
                    }
                }

                $n_dis_info_list = $n_dis_info_model->getList(['where' => $n_dis_where, 'order' => "piece desc"]);
                $dis_p = 10;
                $activity_price = 0;
                if ($n_dis_info_list) {
                    foreach ($n_dis_info_list as $n_k => $n_v) {
                        if ($count >= $n_v['piece']) {
                            $dis_p = $n_v['discount'];

                            $have_act_price = 1;
                            $activity_price = round($old_price * $dis_p / 10 * $count, 2) / $count;


                            break;
                        }
                    }
                }
                if ($activity_price) {
                    $rel_card_ids = $activity_info['rel_card_ids'];
                    $can_use_card = $activity_info['card_available'];
                    if ($activity_info['user_segment']) {
                        $have_member_price = 1;
                    }
                }
            }
            if ($activity_type == 10) {
                $sekillObj = new DbSeckill();
                $sekill_where = ['id' => $activity_id, 'start_time' => ['<=', $now_date], 'end_time' => ['>=', $now_date]];
                $sekill_where[] = ['exp', " (find_in_set('{$channel_type}',up_down_channel_dlr)) "];
                $activity_info = $sekillObj->where($sekill_where)->find();
                if ($commodity_info['is_grouped'] || $activity_price <> $old_price) {
                    $rel_card_ids = $activity_info['rel_card_ids'];//有活动价
                    $can_use_card = $activity_info['card_available'];//判断能否用券
                    $have_act_price = 1;
                    if ($activity_info['user_segment']) {
                        $have_member_price = 1;
                    }
                }
            }
            if ($user_le_card_js) {
                if ($can_use_card == 1) {
                    if ($rel_card_ids) {
                        $rel_card_id_arr = explode(',', $rel_card_ids);
                        $ac_card_id = array_intersect($user_le_card_js, $rel_card_id_arr);
                    } else {
                        $ac_card_id = $user_le_card_js;
                    }
                }
            }
            $ac_card_yh = 0;
            if (!$have_act_price) {
                $activity_price = $old_price;
            }
            if ($ac_card_id) {
                $ac_card_yh = $this->_cardJsBest($ac_card_id, $activity_price, $count,$card_can_count);
            }

            //活动后价
            $ac_all_yh = bcadd(bcsub($old_price, $activity_price, 2) * $count, $ac_card_yh, 2);
            $ac_af_price = bcsub($old_price * $count, $ac_all_yh, 2);
//            echo bcsub($old_price,$activity_price,2).'--'.$activity_price.'--'.$old_price.'--'.$ac_card_yh.'---'.$ac_all_yh;die();
//            print_json($ac_all_yh,$ac_af_price);
            if ($ac_af_price < $yh_af_price || $crowd_dis_id) {
                $yh_af_price = $ac_af_price;
//                if($member_price){
//                    $have_member_price=1;
//                }
                $have_member_price = 0;//参与了活动，则没有会员价
                $have_card = 0;
                if ($ac_card_yh) {
                    $have_card = 1;
                }
            }
        }
        if ($yh_af_price < 0) {
            $yh_af_price = 0;
        }
        return $this->re_msg(['af_price' => sprintf("%.2f", $yh_af_price), 'have_card' => $have_card, 'have_member_price' => $have_member_price]);
    }

    //计算卡券最优了，
    private function _cardJsBest($card_id, $price, $count,$card_can_count)
    {
        $card_model = new DbCard();
        $where = ['id' => ['in', $card_id], 'is_enable' => 1];
        //可以加一个计算已领取的但是不能用的券的数量
        $card_list = $card_model->getList(['where' => $where]);
        $card_yh_money = 0;
        if ($card_list) {
            $all_price = bcmul($price, $count,2);
            //,a.id rec_id,a.activity_id rec_act_id
            foreach ($card_list as $k => $vv) {
                if(in_array($vv['receive_scene'],[59,60])){
                    $all_price = $price;
                }
                $yh_money = 0;
                $new_count = $count;
                if( in_array($vv['receive_scene'],[59,60])){
                    $new_count =  min($vv['get_limit'],$card_can_count[$vv['id']]??$count);//可领，商品数取最小值
                }

                if (($vv['least_cost'] && $all_price >= $vv['least_cost']) || !$vv['least_cost']) {
                    if ($vv['card_type'] == 2) {
                        $yh_money = round(($price - ($price * $vv['card_discount'] / 10)) * $new_count, 1);
//                        float(0.034)
//                        ++float(0.102)
                    } else {
                        $yh_money = $vv['card_quota'];
                    }


                }
//                if($test){
//                    var_dump($yh_money);
//                    var_dump($vv['least_cost']);
//                    var_dump($all_price);
//                    var_dump($vv['card_quota']);
//                    echo '---';
//                }
                if($vv['max_discount']){
                    $yh_money = min($yh_money, $vv['max_discount']);
                }

                $card_list[$k]['value'] = $yh_money;
                $card_list[$k]['rec_act_id'] = $vv['activity_id'];
            }
//            if($test){
//                die();
//
//            }
//            print_json($card_list);
            $best_js = $this->best_card($card_list);
//            if($test){
//                print_json($best_js);
//            }
            $card_yh_money = $best_js['card_yh_all_money'];

        }
        return $card_yh_money;

    }


    /**
     * 产品说一个主品只会关联一个卡券ID，运营自己控制。
     * @param $card_id
     * @param $user
     * @param $channel_type
     * @return array
     */
    public function getGiftGoods($card_id,$user,$channel_type)
    {

        $goods_card_model =  new DbCommodityCard();
        $goods_card_list =  $goods_card_model->getList(['where'=>['card_id'=>$card_id]]);
//        $goods_type = [];
//        $goods_arr = [];
//        foreach ($goods_card_list as $v){
////            if($v['class_id']){
////                $goods_type = $v['class_id'];
////            }
//            if($v['set_sku_ids']){
//                $set_sku_ids_arr =  explode(',',$v['set_sku_ids']);
//                if(isset($goods_arr[$v['commodity_set_id']])){
//                    $goods_arr[$v['commodity_set_id']] =  array_merge($goods_arr[$v['commodity_set_id']],$set_sku_ids_arr);
//                }else{
//                    $goods_arr[$v['commodity_set_id']] =$set_sku_ids_arr;
//                }
//            }
//        }
        $query = [
            'card_id'=>$card_id,
            'card_gift'=>1
        ];
        $spec_arr = redis(config('cache_prefix.spec_value'));
        if (empty($spec_arr)) {
            $spec_arr = [];
            $spec_model = new DbSpecValue();
            $spec = $spec_model->getList();
            foreach ($spec as $v) {
                $spec_arr[$v['id']] = $v['sp_value_name'];
            }
            redis(config('cache_prefix.spec_value'), $spec_arr, 10);
        }
//        print_json($query);
        $goods_list =  $this->goodsList($query,$user,$channel_type,[],'get_g_g');
        $re_data = [];
        if($goods_list){
            $res =  $goods_list['msg']['data'];
            foreach ($res as $k=>$v){
                $sp_name='';
                //正常不会有组合商品--产品说的
                if ($v['de_ss_plist'] && $v['is_grouped']!=1) {
                    $sp_list = explode(',', $v['de_ss_plist']);
                    foreach ($sp_list as $vvv) {
                        $sp_name .= $spec_arr[$vvv] . ',';
                    }
                    $v['sp_name'] = trim($sp_name, ',');
                } else {
                    $v['sp_name'] = '';
                }
                $re_data[]=$v;
            }

        }

        return $re_data;

    }

    //goods/gift_card_num
    public function checkGiftCard($data,$user,$cart_id=[],$channel_type='')
    {
        if(!isset($user['card_r_gift_wjh'])){
            return ['number'=>0,'card_id'=>'','card_desc'=>''];
        }
        $model = new DbCommoditySetSku();
        $sku_id_arr =  array_column(json_decode($data,true),"sku_id");
//        print_json($data,$sku_id_arr);
        $where = ['a.id'=>['in',$sku_id_arr],'a.is_enable'=>1];
        $sku_list = $model->alias('a')->join('t_db_commodity_sku sku','a.commodity_sku_id=sku.id and  a.group_sub_commodity_id<1')
            ->where($where)->field('sku.sku_code')->select();
//            ->join('t_db_commodity goods','sku.commodity_id=goods.id and goods.is_grouped=0')
//        print_json($model->getLastSql());
        $sku_code_arr = [];
        $sku_code_str = '';
        foreach ($sku_list as $v){
            if($v['sku_code']){
                $sku_code_str.=$v['sku_code'].',';
            }
        }
        if($sku_code_str){
            $sku_code_arr =  explode(',',trim($sku_code_str,','));
            $act_id_arr =  array_column($user['card_r_gift_wjh'],'activity_id');
            $gift_card_rule = $this->gift_card_rule($act_id_arr,$sku_code_arr);
            $gift_card_rule_list =  $gift_card_rule['list'];
            $act_id_list =  array_column($gift_card_rule_list,'activity_id');
            $card_id_s=[];
            $card_id_str = '';//一个商品取一个卡券
            foreach ($user['card_r_gift_wjh'] as $r_v){
                if(in_array($r_v['activity_id'],$act_id_list)){
                    $card_id_s[$r_v['card_id']][] =$r_v['card_code'];//[]
                    $card_id_str = $r_v['card_id'];
                }
            }
            if($card_id_s){
                $card_id_one =  end($card_id_s);
                $old_count = count($card_id_one);
                $shop_count =  $this->cart_gift_card($card_id_str,$user,$cart_id);
                $u_count  =  bcsub($old_count,$shop_count);
                $card_model =  new DbCard();
                $card_info =  $card_model->getOneByPk($card_id_str);

                return ['number'=>$u_count,'card_id'=>$card_id_str,'card_desc'=>$card_info['card_desc']];
            }
        }
        return ['number'=>0,'card_id'=>'','card_desc'=>''];
    }

    /**
     * 通过子品sku获取主品
     * @param $sku_id
     * @param $user
     * @return array
     */
    public function checkGiftGoods($sku_id,$user)
    {
        if(!isset($user['card_r_gift_wjh'])){
            return ['have_gift_card'=>0,'goods_id'=>999999999];
        }
        $act_id_arr =  array_column($user['card_r_gift_wjh'],'card_id');
        $where = ['card_id'=>['in',$act_id_arr],'is_enable'=>1];
        $where[] = ['exp',sprintf("find_in_set('%s',set_sku_ids)",$sku_id)];

        $goods_card_model =  new DbCommodityCard();
        $goods_card_list =  $goods_card_model->getList(['where'=>$where]);
        $str='';
        if($goods_card_list){
            $goods_card_list_id =  array_column($goods_card_list,'card_id');

            foreach ($user['card_r_gift_wjh'] as $v){
                if(in_array($v['card_id'],$goods_card_list_id) ){
                    $gift_card_id = $v['card_id'];
                    break;
                }
            }

            $gift_goods = $this->getGiftMainGoods($gift_card_id,$user);
            if($gift_goods){
                return $gift_goods;
            }
        }

        return ['have_gift_card'=>0,'goods_id'=>999999999];
    }

    //赠品券反查主品
    public function getGiftMainGoods($gift_card_id,$user)
    {
        if(!isset($user['card_r_gift_wjh'])){
            return ['have_gift_card'=>0,'goods_id'=>'99999999'];
        }
        $act_id = '';
        foreach ($user['card_r_gift_wjh'] as $v){
            if($v['card_id'] == $gift_card_id){
                $act_id = $v['activity_id'];
            }
        }
        if($act_id){
            $gift_card_rule = $this->gift_card_rule($act_id);
            if($gift_card_rule){
                $gift_card_sku_arr = $gift_card_rule['gift_card_sku_arr'];
                $gift_card_sku_class_arr = $gift_card_rule['gift_card_sku_class_arr'];

                if(!$gift_card_sku_arr && !$gift_card_sku_class_arr){
                    return ['have_gift_card'=>0,'goods_id'=>99999999];

                }
                $sku_model =  new DbCommoditySku();
                $sku_where =  ['a.is_enable'=>1];
                $sku_where[] =  ['exp',sprintf("find_in_set('%s',a.relate_car_ids) || a.relate_car_ids='' || a.relate_car_ids is null",$user['car_series_id'])];
                //查主品--
                $sku_where_str = '';
                if($gift_card_sku_arr){
                    foreach ($gift_card_sku_arr as $ss_v){
                        $sku_where_str.=sprintf(" find_in_set('%s' ,a.sku_code) or",$ss_v);
                    }

                }
                if($gift_card_sku_class_arr){
                    foreach ($gift_card_sku_class_arr as $ss_v){
                        $sku_where_str.=sprintf(" find_in_set('%s' ,a.variety_code) or",$ss_v);
                    }
                }
                if($sku_where_str){
                    $sku_where_str =  trim($sku_where_str,'or');
                    $sku_where[] = ['exp',$sku_where_str];
                }
                $sku_list =  $sku_model->alias('a')->join('t_db_commodity_set_sku set_sku','set_sku.commodity_sku_id=a.id and set_sku.is_enable=1')->where($sku_where)->field('DISTINCT a.commodity_id')->select();
//                print_json($sku_model->getLastSql());
                if(!empty($sku_list)){
                    $goods_id =  array_column($sku_list,'commodity_id');
//                    $set_sku_sub_model = new DbCommoditySub();
//                    $set_sku_list =  $set_sku_sub_model->getList(['where'=>['group_sub_commodity_id'=>['in',$goods_id],'is_enable'=>1],'field'=>'distinct commodity_id']);
//
//                    if($set_sku_list){
//                        $mo_goods_id =  array_column($set_sku_list,'commodity_id');
//                        $goods_id =  array_merge($goods_id,$mo_goods_id);
//                    }
                    if(empty($goods_id)) $goods_id = 99999999;
                    return ['have_gift_card'=>1,'goods_id'=>implode(',',$goods_id)];
                }
            }
        }

        return ['have_gift_card'=>0,'goods_id'=>'99999999'];;
    }










}
