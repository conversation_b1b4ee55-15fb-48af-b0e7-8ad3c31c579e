<?php

namespace app\common\service;

/**
 * 缓存服务类 - 防止缓存穿透
 * 
 * 解决问题：
 * 1. 缓存穿透：查询结果为空时，避免重复查询数据库
 * 2. 内存缓存：单次请求内的重复查询直接从内存获取
 * 3. 统一缓存管理：提供统一的缓存接口
 */
class CacheService
{
    /**
     * 空结果标记
     */
    const EMPTY_RESULT_MARK = '__EMPTY_RESULT__';
    
    /**
     * 内存缓存
     * @var array
     */
    private static $memoryCache = [];
    
    /**
     * 获取缓存数据（防穿透版本）
     * 
     * @param string $cache_key 缓存键
     * @param callable $data_loader 数据加载器函数
     * @param int $cache_time 缓存时间（秒），默认30分钟
     * @param int $empty_cache_time 空结果缓存时间（秒），默认5分钟
     * @return mixed|null
     */
    public static function getWithAntiPenetration($cache_key, callable $data_loader, $cache_time = 1800, $empty_cache_time = 300)
    {
        // 1. 检查内存缓存
        if (isset(self::$memoryCache[$cache_key])) {
            $cached_result = self::$memoryCache[$cache_key];
            return $cached_result === self::EMPTY_RESULT_MARK ? null : $cached_result;
        }
        
        // 2. 检查Redis缓存
        $cached_data = redis($cache_key);
        
        if ($cached_data === false || $cached_data === null) {
            // 缓存不存在，执行数据加载器
            $data = $data_loader();
            
            if ($data) {
                // 有数据，正常缓存
                redis($cache_key, $data, $cache_time);
                self::$memoryCache[$cache_key] = $data;
                return $data;
            } else {
                // 无数据，缓存空结果标记
                redis($cache_key, self::EMPTY_RESULT_MARK, $empty_cache_time);
                self::$memoryCache[$cache_key] = self::EMPTY_RESULT_MARK;
                return null;
            }
        } else {
            // 缓存存在
            if ($cached_data === self::EMPTY_RESULT_MARK) {
                // 是空结果标记
                self::$memoryCache[$cache_key] = self::EMPTY_RESULT_MARK;
                return null;
            } else {
                // 是正常数据
                self::$memoryCache[$cache_key] = $cached_data;
                return $cached_data;
            }
        }
    }
    
    /**
     * 批量获取缓存数据
     * 
     * @param array $cache_keys 缓存键数组
     * @param callable $batch_loader 批量数据加载器，接收未命中的键数组
     * @param int $cache_time 缓存时间
     * @param int $empty_cache_time 空结果缓存时间
     * @return array 键值对数组
     */
    public static function batchGetWithAntiPenetration(array $cache_keys, callable $batch_loader, $cache_time = 1800, $empty_cache_time = 300)
    {
        $results = [];
        $missed_keys = [];
        
        // 1. 检查内存缓存和Redis缓存
        foreach ($cache_keys as $key) {
            if (isset(self::$memoryCache[$key])) {
                // 内存缓存命中
                $cached_result = self::$memoryCache[$key];
                $results[$key] = $cached_result === self::EMPTY_RESULT_MARK ? null : $cached_result;
            } else {
                // 检查Redis缓存
                $cached_data = redis($key);
                if ($cached_data === false || $cached_data === null) {
                    // 缓存未命中
                    $missed_keys[] = $key;
                } else {
                    // Redis缓存命中
                    if ($cached_data === self::EMPTY_RESULT_MARK) {
                        self::$memoryCache[$key] = self::EMPTY_RESULT_MARK;
                        $results[$key] = null;
                    } else {
                        self::$memoryCache[$key] = $cached_data;
                        $results[$key] = $cached_data;
                    }
                }
            }
        }
        
        // 2. 批量加载未命中的数据
        if (!empty($missed_keys)) {
            $loaded_data = $batch_loader($missed_keys);
            
            foreach ($missed_keys as $key) {
                if (isset($loaded_data[$key]) && $loaded_data[$key]) {
                    // 有数据
                    redis($key, $loaded_data[$key], $cache_time);
                    self::$memoryCache[$key] = $loaded_data[$key];
                    $results[$key] = $loaded_data[$key];
                } else {
                    // 无数据
                    redis($key, self::EMPTY_RESULT_MARK, $empty_cache_time);
                    self::$memoryCache[$key] = self::EMPTY_RESULT_MARK;
                    $results[$key] = null;
                }
            }
        }
        
        return $results;
    }
    
    /**
     * 清除缓存
     * 
     * @param string|array $cache_keys 缓存键或键数组
     */
    public static function clear($cache_keys)
    {
        if (!is_array($cache_keys)) {
            $cache_keys = [$cache_keys];
        }
        
        foreach ($cache_keys as $key) {
            // 清除Redis缓存
            redis($key, null);
            // 清除内存缓存
            unset(self::$memoryCache[$key]);
        }
    }
    
    /**
     * 清除所有内存缓存
     */
    public static function clearMemoryCache()
    {
        self::$memoryCache = [];
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return array
     */
    public static function getStats()
    {
        return [
            'memory_cache_count' => count(self::$memoryCache),
            'memory_cache_keys' => array_keys(self::$memoryCache),
            'empty_results_count' => count(array_filter(self::$memoryCache, function($value) {
                return $value === self::EMPTY_RESULT_MARK;
            }))
        ];
    }
    
    /**
     * 生成缓存键
     * 
     * @param string $prefix 前缀
     * @param array $params 参数数组
     * @return string
     */
    public static function generateKey($prefix, array $params = [])
    {
        if (empty($params)) {
            return $prefix;
        }
        
        // 对参数进行排序，确保相同参数生成相同的键
        ksort($params);
        $param_string = http_build_query($params);
        
        return $prefix . '_' . md5($param_string);
    }
}
