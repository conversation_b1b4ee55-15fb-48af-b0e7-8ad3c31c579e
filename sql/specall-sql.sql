[ SQL ] SELECT * FROM `t_db_user` WHERE `plat_id` = '60272024894248583170' AND `is_enable` = 1 LIMIT 1 [ RunTime:0.001454s ]
[ SQL ] SELECT * FROM `t_db_user` WHERE `id` = 159670 LIMIT 1 [ RunTime:0.001238s ]
[ SQL ] SELECT * FROM `t_db_user_sub` WHERE `user_id` = 159670 AND `channel_type` = 'GWSM' LIMIT 1 [ RunTime:0.002275s ]
[ SQL ] UPDATE `t_db_user` SET `headimg_market`='https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLIicEOcWEPytt42aureibdz1cBNQia6qzsQ13MQ5b6XxfPxWVSWNRiaHLnicicRw1enW18pkOvh5UYRyPQ/132',`headimg_app`='https://vitfile.venucia.com/mine_ic_head_portrait_default.png',`nickname_market`='I\'m here',`nickname_app`='136****6591',`one_id`='97c16d435e538d0291ae6363f54568e06591',`mid_phone`='13632236591' WHERE `id` = 159670 [ RunTime:0.001146s ]
[ SQL ] UPDATE `t_db_user_sub` SET `creator`='c_user1',`unionid`='oMiuet5Yel-mF5WZJcOcDP8QMUIo',`openid`='oSET1jrJa4gbE36IXevrgp0_t3Gg',`channel_type`='GWSM',`modifier`='c_user1' WHERE `user_id` = 159670 AND `channel_type` = 'GWSM' [ RunTime:0.001262s ]
[ SQL ] SELECT * FROM `t_db_user_car_series` WHERE `user_id` = 159670 AND `car_brand_code` = 1 AND `channel_type` = '' [ RunTime:0.002912s ]
[ SQL ] SELECT * FROM `t_db_user_car_series` WHERE `user_id` = 159670 AND `channel_type` = 'GWSM' AND `is_vin_car` = 1 [ RunTime:0.001222s ]
[ SQL ] SELECT `a`.`id`,`a`.`is_vin_car`,`a`.`vin`,`a`.`is_bind`,a.relate_car_18n car_config_code,a.car_series_name car_series_cn,a.car_type_name large_car_type_cn,`a`.`car_series_id`,b.car_brand_code brand,`a`.`is_empower`,`b`.`is_nev` FROM `t_db_user_car_series` `a` INNER JOIN `t_e3s_car_series` `b` ON `a`.`relate_car_18n`=`b`.`car_config_code` WHERE `a`.`user_id` = 159670 AND `a`.`car_brand_code` = 1 AND `a`.`is_enable` = 1 AND `a`.`relate_car_18n` <> '' AND `a`.`channel_type` = 'GWSM' GROUP BY a.vin ORDER BY a.is_vin_car desc,a.last_updated_date desc [ RunTime:0.001315s ]
[ SQL ] UPDATE `t_db_user` SET `car_series_id`='8573',`car_18n`='TDNALEWT33UXA-B--A' WHERE `id` = 159670 [ RunTime:0.000985s ]
[ SQL ] SELECT `a`.`card_code`,`a`.`card_id`,`a`.`status`,`a`.`is_enable`,`a`.`activity_id` FROM `t_bu_card_receive_record` `a` INNER JOIN `t_db_card` `b` ON `a`.`card_id`=`b`.`id` INNER JOIN `t_db_activity` `act` ON `act`.`activity_id`=a.activity_id and act.activity_status_flag=1 WHERE `b`.`is_gift_card` = 1 AND `a`.`activity_id` > 0 AND `a`.`validity_date_start` <= '2025-08-26 16:35:31' AND `a`.`validity_date_end` >= '2025-08-26 16:35:31' AND `act`.`activity_time_start` <= '2025-08-26 16:35:31' AND `act`.`activity_time_end` >= '2025-08-26 16:35:31' AND `a`.`status` = 7 AND `b`.`brand_id` = 1 AND `b`.`is_enable` = 1 AND `a`.`is_enable` = 1 AND ( (a.receive_vin='LGBM2ME49MS505780' and (a.user_id='159670' or a.user_id=0)) || ((a.receive_vin='' || a.receive_vin is null) and a.user_id='159670') ) [ RunTime:0.001803s ]
[ SQL ] SELECT `title`,`data_json`,`bg_color`,`bg_img`,`is_dd`,`is_share`,`share_title`,`share_img`,`share_url`,`is_staff` FROM `t_db_special_sm` WHERE `page_type` = 2 AND `id` = 1213 LIMIT 1 [ RunTime:0.002029s ]
[ SQL ] SELECT * FROM `t_ac_gonghui_info` WHERE `is_enable` = 1 AND `sp_id` = 1213 LIMIT 1 [ RunTime:0.000947s ]
[ SQL ] SELECT * FROM `t_db_bdp_recommend` WHERE `vin` = 'LGBM2ME49MS505780' LIMIT 1 [ RunTime:0.001612s ]
[ SQL ] SELECT * FROM `t_db_ads_recommended_datas` WHERE `vin` = 'LGBM2ME49MS505780' AND `recommend_type` = 1 LIMIT 1 [ RunTime:0.001617s ]
[ SQL ] SELECT `goods` FROM `t_db_ads_recommended_datas` WHERE `vin` = 'LGBM2ME49MS505780' AND `recommend_type` = 1 [ RunTime:0.001264s ]
[ SQL ] SELECT * FROM `t_db_ads_mall_goods_referee_d` WHERE `vin` = 'LGBM2ME49MS505780' LIMIT 1 [ RunTime:0.001510s ]
[ SQL ] SELECT `goods` FROM `t_db_ads_mall_goods_referee_d` WHERE `vin` = 'LGBM2ME49MS505780' [ RunTime:0.001015s ]
[ SQL ] SELECT `sku_price` FROM `t_db_limit_discount_commodity` WHERE `limit_discount_id` IN (1775,1775,1775,1775,1775) AND `commodity_id` IN (5352,5357,9900,5338,7237) [ RunTime:0.001222s ]
[ SQL ] SELECT `commodity_id` FROM `t_db_commodity_set` WHERE ( FIND_IN_SET('GWSM', not_show_dlr) ) [ RunTime:0.022928s ]
[ SQL ] SELECT * FROM `t_bu_order` WHERE `user_id` = 159670 AND `brand` = 1 ORDER BY id desc LIMIT 1 [ RunTime:0.002161s ]
[ SQL ] SELECT `dlr_code_value` FROM `t_db_dlr_group` WHERE `id` IN (15) [ RunTime:0.001131s ]
[ SQL ] SELECT * FROM `t_db_dlr` `a` WHERE `has_ice_service_type` = 1 AND `is_enable` = 1 AND `dlr_code` IN ('B0129','B0104','B0101','B0106','B0108','B0110','B0112','B0118','B0122','B0127','B0103','B0120','B0132','B0136','B0138','********','B0137','B0139','B0150','B1036','B1039','B1043','B1046','B1051','B1052','B1007','B1017','B1031','B1034','B1037','B1048','B1053','B1056','B1032','B1011','B1060','R1020','B1065','B1062','B1068','B1061','B1071','B1072','B1070','B1073','Y1048','B1076','B1077','B1078','B1079','B1116','B1130','B1102','B1112','B1117','B1121','B1122','B1125','B1128','B1109','B1135','B1110','B1137','B1138','Y1025','Y1034','Y1033','B1139','Y1049','D2301','D2340','D2361','D2363','D2312','D2321','D2325','D2326','D2332','D2333','D2338','D2365','D2369','D2302','D2307','D2316','D2345','D2349','D2355','D2359','D2377','D2334','D2313','D2329','D2386','D2395','D2393','D2397','Y3042','Y3046','D2398','D2399','K2327','E1517','E2029','E2045','E2036','E2025','E2042','E2062','E2015','E2002','E2012','E2014','E2016','E2017','E2021','E2023','E2028','E2031','E2032','E2038','E2040','E2043','E2046','E2051','E2065','E2068','E2069','E2071','E2072','E2073','E2077','E2080','E2037','D2319','E2063','E2081','D2322','E2070','E2089','E2090','Z2041','Z2032','E2009','E2095','E2096','E2010','E2098','Z2054','Z2056','V2040','E2099','L2501290','Z2060','M2013','M2011','E1519','E1537','E1568','E1586','E1512','E1515','E1516','E1518','E1525','E1526','E1528','E1530','E1531','E1534','E1535','E1536','E1538','E1542','E1543','E1546','E1547','E1548','E1555','E1559','E1562','E1567','E1569','E1571','E1577','E1579','E1580','E1582','E1527','E1585','E1565','E1513','E1523','E1541','E1590','R1552','Z1558','E1591','E1592','R1537','E1595','E1596','Y3038','E1597','E1510','E1509','E1599','Y3058','Y3059','E1575','Z1579','P1516','P1518','P1519','G2920','G2921','G2927','H2972','H2980','H2981','H2988','H2902','H2936','H2918','H2953','H2990','H2966','H2959','H2939','H2950','H2901','H2992','G2925','G2937','H2908','H2913','H2916','H2917','H2920','H2922','H2928','H2929','H2933','H2937','H2941','H2945','H2956','H2958','H2961','H2963','H2965','H2973','H2978','H2979','H2985','H2999','G2933','H2942','H2991','G2932','G2935','G2953','G2948','G2928','H2969','H2977','G2952','G2959','Z2957','G2965','Z2956','G2962','G2950','G2957','G2963','Z2948','Z2943','G2968','G2970','G2971','G2973','G2975','Z2977','G2979','Z2981','G2976','G2969','G2977','G2981','Z2985','G2986','Z2988','Y4071','G2987','G2988','G2983','G2985','G2989','Z2991','G2990','Z2995','G2992','Y4091','Y4093','Z2996','Z2997','Z2999','G2993','G2998','G2991','G2995','G2999','Z1903','S2929','Z1904','G3101','G3102','G3103','G3111','G3113','G3105','G3119','G3120','G3118','J0402','J0407','J0408','J0405','J0406','J0412','J0415','J0416','J0413','J0417','J1841','J1834','J1815','J1843','J1830','J1805','J1847','J1818','J1837','J1842','J1846','J1832','J1806','J1826','J1848','J1845','J1850','J1813','J1819','J1820','J1831','J1852','J1853','J1855','J1857','J1856','J1858','J1860','J2605','J2620','J2601','J2614','J2615','J2617','J2618','J2619','J2611','J2606','J2706','J2730','J2727','J2720','J2713','J2731','J2707','J2732','J2708','J2735','J2736','G2829','G2820','G2815','G2813','G2823','G2805','G2806','G2807','G2818','G2812','G2825','G2833','G2803','G2840','R2813','G2842','G2845','G2810','Z2839','Y3044','G2847','G2852','L2501473','G2862','G2863','A0515','A0520','A0526','A0507','A0521','A0525','A0529','A0533','A0532','A0510','A0535','Y2003','A0537','A0536','A0602','A0611','A0612','A0621','A0608','A0610','A0626','A0906','A0910','A0913','A0916','A0925','A0915','A0902','A0908','A0909','A0921','A0922','A0923','A0927','A0937','A0912','A0918','A0926','A0917','A0941','A0942','A0943','A0947','Z0916','Z0925','A0948','B0205','B0207','B0209','B0210','B0217','B0218','B0219','B0711','B0713','B0717','B0722','B0726','B0727','B0712','B0719','B0729','B0705','B0706','B0735','B0736','B0737','B0738','C0801','C0802','C0803','C0811','C0813','C0817','C0818','C0805','C0812','C0815','C0816','C0820','C0822','C0825','C0806','C1201','C1202','C1216','C1303','C1301','C1315','C1215','C1313','C1316','C1305','C1321','C1322','C1323','C1306','C1325','C1326','C1401','C1403','Y6011','C1702','C1713','C1721','C1722','C1703','C1715','C1723','C1725','C1726','C1727','C1729','C1733','D0209','D0222','D0223','D0227','D0228','D0231','D0230','D0232','E2101','E2113','E2120','E2105','E2125','E2127','E2138','E2139','E2140','Z2120','E2142','E2143','Z2121','F1602','F1605','F1606','F1607','F1613','F1619','F1623','F1624','F1627','F1628','F1629','F1630','F1631','F1637','F1638','F1639','F1640','F1650','F1625','F1632','F1655','F1601','F1608','F1618','F1626','F1642','F1610','F1653','F1633','F1635','F1636','F1651','F1656','F1657','F1658','Z1664','Y4076','Y4075','Y4074','F2202','F2205','F2206','F2208','F2211','F2212','F2216','F2218','F2219','F2220','F2221','F2222','F2223','F2225','F2226','F2227','F2228','F2229','F2201','F2239','F2233','F2236','F2240','F2245','F2246','F2401','F2405','F2412','F2413','F2415','F2418','F2421','F2426','F2431','F2435','F2411','F2417','F2419','F2430','F2432','F2436','F2437','F2440','F2439','F2501','F2505','F2507','F2508','F2510','F2512','F2513','F2516','F2518','F2521','F2522','F2524','F2529','F2530','F2519','F2515','F2503','F2520','F2531','F2502','F2509','F2532','F2536','F2533','F2535','F2543','F2546','F2547','F2548','F2550','F2549','G3012','G3019','G3026','G3003','G3006','G3008','G3022','G3023','G3020','G3028','G3029','G3031','G3009','G3033','G3035','G3010','G3037','G3038','J1901','J1903') [ RunTime:0.038166s ]
[ SQL ] SELECT `dlr_code`,`dlr_name` FROM `t_db_dlr` WHERE `dlr_code` = 'H2901' AND `is_enable` = 1 AND `brand_type` = 1 LIMIT 1 [ RunTime:0.001139s ]
[ SQL ] SELECT `b`.`city_type`,`a`.`dlr_name`,`b`.`brand_city_type`,`a`.`dlr_code` FROM `t_db_dlr` `a` INNER JOIN `t_db_area` `b` ON `a`.`area_id`=`b`.`area_id` WHERE `a`.`dlr_code` = 'H2901' AND `a`.`is_enable` = 1 LIMIT 1 [ RunTime:0.001146s ]
[ SQL ] INSERT INTO `t_db_log` (`type` , `is_success` , `send_note` , `receive_note`) VALUES ('package' , 'success' , '{\"vin\":\"LGBM2ME49MS505780\",\"k\":\"\"}' , '{\"extInfo\":null,\"msg\":null,\"result\":\"1\",\"rows\":{\"carAge\":\"3\"}}') [ RunTime:0.002368s ]
[ SQL ] INSERT INTO `t_db_log` (`type` , `is_success` , `send_note` , `receive_note`) VALUES ('packagesb' , 'success' , 'LGBM2ME49MS505780' , '{\"msg\":null,\"pageindex\":0,\"pages\":0,\"records\":0,\"result\":\"1\",\"rows\":[{\"leftTimes\":0,\"maintainLeftTimes\":0,\"maintainOilLitre\":\"5\",\"maintainOilType\":\"ASOIL\",\"maintainTotalQty\":2,\"oilGroupType\":\"C\",\"productId\":\"0F30102EDE41B3B1E06341F01CACF368\",\"upgradeMaintainCount\":0,\"usedQty\":2}]}') [ RunTime:0.001361s ]
[ SQL ] SET SESSION group_concat_max_len=100000; [ RunTime:0.001466s ]
[ SQL ] SELECT COUNT(*) AS tp_count FROM `t_db_commodity_flat` `a` WHERE `a`.`is_enable` = 1 AND ( (find_in_set('GWSM',a.up_down_channel_dlr)) ) AND ( a.commodity_id in (5352,5357,9900,5338,7237) ) LIMIT 1 [ RunTime:0.001661s ]
[ DB ] CONNECT:[ UseTime:0.001650s ] mysql:dbname=dealer;host=*************;port=3306;charset=utf8
[ SQL ] SELECT `a`.`commodity_id`,`a`.`commodity_name`,`a`.`tag`,`a`.`tag_gwnet`,`a`.`tag_gwapp`,`a`.`is_pure`,`a`.`cover_image`,`a`.`card_id`,`b`.`count_stock`,`a`.`sales_channel`,`a`.`cheap_dis`,`a`.`group_dis`,`a`.`full_dis`,`a`.`limit_dis`,`a`.`seckill_dis`,`a`.`n_dis`,`a`.`pre_dis`,`a`.`car_series_id`,min(c.price) price,min(c.price) final_price,`b`.`max_point`,`b`.`pay_style`,`a`.`tag_pz1asm`,`a`.`tag_pz1aapp`,`a`.`tag_qcsm`,`a`.`tag_qcapp`,`a`.`is_grouped`,`b`.`commodity_label`,GROUP_CONCAT(c.id) gc_id,GROUP_CONCAT(c.price) gc_price,`b`.`group_commodity_ids_info`,`b`.`is_sp_associated`,`a`.`commodity_set_id`,`b`.`qsc_group`,`b`.`qsc_group_price`,`b`.`first_free_price`,`b`.`qsc_group_num`,`b`.`qsc_group_name`,`a`.`gift_dis`,`a`.`dd_commodity_type`,`b`.`is_store`,`a`.`comm_type_id`,GROUP_CONCAT( c.stock ) gc_stock,`c`.`relate_car_ids`,`b`.`listing_type`,`a`.`activity_image`,`a`.`comm_type_id_str`,`b`.`tag_zdy`,b.mail_type mail_method FROM `t_db_commodity_flat` `a` INNER JOIN `t_db_commodity_set` `b` ON `a`.`commodity_set_id`=b.id and b.is_enable=1 LEFT JOIN `t_db_commodity_card` `card_c` ON `a`.`commodity_id`=card_c.commodity_id and card_c.sorts>0 LEFT JOIN `t_db_card` `cards` ON `card_c`.`card_id`=cards.id and cards.is_enable=1 and cards.validity_date_start > now() and cards.validity_date_end <= NOW() INNER JOIN `t_db_commodity_set_sku` `c` ON `c`.`commodity_set_id`=b.id and c.is_enable=1 INNER JOIN `t_db_commodity_sku` `d` ON `d`.`id`=c.commodity_sku_id and d.is_enable=1 WHERE `c`.`id` IN (214136,214137,214138,214140,214141,214142,214143,214146,214148,214149,214150,214151,214152,214153,214157,214158,214159,214160,214161,214163,214166,214167,214169,214199,214200,214201,214202,214203,214204,214205,214206,214207,214208,214209,214210,214211,214212,214213,214214,214215,214216,214217,214218,214219,214220,214221,214222,214223,214224,214225,214226,214227,214228,214229,214230,214231,214232,214233,214234,214235,214236,214237,214238,214239,214240,214241,214242,214243,214244,214245,214246,214247,214248,214249,214250,214251,214252,214253,214254,214255,214256,214257,214258,214259,214260,214261,214262,214263,214264,214265,214266,214267,214268,214269,214270,214271,214272,214273,214274,214326,214327,214328,214329,214330,214331,214332,214333,214334,214335,214336,214337,214338,214339,214340,214341,214342,214343,214344,214357,214358,214359,214360,214361,214362,214363,214364,214365,214366,214367,214368,214369,214370,214371,214372,214373,214374,214375,214376,214377,214378,214379,214380,214381,214382,214383,214384,214388,214389,214390,214391,214392,214393,214521,214522,214524,214525,214526,214527,214528,214529,214530,214531,214532,214539,214540,214541,214542,214543,214544,214545,214546,214547,214548,214549,214554,214555,214556,214557,214558,214559,214560,214561,214562,214563,214564,214565,214566,214567,214568,214570,214571,214572,214573,214574,214575,214576,214577,214578,214579,214580,325261,325262,325263,325264,325265,325266,325267,325268,333865,333866,118753,118754,118756,118757,118839,118840,118841,118842,118843,118844,118851,118852,118853,118854,118855,118856,118858,118859,118864,118865,118866,125827,324301,324302,324303,324304,324308,324309,324310,324311,324315,324316,324317,324318,324319,324320,324321,324322,324323,324324,324325,324330,324331,324332,324333,324334,324335,324336,324337,324338,324339,324340,324345,324346,324347,324348,324349,324350,324351,324352,324353,324360,324361,324362,324363,324364,324365,324366,324367,324368,324375,324376,324378,324379,324381,324382,324384,324385,324387,324388,324390,324391,324393,324394,324395,324396,324397,324398,324399,324400,324401,324402,324403,324404,324405,324406,324407,324408,324409,324410,324411,324412,324413,324414,324415,324416,324417,324418,324419,324421,324422,324423,324424,324425,324426,324428,324429,324430,324431,324432,324433,324435,324436,324437,324438,324439,324440,324442,324443,322319,322320,322321,322322,322323,322324,322325,322326,322328,322364,322365,322366,322368,322369,322371,322395,322397,322398,322403,322409,322410,322411,322412,322413,322414,322415,322416,322417,322418,322420,322422,322423,322455,322456,322458,322459,322460,322461,322462,322463,322465,322467,322473,322474,322480,322481,322483,322484,324590,324591,324593,324595,324597,324598,324600,326692,326695,326696,326708,326709,326710,326711,326712,326714,333680,333681,333682,333683,333684,333685,333686,352137,352138,352139,354542,316743,316744,316745,316746,316747,316748,316749,316750,316751,316752,316753,316754,316755,316756,316757,316758,316759,316760,316761,316762,316763,316764,316765,316766,316767,316768,316769,316770,316771,316772,316773,316774,316775,316776,316777,316785,316786,316787,316788,316797,316798,316799,316800,316801,316802,316803,316804,316805,316806,316807,316808,316809,316810,316811,316812,316813,316814,316815,316816,316817,316818,316819,316820,316821,316822,316823,316824,316825,316826,316827,316828,316829,316830,316831,316832,316834,316838,348930,348931,348932,348933,348934,348935,348936,348937,348938,348939,348940,348941,348942,348943,348944,348945,348946,348947,348948,348949,348950,348951,348952,348953,348954,348955,348956,348957,348958,348959,348960,348961,348962,348963,348993,348994,348995,348996,348997,349000,349001,349002,349003,349004,349005,349007,349008,349009,349010,349011,349012,349013,349014,349015,349016,349017,349018,349019,349020,349021,349022,349023,349024,349025,349039,349040,349041,349042,349043,349044,349045,349046,349047,349048,349049,349050,349051,349056,349057,349059,349060,349061,349062,349063,349064,349065,349066,349067,349068,349069,349070,349071,349072,349073,349074,349075,349076,351836,351837,354884,354885,90540,90541,90543,90544,90545,90546,90547,90549,90555,90556,90557,90570,90581,90596,90597,90599,90603,90604,90605,90611,90614,90615,90617,90620,90622,90623,95728,95729,95730,95731,118653,118654,118661,157893,157894,208500,208501,208502,208503,208504,208505,208506,208507,208523,208524,208602,208663,208664,208728,208760,333661,333662,336463,352089,352090,352115,354543,354877,354878,354879,354880,354881) AND `a`.`is_enable` = 1 AND `c`.`is_enable` = 1 AND ( (find_in_set('GWSM',a.up_down_channel_dlr)) ) AND ( a.commodity_id in (5352,5357,9900,5338,7237) ) AND ( (find_in_set('A',d.city_type) || d.city_type='' || d.city_type is null ) ) AND ( (find_in_set('H2901',d.relate_dlr_code) || d.relate_dlr_code='' || d.relate_dlr_code is null ) ) AND ( d.oli_liters in (5) || d.oli_liters ='' ) AND ( ((d.maintain_q='8.0' and a.dd_commodity_type=3 ) || (d.maintain_q='7.0' and a.dd_commodity_type=12 ) || a.dd_commodity_type not in (3,12) ) ) AND ( ((find_in_set(8573,d.relate_car_ids) || d.relate_car_ids='' || d.relate_car_ids is null) ) ) AND ( ((d.part_start_time<='2021/08' || d.part_start_time ='') and (d.part_end_time>='2021/08' || d.part_end_time ='') ) ) AND ( ( (d.maintain_num<=0 || d.maintain_num='') || a.dd_commodity_type <> 4 ) ) AND ( ((b.listing_type=2 and a.crowdfund_dis like '%GWSM%' )|| b.listing_type=1) ) GROUP BY a.commodity_id ORDER BY field(a.commodity_id, 5352,5357,9900,5338,7237),card_c.sorts desc,a.last_updated_date asc LIMIT 0,5 [ RunTime:0.010489s ]
[ SQL ] SELECT `flat`.`commodity_set_id`,`flat`.`commodity_id`,a.group_sub_commodity_id sub_goods_id,`a`.`set_sku_ids`,`a`.`group_card_type`,`a`.`class_id`,`b`.*,`a`.`card_id`,`act`.`receive_coupon_points`,`a`.`group_sub_commodity_id`,b.card_name name,`flat`.`dd_commodity_type`,`act`.`push_type`,`act`.`select_obj` FROM `t_db_commodity_flat` `flat` INNER JOIN `t_db_commodity_card` `a` ON `a`.`commodity_set_id`=`flat`.`commodity_set_id` INNER JOIN `t_db_card` `b` ON `b`.`id`=a.card_id and ((( b.car_config_code IS NOT NULL || b.car_config_code <> '' ) AND flat.dd_commodity_type > 0 ) || ( b.car_config_code IS NULL || b.car_config_code = '' )) LEFT JOIN `t_db_activity` `act` ON `b`.`activity_id`=act.activity_id and FIND_IN_SET( 'GWSM', act.up_down_channel_dlr ) and (act.select_obj=1 || (act.select_obj in (2,3) and flat.dd_commodity_type>0)) WHERE `b`.`is_enable` = 1 AND `a`.`is_enable` = 1 AND `b`.`receive_start_date` <= '2025-08-26' AND `b`.`validity_date_end` >= '2025-08-26' AND `flat`.`commodity_set_id` IN (10105,16708,10092,12819) AND ( FIND_IN_SET('GWSM',b.up_down_channel_dlr) ) AND `a`.`card_id` IN ('*****************','*****************','*****************') ORDER BY b.created_date desc [ RunTime:0.002829s ]
[ SQL ] SELECT * FROM `t_db_user_car_series` `a` WHERE `user_id` = 159670 AND `is_enable` = 1 AND `car_brand_code` = 1 [ RunTime:0.001341s ]
[ SQL ] INSERT INTO `t_db_activity_center_log` (`request_id` , `oneid` , `request_url` , `request_info`) VALUES ('getMallActivityCouponList' , '97c16d435e538d0291ae6363f54568e06591' , '/activity-center-middle-service/middle/middle/v1/open/getMallActivityCouponList' , '{\"activityIdList\":\"1940204165395484674,1938550374269915137\",\"couponIdList\":\"1423967,1423886\",\"oneid\":\"97c16d435e538d0291ae6363f54568e06591\",\"mallChannel\":\"mini_app\",\"vinList\":\"LGBM2ME49MS505780\",\"brand\":1,\"goods_set_id\":[10105,16708,10092,12819],\"from_mall\":\"special\",\"from_user_id\":159670}') [ RunTime:0.003094s ]
[ SQL ] UPDATE `t_db_activity_center_log` SET `response_info`='{\"result\":\"1\",\"msg\":\"操作成功\",\"extInfo\":null,\"rows\":[{\"activityId\":\"1938550374269915137\",\"couponId\":\"1423886\",\"couponTitle\":\"20元商城代金券\",\"vinIntersection\":null,\"receiveDataList\":[{\"vin\":null,\"canReceive\":1,\"received\":0,\"oneid\":\"97c16d435e538d0291ae6363f54568e06591\"}],\"oneid\":\"97c16d435e538d0291ae6363f54568e06591\"}]}' WHERE `id` = 27104788 [ RunTime:0.001582s ]
[ SQL ] SELECT `rec`.`card_id`,count(1) cc FROM `t_bu_card_receive_record` `rec` INNER JOIN `t_db_card` `a` ON `a`.`id`=`rec`.`card_id` WHERE `rec`.`card_id` IN ('*****************','*****************','*****************','*****************','*****************','*****************','*****************') AND `rec`.`is_enable` = 1 AND ( rec.user_id=159670 or rec.receive_vin='LGBM2ME49MS505780' ) AND ( rec.status in (1,3,5) or (rec.status in (1,3,5,7) and a.is_gift_card=1) ) GROUP BY rec.card_id [ RunTime:0.006026s ]
[ SQL ] SELECT `rec`.*,min(rec.validity_date_start) min_start,min(rec.validity_date_end) min_end,count(rec.card_id) cc,GROUP_CONCAT(rec.status) g_status FROM `t_bu_card_receive_record` `rec` INNER JOIN `t_db_card` `a` ON `a`.`id`=`rec`.`card_id` LEFT JOIN `t_db_activity` `act` ON `act`.`activity_id`=rec.activity_id and rec.activity_id>0 WHERE `rec`.`card_id` IN ('*****************','*****************','*****************') AND `rec`.`is_enable` = 1 AND `rec`.`validity_date_end` >= '2025-08-26 16:35:32' AND ( (rec.receive_vin='LGBM2ME49MS505780' ) || (rec.user_id='159670' ) ) AND ( rec.status=1 ) AND ( (rec.activity_id>0 and FIND_IN_SET('GWSM',act.up_down_channel_dlr)) or (rec.activity_id=0 and FIND_IN_SET('GWSM',a.up_down_channel_dlr)) ) GROUP BY rec.card_id ORDER BY validity_date_end asc [ RunTime:0.002466s ]
[ SQL ] SELECT `a`.`id`,`a`.`sp_value_list`,`b`.`price`,`b`.`id`,b.id bid,`a`.`sku_code`,`a`.`variety_code`,`a`.`maintain_q`,`b`.`commodity_id` FROM `t_db_commodity_sku` `a` INNER JOIN `t_db_commodity_set_sku` `b` ON `a`.`id`=b.commodity_sku_id and a.is_enable=1 and b.is_enable=1 WHERE `b`.`id` IN ('208728','316831','214573','324439','324442') GROUP BY b.commodity_id,a.id ORDER BY b.price DESC,a.id DESC,b.id DESC [ RunTime:0.001267s ]
[ SQL ] SELECT `commodity_id` FROM `t_db_commodity_set` WHERE ( FIND_IN_SET('GWSM', not_show_dlr) ) [ RunTime:0.025450s ]
[ SQL ] SET SESSION group_concat_max_len=100000; [ RunTime:0.000992s ]
[ SQL ] SELECT COUNT(*) AS tp_count FROM `t_db_commodity_flat` `a` WHERE `a`.`is_enable` = 1 AND ( (find_in_set('GWSM',a.up_down_channel_dlr)) ) AND ( a.commodity_id in (9900,5352,5357,10332,10330,10329,9680,10334,9472,5993,5998,5331,5332,5336,5339,5360,5361,5913,5356,5616,5347,5345,5351,5990,5341,10108,10107,10106,10105) ) LIMIT 1 [ RunTime:0.001525s ]
[ SQL ] SELECT `a`.`commodity_id`,`a`.`commodity_name`,`a`.`tag`,`a`.`tag_gwnet`,`a`.`tag_gwapp`,`a`.`is_pure`,`a`.`cover_image`,`a`.`card_id`,`b`.`count_stock`,`a`.`sales_channel`,`a`.`cheap_dis`,`a`.`group_dis`,`a`.`full_dis`,`a`.`limit_dis`,`a`.`seckill_dis`,`a`.`n_dis`,`a`.`pre_dis`,`a`.`car_series_id`,min(c.price) price,min(c.price) final_price,`b`.`max_point`,`b`.`pay_style`,`a`.`tag_pz1asm`,`a`.`tag_pz1aapp`,`a`.`tag_qcsm`,`a`.`tag_qcapp`,`a`.`is_grouped`,`b`.`commodity_label`,GROUP_CONCAT(c.id) gc_id,GROUP_CONCAT(c.price) gc_price,`b`.`group_commodity_ids_info`,`b`.`is_sp_associated`,`a`.`commodity_set_id`,`b`.`qsc_group`,`b`.`qsc_group_price`,`b`.`first_free_price`,`b`.`qsc_group_num`,`b`.`qsc_group_name`,`a`.`gift_dis`,`a`.`dd_commodity_type`,`b`.`is_store`,`a`.`comm_type_id`,GROUP_CONCAT( c.stock ) gc_stock,`c`.`relate_car_ids`,`b`.`listing_type`,`a`.`activity_image`,`a`.`comm_type_id_str`,`b`.`tag_zdy`,b.mail_type mail_method FROM `t_db_commodity_flat` `a` INNER JOIN `t_db_commodity_set` `b` ON `a`.`commodity_set_id`=b.id and b.is_enable=1 LEFT JOIN `t_db_commodity_card` `card_c` ON `a`.`commodity_id`=card_c.commodity_id and card_c.sorts>0 LEFT JOIN `t_db_card` `cards` ON `card_c`.`card_id`=cards.id and cards.is_enable=1 and cards.validity_date_start > now() and cards.validity_date_end <= NOW() INNER JOIN `t_db_commodity_set_sku` `c` ON `c`.`commodity_set_id`=b.id and c.is_enable=1 INNER JOIN `t_db_commodity_sku` `d` ON `d`.`id`=c.commodity_sku_id and d.is_enable=1 WHERE `a`.`is_enable` = 1 AND `c`.`is_enable` = 1 AND ( (find_in_set('GWSM',a.up_down_channel_dlr)) ) AND ( a.commodity_id in (9900,5352,5357,10332,10330,10329,9680,10334,9472,5993,5998,5331,5332,5336,5339,5360,5361,5913,5356,5616,5347,5345,5351,5990,5341,10108,10107,10106,10105) ) AND ( (find_in_set('A',d.city_type) || d.city_type='' || d.city_type is null ) ) AND ( (find_in_set('H2901',d.relate_dlr_code) || d.relate_dlr_code='' || d.relate_dlr_code is null ) ) AND ( d.oli_liters in (5) || d.oli_liters ='' ) AND ( ((d.maintain_q='8.0' and a.dd_commodity_type=3 ) || (d.maintain_q='7.0' and a.dd_commodity_type=12 ) || a.dd_commodity_type not in (3,12) ) ) AND ( ((find_in_set(8573,d.relate_car_ids) || d.relate_car_ids='' || d.relate_car_ids is null) ) ) AND ( ((d.part_start_time<='2021/08' || d.part_start_time ='') and (d.part_end_time>='2021/08' || d.part_end_time ='') ) ) AND ( ( (d.maintain_num<=0 || d.maintain_num='') || a.dd_commodity_type <> 4 ) ) AND ( ((b.listing_type=2 and a.crowdfund_dis like '%GWSM%' )|| b.listing_type=1) ) GROUP BY a.commodity_id ORDER BY field(a.commodity_id, 9900,5352,5357,10332,10330,10329,9680,10334,9472,5993,5998,5331,5332,5336,5339,5360,5361,5913,5356,5616,5347,5345,5351,5990,5341,10108,10107,10106,10105),card_c.sorts desc,a.last_updated_date asc LIMIT 0,29 [ RunTime:0.506482s ]
[ SQL ] SELECT `flat`.`commodity_set_id`,`flat`.`commodity_id`,a.group_sub_commodity_id sub_goods_id,`a`.`set_sku_ids`,`a`.`group_card_type`,`a`.`class_id`,`b`.*,`a`.`card_id`,`act`.`receive_coupon_points`,`a`.`group_sub_commodity_id`,b.card_name name,`flat`.`dd_commodity_type`,`act`.`push_type`,`act`.`select_obj` FROM `t_db_commodity_flat` `flat` INNER JOIN `t_db_commodity_card` `a` ON `a`.`commodity_set_id`=`flat`.`commodity_set_id` INNER JOIN `t_db_card` `b` ON `b`.`id`=a.card_id and ((( b.car_config_code IS NOT NULL || b.car_config_code <> '' ) AND flat.dd_commodity_type > 0 ) || ( b.car_config_code IS NULL || b.car_config_code = '' )) LEFT JOIN `t_db_activity` `act` ON `b`.`activity_id`=act.activity_id and FIND_IN_SET( 'GWSM', act.up_down_channel_dlr ) and (act.select_obj=1 || (act.select_obj in (2,3) and flat.dd_commodity_type>0)) WHERE `b`.`is_enable` = 1 AND `a`.`is_enable` = 1 AND `b`.`receive_start_date` <= '2025-08-26' AND `b`.`validity_date_end` >= '2025-08-26' AND `flat`.`commodity_set_id` IN (16708,10105,17609,17608,16381,17604,16287,11188,10085,10086,10090,10093,10107,10108,11050,10102,10555,10097,10096,10100,11162,10094,17365,17364,17363,17362) AND ( FIND_IN_SET('GWSM',b.up_down_channel_dlr) ) AND `a`.`card_id` IN ('*****************','*****************','*****************','50525337925223424','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','1547','32769882217022464','*****************') ORDER BY b.created_date desc [ RunTime:0.012818s ]
[ SQL ] SELECT * FROM `t_db_user_car_series` `a` WHERE `user_id` = 159670 AND `is_enable` = 1 AND `car_brand_code` = 1 [ RunTime:0.001610s ]
[ SQL ] INSERT INTO `t_db_activity_center_log` (`request_id` , `oneid` , `request_url` , `request_info`) VALUES ('getMallActivityCouponList' , '97c16d435e538d0291ae6363f54568e06591' , '/activity-center-middle-service/middle/middle/v1/open/getMallActivityCouponList' , '{\"activityIdList\":\"1943598089448116226,1940204165395484674,1939648643423674370,1939647561438134274,1939727687954219010,1939646267094290434,1939645069510135809,1939644289695252482,1939643287935332354,1939642564973092865,1939641755244138497,1939640997912350722,1938550374269915137,1939598280020873217\",\"couponIdList\":\"1423991,1423967,1423901,1423900,1423899,1423898,1423897,1423896,1423895,1423894,1423893,1423892,1423886,1423885\",\"oneid\":\"97c16d435e538d0291ae6363f54568e06591\",\"mallChannel\":\"mini_app\",\"vinList\":\"LGBM2ME49MS505780\",\"brand\":1,\"goods_set_id\":[16708,10105,17609,17608,16381,17604,16287,11188,10085,10086,10090,10093,10107,10108,11050,10102,10555,10097,10096,10100,11162,10094,17365,17364,17363,17362],\"from_mall\":\"goodslist\",\"from_user_id\":159670}') [ RunTime:0.009279s ]
[ SQL ] UPDATE `t_db_activity_center_log` SET `response_info`='{\"result\":\"1\",\"msg\":\"操作成功\",\"extInfo\":null,\"rows\":[{\"activityId\":\"1938550374269915137\",\"couponId\":\"1423886\",\"couponTitle\":\"20元商城代金券\",\"vinIntersection\":null,\"receiveDataList\":[{\"vin\":null,\"canReceive\":1,\"received\":0,\"oneid\":\"97c16d435e538d0291ae6363f54568e06591\"}],\"oneid\":\"97c16d435e538d0291ae6363f54568e06591\"}]}' WHERE `id` = 27104789 [ RunTime:0.002178s ]
[ SQL ] SELECT `rec`.`card_id`,count(1) cc FROM `t_bu_card_receive_record` `rec` INNER JOIN `t_db_card` `a` ON `a`.`id`=`rec`.`card_id` WHERE `rec`.`card_id` IN ('50525337925223424','50525337925223424','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','32769882217022464','1547') AND `rec`.`is_enable` = 1 AND ( rec.user_id=159670 or rec.receive_vin='LGBM2ME49MS505780' ) AND ( rec.status in (1,3,5) or (rec.status in (1,3,5,7) and a.is_gift_card=1) ) GROUP BY rec.card_id [ RunTime:0.005013s ]
[ SQL ] SELECT `rec`.*,min(rec.validity_date_start) min_start,min(rec.validity_date_end) min_end,count(rec.card_id) cc,GROUP_CONCAT(rec.status) g_status FROM `t_bu_card_receive_record` `rec` INNER JOIN `t_db_card` `a` ON `a`.`id`=`rec`.`card_id` LEFT JOIN `t_db_activity` `act` ON `act`.`activity_id`=rec.activity_id and rec.activity_id>0 WHERE `rec`.`card_id` IN ('50525337925223424','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','*****************','32769882217022464','1547') AND `rec`.`is_enable` = 1 AND `rec`.`validity_date_end` >= '2025-08-26 16:35:33' AND ( (rec.receive_vin='LGBM2ME49MS505780' ) || (rec.user_id='159670' ) ) AND ( rec.status=1 ) AND ( (rec.activity_id>0 and FIND_IN_SET('GWSM',act.up_down_channel_dlr)) or (rec.activity_id=0 and FIND_IN_SET('GWSM',a.up_down_channel_dlr)) ) GROUP BY rec.card_id ORDER BY validity_date_end asc [ RunTime:0.004638s ]
[ SQL ] SELECT `a`.`id`,`a`.`sp_value_list`,`b`.`price`,`b`.`id`,b.id bid,`a`.`sku_code`,`a`.`variety_code`,`a`.`maintain_q`,`b`.`commodity_id` FROM `t_db_commodity_sku` `a` INNER JOIN `t_db_commodity_set_sku` `b` ON `a`.`id`=b.commodity_sku_id and a.is_enable=1 and b.is_enable=1 WHERE `b`.`id` IN ('316831','208728','341813','341688','341517','341614','341090','355182','355245','355357','355363','355394','355426','355453','353703','353953','354257','354276','354358','354420','354476','323616','329619','323578','323687','323706','125701','319272','182047','321995','53440','336548','43627','39930','39929','39933','142561','54473','327905','327906','328003','327851','327852','327626','327842','327850','329746','327217','327241','328550','336549') GROUP BY b.commodity_id,a.id ORDER BY b.price DESC,a.id DESC,b.id DESC [ RunTime:0.003689s ]
[ SQL ] SELECT * FROM `t_db_gift` WHERE `id` = 102 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001045s ]
[ SQL ] SELECT * FROM `t_db_gift_commodity` WHERE `gift_id` = 102 AND `commodity_id` = 10330 AND `is_gift` = 0 LIMIT 1 [ RunTime:0.000981s ]
[ SQL ] SELECT * FROM `t_db_gift` WHERE `id` = 101 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001148s ]
[ SQL ] SELECT * FROM `t_db_gift_commodity` WHERE `gift_id` = 101 AND `commodity_id` = 10329 AND `is_gift` = 0 LIMIT 1 [ RunTime:0.000910s ]
[ SQL ] SELECT `group_sub_set_sku_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (341517,341614) [ RunTime:0.001005s ]
[ SQL ] SELECT `group_sub_commodity_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (341517,341614) [ RunTime:0.000985s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (341270,341271,341272,341273,341226,341227,341228,341229,341230,341231,341232,341233,341234,341235,341236,341237,341238,341239,341240,341241,341242,341243,341274,341275,341276,341277,341279,341280,341281,341282,341283,341284,341285,341286,341287,341288,341289,341290,341291,341292,341259,341260,341261,341262,341263,341264,341265,341266,341267,341268,341269,341300,341301,341302,341303,341304,341305,341306,341309,341310,341244,341245,341293,341294,341295,341296,341246,341247,341248,341249,341250,341251,341307,341308,341297,341298,341299,341190,341191,341192,341256,341257,341258,341252,341253,341254,341278,341312,341311,341255,341193,341194,341195,341196,341197,341198) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001530s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (341408,341409,341353,341354,341355,341356,341357,341358,341359,341360,341361,341398,341399,341400,341401,341374,341375,341376,341377,341378,341402,341403,341404,341405,341406,341407,341415,341416,341417,341418,341423,341424,341379,341380,341382,341383,341419,341420,341421,341422,341384,341385,341386,341387,341388,341389,341429,341430,341362,341363,341364,341365,341366,341367,341368,341369,341370,341371,341372,341373,341410,341411,341412,341413,341414,341425,341426,341427,341394,341395,341396,341390,341391,341392,341397,341381,341432,341431,341393,341428,343289) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001533s ]
[ SQL ] SELECT `set_a`.`stock`,`c`.`sp_value_list`,`a`.`price`,a.price min_price,a.id set_sku_id,`a`.`commodity_id`,`a`.`group_sub_commodity_id`,`b`.`commodity_name`,`c`.`image`,`c`.`relate_car_work_hour`,`b`.`work_hour_type`,`a`.`relate_car_ids`,`b`.`cover_image`,`c`.`sku_code`,c.commodity_id sku_commodity_id,`a`.`group_sub_set_sku_id` FROM `t_db_commodity_set_sku` `a` INNER JOIN `t_db_commodity` `b` ON `a`.`group_sub_commodity_id`=b.id and b.is_enable=1 INNER JOIN `t_db_commodity_set_sku` `set_a` ON `a`.`group_sub_set_sku_id`=`set_a`.`id` INNER JOIN `t_db_commodity_sku` `c` ON `set_a`.`commodity_sku_id`=c.id and c.is_enable = 1 INNER JOIN `t_db_commodity_set` `e` ON `e`.`commodity_id`=a.commodity_id and e.is_enable = 1 WHERE `a`.`commodity_id` = 9680 AND `a`.`is_enable` = 1 AND ( 1 and (c.part_start_time<='2021/08' || c.part_start_time='') and (c.part_end_time>='2021/08' || c.part_end_time ='') and (FIND_IN_SET(8573,a.relate_car_ids) || a.relate_car_ids='' || a.relate_car_ids is null) ) GROUP BY c.id ORDER BY price desc,a.id [ RunTime:0.004381s ]
[ SQL ] SELECT * FROM `t_db_commodity_spec_union` `a` WHERE `group_commodity_set_id` = 16381 [ RunTime:0.000947s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (341308) AND `stock` > 0 LIMIT 1 [ RunTime:0.000896s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (341430) AND `stock` > 0 LIMIT 1 [ RunTime:0.001034s ]
[ SQL ] SELECT * FROM `t_db_gift` WHERE `id` = 104 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001222s ]
[ SQL ] SELECT * FROM `t_db_gift_commodity` WHERE `gift_id` = 104 AND `commodity_id` = 9680 AND `is_gift` = 0 LIMIT 1 [ RunTime:0.001100s ]
[ SQL ] SELECT * FROM `t_db_n_discount` WHERE `id` = 184 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001142s ]
[ SQL ] SELECT `group_sub_set_sku_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (323687,323706) [ RunTime:0.000975s ]
[ SQL ] SELECT `group_sub_commodity_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (323687,323706) [ RunTime:0.001062s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (92486,92487,92488,323660,92437,92438,92439,92440,94559,323652,323653,92444,92445,92446,92447,92448,92449,92450,94560,94561,94562,323655,92489,92490,92491,323661,92493,92494,92495,92496,92497,92498,323663,323664,323665,323666,92500,92501,92502,337481,92471,92472,92473,92474,92475,92476,92477,92478,92479,92480,323659,92517,92518,92519,92520,92521,92522,336457,92533,323671,92459,92460,92508,92511,336456,339890,92462,92464,92465,92466,94566,323656,92532,323670,92514,92515,92516,93251,323649,323650,94567,129371,323658,170816,184021,323657,323662,323673,323672,333842) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001502s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (92346,92347,92298,92299,92300,92301,92302,94548,323630,323631,323632,92336,92337,92338,323640,92315,92316,92317,92318,323633,92339,92340,92341,323641,323642,323643,92356,92357,92358,337480,92377,336455,92370,323645,92322,323634,92323,92324,92364,92367,336454,339889,92326,92328,92329,92330,94555,323636,92389,323646,92305,92306,92307,92308,92309,92310,92312,92313,92314,94549,94550,94551,92349,92350,92351,92352,323644,92371,92372,92373,94556,129370,323639,170815,184020,323637,323638,323635,323648,323647,333843) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001368s ]
[ SQL ] SELECT `set_a`.`stock`,`c`.`sp_value_list`,`a`.`price`,a.price min_price,a.id set_sku_id,`a`.`commodity_id`,`a`.`group_sub_commodity_id`,`b`.`commodity_name`,`c`.`image`,`c`.`relate_car_work_hour`,`b`.`work_hour_type`,`a`.`relate_car_ids`,`b`.`cover_image`,`c`.`sku_code`,c.commodity_id sku_commodity_id,`a`.`group_sub_set_sku_id` FROM `t_db_commodity_set_sku` `a` INNER JOIN `t_db_commodity` `b` ON `a`.`group_sub_commodity_id`=b.id and b.is_enable=1 INNER JOIN `t_db_commodity_set_sku` `set_a` ON `a`.`group_sub_set_sku_id`=`set_a`.`id` INNER JOIN `t_db_commodity_sku` `c` ON `set_a`.`commodity_sku_id`=c.id and c.is_enable = 1 INNER JOIN `t_db_commodity_set` `e` ON `e`.`commodity_id`=a.commodity_id and e.is_enable = 1 WHERE `a`.`commodity_id` = 5336 AND `a`.`is_enable` = 1 AND ( 1 and (c.part_start_time<='2021/08' || c.part_start_time='') and (c.part_end_time>='2021/08' || c.part_end_time ='') and (FIND_IN_SET(8573,a.relate_car_ids) || a.relate_car_ids='' || a.relate_car_ids is null) ) GROUP BY c.id ORDER BY price desc,a.id [ RunTime:0.015429s ]
[ SQL ] SELECT * FROM `t_db_commodity_spec_union` `a` WHERE `group_commodity_set_id` = 10090 [ RunTime:0.001113s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (323670) AND `stock` > 0 LIMIT 1 [ RunTime:0.001107s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (323646) AND `stock` > 0 LIMIT 1 [ RunTime:0.001254s ]
[ SQL ] SELECT `group_sub_set_sku_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327905,327906,328003) [ RunTime:0.001019s ]
[ SQL ] SELECT `group_sub_commodity_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327905,327906,328003) [ RunTime:0.000980s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (92171,92172,92173,92174,92175,92176,92177,322604,91970,322630,322631,91971,91972,91973,91974,91975,91976,91977,91978,91979,91980,91981,91982,91983,91984,91985,91986,91987,91988,91989,91990,91991,91992,91993,91994,91995,91996,91997,91998,91999,92000,92001,92002,92005,92006,92007,92008,92009,92010,92011,92012,92014,92015,92017,92019,92021,96780,96781,96782,96783,118594,118595,126651,322510,322511,322512,322513,322514,322515,322516,322517,322518,322519,322520,322521,322522,322523,322524,322525,322526,322527,322528,322529,322530,322531,322532,322533,92034,92035,92036,92037,92038,92039,92040,92041,92042,92043,92044,92045,92046,92047,92048,92049,92050,92051,92052,92053,92054,92055,92056,92057,92058,92059,92060,92061,92062,92063,92064,92065,92068,92069,92070,92071,92072,92073,92074,92075,92077,92078,92080,92082,92084,96787,96788,96789,96790,118601,118602,126653,322541,322542,322543,322544,322545,322546,322547,322548,322549,322550,322551,322552,322553,322554,322555,322556,322557,322558,322559,322560,322561,322562,322563,322564,92100,92101,92102,92103,92104,92105,92106,92107,92108,92109,92110,92111,92112,92113,92114,92115,92116,92117,92118,92119,92120,92121,92122,92123,92124,92125,92126,92127,92128,92129,92130,92131,92132,92135,92136,92137,92138,92139,92140,92141,92142,92144,92145,92147,92148,92149,92151,92155,96794,96795,96796,96797,96798,118608,118609,126657,322572,322573,322574,322575,322576,322577,322578,322579,322580,322581,322582,322583,322584,322585,322586,322587,322588,322589,322590,322591,322592,106774,106775,106776,106777,106779,106780,106781,106782,106783,106784,118617,118618,157929,157930,180432,322616,322617,322618,322619,322620,329344,329345,329346,329347,329348,329349,329350,329351,329352,329353,329354,329355,329359,329360,329361,329362,329363,329364,329365,329366,329367,329368,329369,329370,329371,329374,329375,329376,329377,329378,329379,329381,329382,329383,329384,329385,329386,335430,335431,335432,335433,329389,329390,329391,329392,329393,329394,329395,329396,329397,338304,338305,338306,338307,338308,338309,338310,338311,338312,338313,338314,338315,338316,338317,338318,338319,338320,338321,338322,338323,338324,338325,338326,338327,338328,338329,338330,338331,338332,338333,338334,338335,338336,338337,338338,338339,338340,338341,338342,338343,338344,338345,338346,338347,338348,338349,338350,338351,338352,338353,338354,338355,338356,338357,338358,338359,338360) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.004045s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (54473) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.002470s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (39930) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001398s ]
[ SQL ] SELECT min(price) ff_price,`group_sub_commodity_id`,GROUP_CONCAT(group_sub_set_sku_id) sub_sku_ids,`relate_car_ids`,GROUP_CONCAT(id) set_sku_ids FROM `t_db_commodity_set_sku` `a` WHERE `commodity_id` = 10108 AND `is_enable` = 1 AND `id` IN (327905,327906,328003) AND ( (find_in_set(8573,relate_car_ids) || relate_car_ids='') ) GROUP BY group_sub_commodity_id [ RunTime:0.002077s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (322630) AND `stock` > 0 LIMIT 1 [ RunTime:0.001149s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (54473) AND `stock` > 0 LIMIT 1 [ RunTime:0.001122s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (39930) AND `stock` > 0 LIMIT 1 [ RunTime:0.001167s ]
[ SQL ] SELECT * FROM `t_db_limit_discount` WHERE `id` = 1735 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001569s ]
[ SQL ] SELECT * FROM `t_db_limit_discount_commodity` WHERE `limit_discount_id` = 1735 AND `commodity_id` = 10108 LIMIT 1 [ RunTime:0.001470s ]
[ SQL ] SELECT `group_sub_set_sku_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327851,327852) [ RunTime:0.000999s ]
[ SQL ] SELECT `group_sub_commodity_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327851,327852) [ RunTime:0.000928s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (39932) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001747s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (39933) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001819s ]
[ SQL ] SELECT min(price) ff_price,`group_sub_commodity_id`,GROUP_CONCAT(group_sub_set_sku_id) sub_sku_ids,`relate_car_ids`,GROUP_CONCAT(id) set_sku_ids FROM `t_db_commodity_set_sku` `a` WHERE `commodity_id` = 10107 AND `is_enable` = 1 AND `id` IN (327851,327852) AND ( (find_in_set(8573,relate_car_ids) || relate_car_ids='') ) GROUP BY group_sub_commodity_id [ RunTime:0.001696s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (39932) AND `stock` > 0 LIMIT 1 [ RunTime:0.000950s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (39933) AND `stock` > 0 LIMIT 1 [ RunTime:0.000995s ]
[ SQL ] SELECT * FROM `t_db_limit_discount_commodity` WHERE `limit_discount_id` = 1735 AND `commodity_id` = 10107 LIMIT 1 [ RunTime:0.001287s ]
[ SQL ] SELECT `group_sub_set_sku_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327626,327842,327850,329746) [ RunTime:0.001078s ]
[ SQL ] SELECT `group_sub_commodity_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327626,327842,327850,329746) [ RunTime:0.001156s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (96097,96098,96099,96100,96101,96102,352146,352147,95884,95885,95886,96076,352144,96122,323616,323617,323618,96049,96051,323603,323604,96056,96057,323606,323607,96003,96004,96005,96006,96007,96008,96009,96010,96011,96012,323595,323596,323597,323598,323599,96059,96060,96061,96062,323609,323610,95950,95951,95952,95953,95954,95955,95956,95957,95958,95959,95960,95962,95963,95964,95965,95966,95967,95968,119839,323585,96066,96067,96068,96069,96070,96071,96072,96112,96113,323623,96018,96019,96020,96021,96022,96023,96024,323602,329480,329481,329482,96033,96034,96035,96037,96038,119856,95901,95902,95903,96048,96073,96074,96075,95905,95906,95907,95908,95909,95910,329472,96040,96041,96042,96043,329483,96047,118525,118526,329488,95912,95913,95914,95915,95916,95917,95918,95919,95920,95921,95922,95923,95924,95926,95927,95928,95929,95932,95933,95934,95935,118522,119838,323624,95943,95944,95948,118523,329473,329474,329475,329476,96386,323625,96387,323626,352143,96382,329484,329485,96383,329489,329490,96384,96388,96389,96390,96385,323629,118515,118516,118517,118518,119837,155317,323594,119845,119846,119847,119848,119849,119850,323586,189118,189119,189120,189124,323587,323588,323589,323590,323591,323592,150307,150308,150310,166052,162587,162588,162604,162605,162606,162607,162608,162609,162610,162611,162612,162613,162614,162615,162616,323620,162590,162591,162592,162593,323622,162572,162573,162574,162575,162576,162577,162578,162580,162581,323582,323583,162619,162620,162621,162622,162597,162598,162599,162600,323619,323621,329492,329493,329494,329495,329496,329497,329498,329499,329500,329501,329502,329503,329504,329505,329506,329507,329508,329509,329510,329511,329512,329513,329514,329515,329523,329524,329525,329526,329527,329528,329529,329530,329531,329532,329533,329534,329535,329536,329537,329538,329539,329540,329554,329555,329557,329558,329559,329560,329561,329562,329563,329564,329565,329566,329567,329572,329573,329574,329575,329576,329577,329578,329579,329587,329588,329589,352142,329609,329610,329612,329613,329614,329615,329616,329618,329619,329620,329621,329622,329623,341869,329625,329641,329642,329643,329644,329645,333781,352145,333846,339882,352141,352140) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.002704s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (142561) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001811s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (39929) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001221s ]
[ SQL ] SELECT min(price) ff_price,`group_sub_commodity_id`,GROUP_CONCAT(group_sub_set_sku_id) sub_sku_ids,`relate_car_ids`,GROUP_CONCAT(id) set_sku_ids FROM `t_db_commodity_set_sku` `a` WHERE `commodity_id` = 10106 AND `is_enable` = 1 AND `id` IN (327626,327842,327850,329746) AND ( (find_in_set(8573,relate_car_ids) || relate_car_ids='') ) GROUP BY group_sub_commodity_id [ RunTime:0.001458s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (329619,323616) AND `stock` > 0 LIMIT 1 [ RunTime:0.001127s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (39929) AND `stock` > 0 LIMIT 1 [ RunTime:0.001024s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (142561) AND `stock` > 0 LIMIT 1 [ RunTime:0.000989s ]
[ SQL ] SELECT * FROM `t_db_limit_discount_commodity` WHERE `limit_discount_id` = 1735 AND `commodity_id` = 10106 LIMIT 1 [ RunTime:0.001205s ]
[ SQL ] SELECT `group_sub_set_sku_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327217,327241,328550,336549) [ RunTime:0.001211s ]
[ SQL ] SELECT `group_sub_commodity_id` FROM `t_db_commodity_set_sku` WHERE `id` IN (327217,327241,328550,336549) [ RunTime:0.001092s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (90646,90647,90648,90649,90650,90651,90652,90653,90654,90655,90656,90657,90658,90659,90661,90662,90663,90665,94890,94891,94892,94893,119795,119796,119797,119798,317832,317833,317834,317835,317836,90741,90742,90744,337477,351950,90680,90681,90682,90683,90684,90685,90686,90687,90688,90689,90690,90691,90693,90696,95000,95001,318501,318502,318503,318504,318505,354799,90772,351951,354804,90713,90714,90726,319272,319273,90712,162568,162569,162570,90715,90716,90717,90718,90719,90722,90723,90727,90728,90729,90730,90731,104639,319337,90791,90677,90678,90630,125701,317642,90631,90632,90633,90634,90635,90636,90637,90638,90639,90640,90641,90643,90644,94870,94871,94872,94873,94874,94875,119793,317609,317610,95052,95053,95054,95055,319466,94981,94982,318245,94984,94985,94986,94987,94988,94989,94990,94991,94992,94993,94994,94995,94996,94997,318309,318310,318311,351944,95042,319401,351946,351947,95006,95010,318634,337476,354800,196389,196390,196391,196392,317799,317800,94944,95059,95061,95063,319530,351949,94974,94975,94976,94977,94979,318212,318213,95018,95020,95022,95023,131701,351945,354801,354802,95033,95034,95038,119807,318766,94912,343292,351936,351937,351938,351939,351940,94962,351943,94966,94861,325246,94936,94863,94864,94865,94867,197382,197383,197384,197385,197386,317589,317590,317591,317592,317593,94967,94968,94969,94970,94971,354785,354786,354787,354788,354789,94831,94838,325243,351904,354757,354758,354759,354760,354761,354762,354763,354764,94897,94898,94902,119802,317868,94937,94938,94939,94940,94942,325255,325256,94880,94884,154904,184028,317736,325254,351928,354775,354776,94887,94888,94846,94847,94848,94849,94850,94851,94852,94853,94854,94855,94856,317553,317554,317555,94877,94879,119794,163434,163435,163437,163438,163439,325247,325248,325249,325250,105708,105709,105710,105711,105712,105713,197408,351927,106364,106367,106368,317570,317573,325236,343299,351912,351913,351914,351915,351916,351917,351918,351919,351920,351921,351922,351923,351924,351925,354772,354773,354774,119812,336493,351952,351953,119806,336555,343293,354792,354793,354794,354795,354796,354797,125758,125759,127095,162467,320107,320108,162560,318830,162561,162562,162563,162564,162565,167621,196372,196373,196374,196375,197409,318569,318570,319852,319788,320236,320237,320238,318118,322764,351897,351898,351899,351900,333847,333848,351929,351930,354778,354779,354780,354781,354782,354783,354784,351905,351906,351907,351908,351909,351910) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.002841s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (142563,182047) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001210s ]
[ SQL ] SELECT * FROM `t_db_commodity_set_sku` WHERE `id` IN (336548) AND `is_enable` = 1 AND `stock` > 0 LIMIT 1 [ RunTime:0.001720s ]
[ SQL ] SELECT min(price) ff_price,`group_sub_commodity_id`,GROUP_CONCAT(group_sub_set_sku_id) sub_sku_ids,`relate_car_ids`,GROUP_CONCAT(id) set_sku_ids FROM `t_db_commodity_set_sku` `a` WHERE `commodity_id` = 10105 AND `is_enable` = 1 AND `id` IN (327217,327241,328550,336549) AND ( (find_in_set(8573,relate_car_ids) || relate_car_ids='') ) GROUP BY group_sub_commodity_id [ RunTime:0.001749s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (319272,125701) AND `stock` > 0 LIMIT 1 [ RunTime:0.001225s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (336548) AND `stock` > 0 LIMIT 1 [ RunTime:0.001048s ]
[ SQL ] SELECT sum(stock) s_stock FROM `t_db_commodity_set_sku` WHERE `id` IN (182047) AND `stock` > 0 LIMIT 1 [ RunTime:0.000827s ]
[ SQL ] SELECT * FROM `t_db_limit_discount_commodity` WHERE `limit_discount_id` = 1735 AND `commodity_id` = 10105 LIMIT 1 [ RunTime:0.001090s ]
[ SQL ] SELECT `commodity_id` FROM `t_db_commodity_set` WHERE ( FIND_IN_SET('GWSM', not_show_dlr) ) [ RunTime:0.024984s ]
[ SQL ] SET SESSION group_concat_max_len=100000; [ RunTime:0.000943s ]
[ SQL ] SELECT COUNT(*) AS tp_count FROM `t_db_commodity_flat` `a` WHERE `a`.`is_enable` = 1 AND ( (find_in_set('GWSM',a.up_down_channel_dlr)) ) AND ( a.commodity_id in (10343,10335,9617,9985,9986,10421,10423,10424,10425,10146,10338,10339) ) LIMIT 1 [ RunTime:0.001123s ]
[ SQL ] SELECT `a`.`commodity_id`,`a`.`commodity_name`,`a`.`tag`,`a`.`tag_gwnet`,`a`.`tag_gwapp`,`a`.`is_pure`,`a`.`cover_image`,`a`.`card_id`,`b`.`count_stock`,`a`.`sales_channel`,`a`.`cheap_dis`,`a`.`group_dis`,`a`.`full_dis`,`a`.`limit_dis`,`a`.`seckill_dis`,`a`.`n_dis`,`a`.`pre_dis`,`a`.`car_series_id`,min(c.price) price,min(c.price) final_price,`b`.`max_point`,`b`.`pay_style`,`a`.`tag_pz1asm`,`a`.`tag_pz1aapp`,`a`.`tag_qcsm`,`a`.`tag_qcapp`,`a`.`is_grouped`,`b`.`commodity_label`,GROUP_CONCAT(c.id) gc_id,GROUP_CONCAT(c.price) gc_price,`b`.`group_commodity_ids_info`,`b`.`is_sp_associated`,`a`.`commodity_set_id`,`b`.`qsc_group`,`b`.`qsc_group_price`,`b`.`first_free_price`,`b`.`qsc_group_num`,`b`.`qsc_group_name`,`a`.`gift_dis`,`a`.`dd_commodity_type`,`b`.`is_store`,`a`.`comm_type_id`,GROUP_CONCAT( c.stock ) gc_stock,`c`.`relate_car_ids`,`b`.`listing_type`,`a`.`activity_image`,`a`.`comm_type_id_str`,`b`.`tag_zdy`,b.mail_type mail_method FROM `t_db_commodity_flat` `a` INNER JOIN `t_db_commodity_set` `b` ON `a`.`commodity_set_id`=b.id and b.is_enable=1 LEFT JOIN `t_db_commodity_card` `card_c` ON `a`.`commodity_id`=card_c.commodity_id and card_c.sorts>0 LEFT JOIN `t_db_card` `cards` ON `card_c`.`card_id`=cards.id and cards.is_enable=1 and cards.validity_date_start > now() and cards.validity_date_end <= NOW() INNER JOIN `t_db_commodity_set_sku` `c` ON `c`.`commodity_set_id`=b.id and c.is_enable=1 INNER JOIN `t_db_commodity_sku` `d` ON `d`.`id`=c.commodity_sku_id and d.is_enable=1 WHERE `a`.`is_enable` = 1 AND `c`.`is_enable` = 1 AND ( (find_in_set('GWSM',a.up_down_channel_dlr)) ) AND ( a.commodity_id in (10343,10335,9617,9985,9986,10421,10423,10424,10425,10146,10338,10339) ) AND ( (find_in_set('A',d.city_type) || d.city_type='' || d.city_type is null ) ) AND ( (find_in_set('H2901',d.relate_dlr_code) || d.relate_dlr_code='' || d.relate_dlr_code is null ) ) AND ( d.oli_liters in (5) || d.oli_liters ='' ) AND ( ((d.maintain_q='8.0' and a.dd_commodity_type=3 ) || (d.maintain_q='7.0' and a.dd_commodity_type=12 ) || a.dd_commodity_type not in (3,12) ) ) AND ( ((find_in_set(8573,d.relate_car_ids) || d.relate_car_ids='' || d.relate_car_ids is null) ) ) AND ( ((d.part_start_time<='2021/08' || d.part_start_time ='') and (d.part_end_time>='2021/08' || d.part_end_time ='') ) ) AND ( ( (d.maintain_num<=0 || d.maintain_num='') || a.dd_commodity_type <> 4 ) ) AND ( ((b.listing_type=2 and a.crowdfund_dis like '%GWSM%' )|| b.listing_type=1) ) GROUP BY a.commodity_id ORDER BY field(a.commodity_id, 10343,10335,9617,9985,9986,10421,10423,10424,10425,10146,10338,10339),card_c.sorts desc,a.last_updated_date asc LIMIT 0,12 [ RunTime:0.014756s ]
[ SQL ] SELECT `a`.`id`,`a`.`sp_value_list`,`b`.`price`,`b`.`id`,b.id bid,`a`.`sku_code`,`a`.`variety_code`,`a`.`maintain_q`,`b`.`commodity_id` FROM `t_db_commodity_sku` `a` INNER JOIN `t_db_commodity_set_sku` `b` ON `a`.`id`=b.commodity_sku_id and a.is_enable=1 and b.is_enable=1 WHERE `b`.`id` IN ('342253','341187','194249','320814','349348','349363','349859','349874','350370','350385','350881','350896','339899','343272','342514') GROUP BY b.commodity_id,a.id ORDER BY b.price DESC,a.id DESC,b.id DESC [ RunTime:0.002414s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1202 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001709s ]
[ SQL ] SELECT * FROM `t_db_seckill_commodity` WHERE `seckill_id` = 1202 AND `commodity_id` = 10343 LIMIT 1 [ RunTime:0.001388s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1363 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001276s ]
[ SQL ] SELECT * FROM `t_db_seckill_commodity` WHERE `seckill_id` = 1363 AND `commodity_id` = 10335 LIMIT 1 [ RunTime:0.001098s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1206 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001466s ]
[ SQL ] SELECT * FROM `t_db_seckill_commodity` WHERE `seckill_id` = 1206 AND `commodity_id` = 10146 LIMIT 1 [ RunTime:0.001262s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1207 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001422s ]
[ SQL ] SELECT * FROM `t_db_seckill_commodity` WHERE `seckill_id` = 1207 AND `commodity_id` = 10338 LIMIT 1 [ RunTime:0.001127s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1208 AND ( user_segment=0 or (user_segment=1 and FIND_IN_SET('PLATINUM_CARD',user_segment_options)) or (user_segment=2 and FIND_IN_SET('N',user_segment_options)) ) LIMIT 1 [ RunTime:0.001694s ]
[ SQL ] SELECT * FROM `t_db_seckill_commodity` WHERE `seckill_id` = 1208 AND `commodity_id` = 10339 LIMIT 1 [ RunTime:0.001373s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1338 LIMIT 1 [ RunTime:0.001558s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1202 LIMIT 1 [ RunTime:0.001336s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1348 LIMIT 1 [ RunTime:0.001378s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1341 LIMIT 1 [ RunTime:0.001481s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1342 LIMIT 1 [ RunTime:0.001472s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1343 LIMIT 1 [ RunTime:0.001304s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1335 LIMIT 1 [ RunTime:0.001239s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1336 LIMIT 1 [ RunTime:0.001243s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1337 LIMIT 1 [ RunTime:0.001436s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1338 LIMIT 1 [ RunTime:0.001352s ]
[ SQL ] SELECT * FROM `t_db_limit_discount` WHERE `id` = 1775 AND `is_enable` = 1 AND `act_status` = 2 LIMIT 1 [ RunTime:0.001367s ]
[ SQL ] SELECT * FROM `t_db_limit_discount` WHERE `id` = 1775 AND `is_enable` = 1 AND `act_status` = 2 LIMIT 1 [ RunTime:0.001334s ]
[ SQL ] SELECT * FROM `t_db_limit_discount` WHERE `id` = 1775 AND `is_enable` = 1 AND `act_status` = 2 LIMIT 1 [ RunTime:0.001098s ]
[ SQL ] SELECT * FROM `t_db_limit_discount` WHERE `id` = 1775 AND `is_enable` = 1 AND `act_status` = 2 LIMIT 1 [ RunTime:0.001149s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1208 LIMIT 1 [ RunTime:0.001076s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1206 LIMIT 1 [ RunTime:0.001067s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1207 LIMIT 1 [ RunTime:0.000997s ]
[ SQL ] SELECT * FROM `t_db_seckill` WHERE `id` = 1208 LIMIT 1 [ RunTime:0.000982s ]